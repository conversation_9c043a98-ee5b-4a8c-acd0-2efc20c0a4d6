import { useState, useRef } from "react";
import { 
  Image, Video, Music, FileText, Upload, X, Send, 
  Loader2, AlertCircle, CheckCircle 
} from "lucide-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";

interface MediaUploadProps {
  userId: string;
  contactPhone: string;
  onClose: () => void;
  isOpen: boolean;
}

interface UploadFile {
  file: File;
  preview?: string;
  type: 'image' | 'video' | 'audio' | 'document';
  id: string;
}

export default function MediaUpload({ userId, contactPhone, onClose, isOpen }: MediaUploadProps) {
  const [selectedFiles, setSelectedFiles] = useState<UploadFile[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const uploadMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('media', file);
      formData.append('userId', userId);
      formData.append('to', contactPhone);

      const response = await fetch('/api/whatsapp/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate messages cache to show new message
      queryClient.invalidateQueries({ 
        queryKey: ['/api/whatsapp/messages', userId, contactPhone] 
      });
      toast({
        title: "Arquivo enviado",
        description: "Sua mídia foi enviada com sucesso",
      });
    },
    onError: () => {
      toast({
        title: "Erro no upload",
        description: "Não foi possível enviar o arquivo",
        variant: "destructive",
      });
    },
  });

  const getFileType = (file: File): 'image' | 'video' | 'audio' | 'document' => {
    const type = file.type.split('/')[0];
    if (['image', 'video', 'audio'].includes(type)) {
      return type as 'image' | 'video' | 'audio';
    }
    return 'document';
  };

  const createPreview = (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.readAsDataURL(file);
    });
  };

  const handleFileSelect = async (files: FileList) => {
    const newFiles: UploadFile[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const type = getFileType(file);
      const id = Date.now() + '-' + i;

      let preview: string | undefined;
      if (type === 'image') {
        preview = await createPreview(file);
      }

      newFiles.push({
        file,
        preview,
        type,
        id,
      });
    }

    setSelectedFiles(prev => [...prev, ...newFiles]);
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  const removeFile = (id: string) => {
    setSelectedFiles(prev => prev.filter(f => f.id !== id));
  };

  const sendFiles = async () => {
    for (const uploadFile of selectedFiles) {
      await uploadMutation.mutateAsync(uploadFile.file);
    }
    setSelectedFiles([]);
    onClose();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return <Image className="w-6 h-6 text-pink-500" />;
      case 'video': return <Video className="w-6 h-6 text-purple-500" />;
      case 'audio': return <Music className="w-6 h-6 text-orange-500" />;
      default: return <FileText className="w-6 h-6 text-blue-500" />;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold">Enviar Mídia</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          {/* Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragActive 
                ? 'border-green-500 bg-green-50' 
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">
              Arraste arquivos aqui ou clique para selecionar
            </p>
            <p className="text-sm text-gray-500 mb-4">
              Suporte: Imagens, vídeos, áudios e documentos (máx. 50MB)
            </p>
            <button
              onClick={() => fileInputRef.current?.click()}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
            >
              Selecionar Arquivos
            </button>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
              onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
              className="hidden"
            />
          </div>

          {/* Selected Files */}
          {selectedFiles.length > 0 && (
            <div className="mt-6">
              <h3 className="font-medium text-gray-900 mb-3">
                Arquivos Selecionados ({selectedFiles.length})
              </h3>
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {selectedFiles.map((uploadFile) => (
                  <div
                    key={uploadFile.id}
                    className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
                  >
                    {/* Preview or Icon */}
                    <div className="flex-shrink-0">
                      {uploadFile.preview ? (
                        <img
                          src={uploadFile.preview}
                          alt={uploadFile.file.name}
                          className="w-12 h-12 object-cover rounded"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
                          {getFileIcon(uploadFile.type)}
                        </div>
                      )}
                    </div>

                    {/* File Info */}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {uploadFile.file.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(uploadFile.file.size)} • {uploadFile.type}
                      </p>
                    </div>

                    {/* Remove Button */}
                    <button
                      onClick={() => removeFile(uploadFile.id)}
                      className="p-1 hover:bg-gray-200 rounded-full"
                    >
                      <X className="w-4 h-4 text-gray-500" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-4 flex items-center justify-between">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            {uploadMutation.isPending && (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Enviando...</span>
              </>
            )}
            {uploadMutation.isSuccess && (
              <>
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span>Enviado com sucesso!</span>
              </>
            )}
            {uploadMutation.isError && (
              <>
                <AlertCircle className="w-4 h-4 text-red-600" />
                <span>Erro no envio</span>
              </>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Cancelar
            </button>
            <button
              onClick={sendFiles}
              disabled={selectedFiles.length === 0 || uploadMutation.isPending}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {uploadMutation.isPending ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
              <span>Enviar {selectedFiles.length > 0 && `(${selectedFiles.length})`}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}