import { useState } from "react";
import { ArrowLeft, MessageSquare, Users, Smartphone, RefreshCw, Play, Image, Video, Music, FileText, Send } from "lucide-react";
import { useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";

export default function SistemaReal() {
  const [, setLocation] = useLocation();
  const [selectedChat, setSelectedChat] = useState<string>("");

  const currentUserId = 'user_1749665821961';

  // Status da conexão WhatsApp
  const { data: whatsappStatus, refetch: refetchStatus } = useQuery({
    queryKey: ['/api/whatsapp/status', currentUserId],
    queryFn: async () => {
      const response = await fetch(`/api/whatsapp/status?userId=${currentUserId}`);
      return response.json();
    },
    refetchInterval: 2000,
  });

  // Conversas reais do WhatsApp
  const { data: chats = [], refetch: refetchChats } = useQuery({
    queryKey: ['/api/whatsapp/chats', currentUserId],
    queryFn: async () => {
      const response = await fetch(`/api/whatsapp/chats?userId=${currentUserId}`);
      return response.json();
    },
    enabled: whatsappStatus?.connected,
    refetchInterval: 5000,
  });

  // Mensagens da conversa selecionada with paginated structure
  const { data: messagesData } = useQuery({
    queryKey: ['/api/whatsapp/messages', currentUserId, selectedChat],
    queryFn: async () => {
      if (!selectedChat) return { data: [], total: 0, offset: 0 };
      const response = await fetch(`/api/whatsapp/messages?userId=${currentUserId}&chatId=${selectedChat}&limit=50`);
      const result = await response.json();
      
      // Handle both array and paginated responses
      if (Array.isArray(result)) {
        return { data: result, total: result.length, offset: 0 };
      }
      return result.data ? result : { data: result, total: Array.isArray(result) ? result.length : 0, offset: 0 };
    },
    enabled: !!selectedChat && whatsappStatus?.connected,
    refetchInterval: 1000, // Atualização mais rápida para tempo real
  });

  // Extract messages array from paginated structure with proper fallback
  const messages = Array.isArray(messagesData?.data) ? messagesData.data : 
                   Array.isArray(messagesData) ? messagesData : [];

  const handleInitializeWhatsApp = async () => {
    try {
      await fetch('/api/whatsapp/initialize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: currentUserId }),
      });
      refetchStatus();
    } catch (error) {
      console.error('Error initializing WhatsApp:', error);
    }
  };

  const isConnected = whatsappStatus?.connected;
  const phoneNumber = whatsappStatus?.phoneNumber;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setLocation('/')}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </button>
          <div className="flex-1">
            <h1 className="text-xl font-semibold text-gray-900">
              Sistema WhatsApp CRM - Apenas Dados Reais
            </h1>
            <p className="text-sm text-gray-500">
              {isConnected 
                ? `WhatsApp conectado: ${phoneNumber} • ${chats.length} conversas carregadas`
                : 'WhatsApp desconectado - conecte para ver dados reais'
              }
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-sm font-medium">
              {isConnected ? 'Conectado' : 'Desconectado'}
            </span>
          </div>
        </div>
      </div>

      <div className="flex" style={{ height: 'calc(100vh - 64px)' }}>
        {/* Lista de Conversas */}
        <div className="w-1/3 bg-white border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200 flex-shrink-0">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold">Conversas Reais</h2>
              <button
                onClick={() => refetchChats()}
                className="p-2 hover:bg-gray-100 rounded-full"
                title="Atualizar conversas"
              >
                <RefreshCw className="w-4 h-4" />
              </button>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              {chats.length} conversas da sua conta WhatsApp
            </p>
          </div>

          <div className="flex-1 overflow-y-auto">
            {!isConnected ? (
              <div className="p-6 text-center">
                <Smartphone className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Conecte seu WhatsApp
                </h3>
                <p className="text-gray-600 mb-4">
                  Para ver suas conversas reais, conecte sua conta WhatsApp
                </p>
                <button
                  onClick={handleInitializeWhatsApp}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center mx-auto"
                >
                  <Play className="w-4 h-4 mr-2" />
                  Conectar WhatsApp
                </button>
              </div>
            ) : chats.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
                Carregando conversas reais...
              </div>
            ) : (
              chats.map((chat: any) => (
                <div
                  key={chat.id}
                  onClick={() => setSelectedChat(chat.id)}
                  className={`p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors ${
                    selectedChat === chat.id ? 'bg-green-50 border-green-200' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                      {chat.isGroup ? '👥' : '👤'}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-gray-900 truncate">{chat.name}</h4>
                        {chat.unreadCount > 0 && (
                          <div className="bg-green-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center flex-shrink-0">
                            {chat.unreadCount}
                          </div>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 truncate">
                        {chat.lastMessage?.body || 'Sem mensagens recentes'}
                      </p>
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-xs text-gray-500">
                          {chat.isGroup ? `Grupo • ${chat.groupMetadata?.participants || 0} participantes` : 'Contato pessoal'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Área de Mensagens */}
        <div className="flex-1 flex flex-col">
          {selectedChat ? (
            <>
              {/* Header da conversa */}
              <div className="bg-gray-100 px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      {chats.find((c: any) => c.id === selectedChat)?.name}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {chats.find((c: any) => c.id === selectedChat)?.isGroup ? 'Grupo' : 'Conversa individual'}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setLocation('/whatsapp')}
                      className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                    >
                      Abrir no WhatsApp Web
                    </button>
                  </div>
                </div>
              </div>

              {/* Mensagens */}
              <div className="flex-1 overflow-y-auto px-6 py-4 bg-gray-50">
                {messages.length === 0 ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center text-gray-500">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
                      <p>Carregando mensagens reais...</p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3 pb-4">
                    {messages.map((message: any) => (
                      <div
                        key={message.id}
                        className={`flex ${message.fromMe ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                            message.fromMe
                              ? 'bg-green-500 text-white rounded-br-none'
                              : 'bg-white text-gray-900 rounded-bl-none shadow-sm border'
                          }`}
                        >
                          {/* Media Content */}
                          {message.hasMedia && (
                            <div className="mb-2">
                              {message.type === 'image' && (
                                <div className="flex items-center p-2 bg-black bg-opacity-10 rounded-lg">
                                  <Image className="w-6 h-6 text-pink-500 mr-2" />
                                  <span className="text-sm">📷 Imagem</span>
                                </div>
                              )}
                              {message.type === 'video' && (
                                <div className="flex items-center p-2 bg-black bg-opacity-10 rounded-lg">
                                  <Video className="w-6 h-6 text-purple-500 mr-2" />
                                  <span className="text-sm">🎥 Vídeo</span>
                                </div>
                              )}
                              {message.type === 'audio' && (
                                <div className="flex items-center p-2 bg-black bg-opacity-10 rounded-lg">
                                  <Music className="w-6 h-6 text-orange-500 mr-2" />
                                  <span className="text-sm">🎵 Áudio</span>
                                </div>
                              )}
                              {message.type === 'document' && (
                                <div className="flex items-center p-2 bg-black bg-opacity-10 rounded-lg">
                                  <FileText className="w-6 h-6 text-blue-500 mr-2" />
                                  <span className="text-sm">📄 Documento</span>
                                </div>
                              )}
                            </div>
                          )}
                          
                          {/* Text Content */}
                          {message.body && (
                            <div className="break-words">
                              {message.body}
                            </div>
                          )}
                          
                          <div className={`text-xs mt-1 ${
                            message.fromMe ? 'text-green-100' : 'text-gray-500'
                          }`}>
                            {new Date(message.timestamp).toLocaleTimeString('pt-BR', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Área de envio */}
              <div className="bg-gray-100 px-6 py-4 border-t border-gray-200">
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    placeholder="Para enviar mensagens, use a interface completa"
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-full bg-gray-50"
                    disabled
                  />
                  <button
                    onClick={() => setLocation('/whatsapp')}
                    className="bg-green-600 text-white px-4 py-2 rounded-full hover:bg-green-700 transition-colors"
                  >
                    <MessageSquare className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center text-gray-500">
                <MessageSquare className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Selecione uma conversa
                </h3>
                <p className="text-gray-600">
                  Escolha uma conversa à esquerda para ver as mensagens reais
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer informativo */}
      <div className="bg-green-50 border-t border-green-200 px-6 py-3">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <p className="text-sm text-green-700">
            <strong>Sistema 100% Real:</strong> Todas as conversas, mensagens e contatos 
            são carregados diretamente da sua conta WhatsApp conectada. 
            Sem dados fake ou simulações.
          </p>
        </div>
      </div>
    </div>
  );
}