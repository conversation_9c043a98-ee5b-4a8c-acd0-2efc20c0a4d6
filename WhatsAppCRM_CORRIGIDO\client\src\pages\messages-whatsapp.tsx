import { useState, useRef, useEffect, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { MessageSquare, ArrowLeft, Search, MoreVertical, Send, Mic, Smile, Paperclip } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useWebSocket } from "@/hooks/use-websocket";
import { apiRequest } from "@/lib/queryClient";
import { formatTime, formatPhone, getInitials } from "@/lib/utils";
import { useLocation } from "wouter";

interface WhatsAppMessage {
  id: string;
  body: string;
  content?: string;
  timestamp: number;
  fromMe: boolean;
  from: string;
  to: string;
  ack?: number; // 0: pending, 1: sent, 2: delivered, 3: read
  type?: string;
  hasMedia?: boolean;
  mediaType?: string;
  mediaUrl?: string;
  mediaName?: string;
  mediaMimeType?: string;
}

interface WhatsAppChat {
  id: string;
  name: string;
  phone: string;
  lastMessage?: {
    body: string;
    content?: string;
    timestamp: number;
    fromMe: boolean;
  };
  unreadCount: number;
  isOnline: boolean;
  profilePicUrl?: string;
}

export default function WhatsAppMessages() {
  const [selectedClient, setSelectedClient] = useState<string>("");
  const [messageInput, setMessageInput] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [, setLocation] = useLocation();
  
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const userId = localStorage.getItem('whatsapp_user_id') || 'default';

  // WhatsApp status and connection
  const { data: whatsappStatus, isLoading: statusLoading } = useQuery({
    queryKey: ["/api/whatsapp/status", userId],
    queryFn: async () => {
      const response = await fetch(`/api/whatsapp/status?userId=${encodeURIComponent(userId)}`);
      if (!response.ok) throw new Error('Failed to check status');
      return response.json();
    },
    refetchInterval: 3000,
  });

  const isConnected = (whatsappStatus as any)?.connected || false;
  const currentPhoneNumber = (whatsappStatus as any)?.phoneNumber || "";

  // Show success toast when connection is established (only once)
  const [hasShownSuccessToast, setHasShownSuccessToast] = useState(false);
  const [lastConnectionState, setLastConnectionState] = useState<boolean>(false);
  
  useEffect(() => {
    // If connection status changed from false to true, force a refresh
    if (isConnected && !lastConnectionState) {
      console.log('WhatsApp connection established, refreshing data...');
      queryClient.invalidateQueries({ queryKey: ["/api/whatsapp/chats"] });
      queryClient.refetchQueries({ queryKey: ["/api/whatsapp/chats"] });
      
      if (!hasShownSuccessToast) {
        toast({
          title: "WhatsApp conectado com sucesso",
          description: `Conectado como ${currentPhoneNumber}`,
        });
        setHasShownSuccessToast(true);
      }
    }
    setLastConnectionState(isConnected);
  }, [isConnected, lastConnectionState, currentPhoneNumber, hasShownSuccessToast, toast, queryClient]);

  // Initialize WhatsApp connection
  const initializeConnection = async () => {
    try {
      // Check current status first
      const statusResponse = await fetch('/api/whatsapp/status');
      const statusData = await statusResponse.json();
      
      if (statusData.connected) {
        // Already connected, refresh the page to show data
        window.location.reload();
        return;
      }

      // If not connected, proceed with QR initialization
      const response = await fetch(`/api/whatsapp/qr?userId=${encodeURIComponent(userId)}`);
      const data = await response.json();
      
      if (data.connected) {
        window.location.reload();
      } else {
        setLocation('/qr-code');
      }
    } catch (error) {
      toast({
        title: "Erro",
        description: "Falha ao inicializar conexão WhatsApp",
        variant: "destructive",
      });
    }
  };

  // WebSocket for real-time updates - disabled temporarily to fix connection errors
  // const { isConnected: wsConnected } = useWebSocket(
  //   isConnected ? `ws://${window.location.hostname}:5001` : '',
  //   {
  //     onMessage: (message) => {
  //       if (message.type === 'whatsapp_message' && message.data) {
  //         handleNewMessage(message.data);
  //       }
  //     },
  //     onConnectionChange: (connected) => {
  //       console.log('WebSocket:', connected ? 'connected' : 'disconnected');
  //     }
  //   }
  // );

  // Handle new incoming messages
  const handleNewMessage = useCallback((messageData: WhatsAppMessage) => {
    const chatId = messageData.fromMe ? messageData.to : messageData.from;
    
    // Update chat list with new last message and reorder
    queryClient.setQueryData(["/api/whatsapp/chats", userId], (oldChats: WhatsAppChat[] = []) => {
      const updatedChats = oldChats.map(chat => {
        if (chat.id === chatId) {
          return {
            ...chat,
            lastMessage: {
              body: messageData.body || messageData.content || '',
              timestamp: messageData.timestamp,
              fromMe: messageData.fromMe
            },
            unreadCount: messageData.fromMe ? chat.unreadCount : (chat.unreadCount || 0) + 1
          };
        }
        return chat;
      });

      // Sort by last message timestamp
      return updatedChats.sort((a, b) => {
        const aTime = a.lastMessage?.timestamp || 0;
        const bTime = b.lastMessage?.timestamp || 0;
        return bTime - aTime;
      });
    });

    // Update messages for current chat
    if (selectedClient === chatId) {
      queryClient.setQueryData(["/api/whatsapp/chats", chatId, "messages"], (oldMessages: WhatsAppMessage[] = []) => {
        // Avoid duplicates
        const exists = oldMessages.some(msg => msg.id === messageData.id);
        if (exists) return oldMessages;

        const newMessages = [...oldMessages, messageData].sort((a, b) => a.timestamp - b.timestamp);
        
        // Auto-scroll to bottom
        setTimeout(() => {
          messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
        }, 100);

        return newMessages;
      });
    }
  }, [selectedClient, queryClient, userId]);

  // Fetch chats with improved persistence and error handling
  const { data: chats = [], isLoading: chatsLoading, error: chatsError, refetch: refetchChats } = useQuery({
    queryKey: ["/api/whatsapp/chats", userId],
    queryFn: async () => {
      if (!isConnected) return [];
      const response = await fetch(`/api/whatsapp/chats?userId=${encodeURIComponent(userId)}`);
      if (!response.ok) throw new Error('Failed to fetch chats');
      return response.json();
    },
    enabled: isConnected && !statusLoading,
    refetchInterval: 60000, // Reduce frequency to prevent data loss
    retry: 3,
    staleTime: 30000, // Keep data fresh for 30 seconds
    gcTime: 300000, // Keep in cache for 5 minutes
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
  });

  // Fetch messages for selected chat with paginated structure
  const { data: messagesData, isLoading: messagesLoading, refetch: refetchMessages } = useQuery({
    queryKey: ["/api/whatsapp/chats", selectedClient, "messages", userId],
    queryFn: async () => {
      if (!selectedClient) return { data: [], total: 0, offset: 0 };
      console.log('Fetching complete message history for:', selectedClient);
      const response = await fetch(`/api/whatsapp/chats/${selectedClient}/messages?limit=100&userId=${encodeURIComponent(userId)}`);
      if (!response.ok) throw new Error('Failed to fetch messages');
      const result = await response.json();
      console.log('Received complete history:', Array.isArray(result) ? result.length : result.data?.length || 0, 'messages');
      
      // Handle both array and paginated responses
      if (Array.isArray(result)) {
        const sortedData = result.sort((a: WhatsAppMessage, b: WhatsAppMessage) => 
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
        );
        return { data: sortedData, total: sortedData.length, offset: 0 };
      }
      
      // If already paginated, sort the data array
      if (result.data) {
        const sortedData = result.data.sort((a: WhatsAppMessage, b: WhatsAppMessage) => 
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
        );
        return { ...result, data: sortedData };
      }
      
      return { data: [], total: 0, offset: 0 };
    },
    enabled: !!selectedClient && isConnected,
    staleTime: 30000, // Cache for 30 seconds
    gcTime: 60000, // Keep in memory for 1 minute
  });

  // Extract messages array from paginated structure with proper fallback
  const messages = Array.isArray(messagesData?.data) ? messagesData.data : 
                   Array.isArray(messagesData) ? messagesData : [];

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: async (messageText: string) => {
      return apiRequest("POST", `/api/messages/send`, {
        userId: userId,
        clientPhone: selectedClient,
        content: messageText
      });
    },
    onMutate: async (messageText) => {
      // Optimistic update - show message immediately
      const optimisticMessage: WhatsAppMessage = {
        id: `temp-${Date.now()}`,
        body: messageText,
        timestamp: Date.now(),
        fromMe: true,
        from: currentPhoneNumber,
        to: selectedClient,
        ack: 0 // pending
      };

      queryClient.setQueryData(["/api/whatsapp/chats", selectedClient, "messages"], 
        (old: WhatsAppMessage[] = []) => [...old, optimisticMessage]
      );

      // Auto-scroll
      setTimeout(() => messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' }), 50);
      
      return { optimisticMessage };
    },
    onSuccess: async (data, messageText, context) => {
      // Parse response to get message ID
      const result = await data.json();
      
      // Replace optimistic message with real one
      queryClient.setQueryData(["/api/whatsapp/chats", selectedClient, "messages"], 
        (old: WhatsAppMessage[] = []) => {
          return old.map(msg => 
            msg.id === context?.optimisticMessage.id 
              ? { ...msg, id: result.id || msg.id, ack: 1 }
              : msg
          );
        }
      );

      // Update chat list
      queryClient.setQueryData(["/api/whatsapp/chats"], (oldChats: WhatsAppChat[] = []) => {
        return oldChats.map(chat => {
          if (chat.id === selectedClient) {
            return {
              ...chat,
              lastMessage: {
                body: messageText,
                timestamp: Date.now(),
                fromMe: true
              }
            };
          }
          return chat;
        }).sort((a, b) => (b.lastMessage?.timestamp || 0) - (a.lastMessage?.timestamp || 0));
      });

      toast({
        title: "Mensagem enviada",
        description: "Sua mensagem foi enviada com sucesso",
      });
    },
    onError: (error, messageText, context) => {
      // Remove optimistic message on error
      queryClient.setQueryData(["/api/whatsapp/chats", selectedClient, "messages"], 
        (old: WhatsAppMessage[] = []) => old.filter(msg => msg.id !== context?.optimisticMessage.id)
      );

      toast({
        title: "Erro ao enviar mensagem",
        description: "Tente novamente",
        variant: "destructive",
      });
    }
  });

  // Handle send message
  const handleSendMessage = () => {
    const text = messageInput.trim();
    if (!text || !selectedClient || sendMessageMutation.isPending) return;

    sendMessageMutation.mutate(text);
    setMessageInput("");
    if (inputRef.current) inputRef.current.focus();
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Auto-scroll when messages change and refresh on client change
  useEffect(() => {
    console.log('Messages effect triggered:', messages.length, 'messages for client:', selectedClient);
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, selectedClient]);

  // Refresh messages when selecting a new client
  useEffect(() => {
    console.log('Client selection effect:', selectedClient, 'isConnected:', isConnected);
    if (selectedClient && isConnected) {
      console.log('Triggering message refetch for new client');
      refetchMessages();
    }
  }, [selectedClient, isConnected, refetchMessages]);

  // Filter and deduplicate chats based on search
  const filteredChats = chats
    .filter((chat: WhatsAppChat, index: number, self: WhatsAppChat[]) => 
      // Remove duplicates by ID
      self.findIndex(c => c.id === chat.id) === index
    )
    .filter((chat: WhatsAppChat) => 
      !searchQuery || 
      chat.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      chat.phone?.includes(searchQuery) ||
      chat.lastMessage?.body?.toLowerCase().includes(searchQuery.toLowerCase())
    );

  // Get current chat data
  const currentChat = chats.find((chat: WhatsAppChat) => chat.id === selectedClient);

  // Render message status icons
  const renderMessageStatus = (ack: number) => {
    switch (ack) {
      case 1:
        return (
          <svg viewBox="0 0 16 15" width="16" height="15" className="text-[#667781]">
            <path fill="currentColor" d="M10.91 3.316l-.478-.372a.365.365 0 0 0-.51.063L4.566 9.879a.32.32 0 0 1-.484.033L1.891 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l3.258 3.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"/>
          </svg>
        );
      case 2:
        return (
          <svg viewBox="0 0 16 15" width="16" height="15" className="text-[#667781]">
            <path fill="currentColor" d="M15.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L8.666 9.879a.32.32 0 0 1-.484.033l-1.91-1.909a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l2.258 2.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"/>
            <path fill="currentColor" d="M12.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L5.666 9.879a.32.32 0 0 1-.484.033L3.191 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l2.258 2.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"/>
          </svg>
        );
      case 3:
        return (
          <svg viewBox="0 0 16 15" width="16" height="15" className="text-[#53bdeb]">
            <path fill="currentColor" d="M15.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L8.666 9.879a.32.32 0 0 1-.484.033l-1.91-1.909a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l2.258 2.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"/>
            <path fill="currentColor" d="M12.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L5.666 9.879a.32.32 0 0 1-.484.033L3.191 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l2.258 2.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"/>
          </svg>
        );
      default:
        return (
          <div className="w-3 h-3 border border-[#667781] border-t-transparent rounded-full animate-spin"></div>
        );
    }
  };

  if (!isConnected) {
    return (
      <div className="h-screen flex items-center justify-center bg-[#f0f2f5]">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-[#00a884] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <h3 className="text-xl font-medium text-[#111b21] mb-2">
            Conectando ao WhatsApp...
          </h3>
          <p className="text-[#667781]">
            Aguarde enquanto estabelecemos a conexão
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex bg-[#f0f2f5]">
      {/* Sidebar */}
      <div className="w-[400px] bg-white flex flex-col border-r border-[#d1d7db]">
        {/* Header */}
        <div className="bg-[#f0f2f5] h-[59px] px-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <button
              onClick={() => setLocation('/')}
              className="w-10 h-10 hover:bg-[#f5f6f6] rounded-full transition-colors flex items-center justify-center"
              title="Voltar ao Dashboard"
            >
              <ArrowLeft className="w-5 h-5 text-[#54656f]" />
            </button>
            
            <div className="w-10 h-10 bg-[#6bcf7f] rounded-full flex items-center justify-center">
              <span className="text-white font-medium text-lg">
                {currentPhoneNumber ? currentPhoneNumber.slice(-2) : 'WA'}
              </span>
            </div>
          </div>
          
          <div className="flex items-center gap-[10px]">
            <button 
              onClick={() => refetchChats()}
              className="w-10 h-10 hover:bg-[#f5f6f6] rounded-full transition-colors flex items-center justify-center"
              title="Atualizar contatos"
            >
              <svg viewBox="0 0 24 24" width="20" height="20" className="text-[#54656f]">
                <path fill="currentColor" d="M12 20.664a9.163 9.163 0 0 1-6.521-2.702.977.977 0 0 1 0-1.393.982.982 0 0 1 1.393 0A7.197 7.197 0 0 0 12 18.518a7.197 7.197 0 0 0 5.128-2.049.982.982 0 0 1 1.393 0 .977.977 0 0 1 0 1.393A9.163 9.163 0 0 1 12 20.664z"/>
              </svg>
            </button>
            
            <button 
              onClick={async () => {
                if (confirm('Desconectar WhatsApp? Você precisará escanear o QR Code novamente.')) {
                  try {
                    await fetch('/api/whatsapp/disconnect', { method: 'POST' });
                    toast({
                      title: "WhatsApp desconectado",
                      description: "Redirecionando para nova conexão...",
                    });
                    setTimeout(() => setLocation('/qr-code'), 1500);
                  } catch (error) {
                    toast({
                      title: "Erro",
                      description: "Falha ao desconectar WhatsApp",
                      variant: "destructive",
                    });
                  }
                }
              }}
              className="w-10 h-10 hover:bg-[#f5f6f6] rounded-full transition-colors flex items-center justify-center"
              title="Desconectar WhatsApp"
            >
              <svg viewBox="0 0 24 24" width="20" height="20" className="text-[#54656f]">
                <path fill="currentColor" d="M19.005 3.175H4.674C3.642 3.175 3 3.789 3 4.821V21.02l3.544-3.514h12.461c1.033 0 2.064-1.06 2.064-2.093V4.821c-.001-1.032-1.032-1.646-2.064-1.646z"/>
              </svg>
            </button>
            
            <button className="w-10 h-10 hover:bg-[#f5f6f6] rounded-full transition-colors flex items-center justify-center">
              <MoreVertical className="w-5 h-5 text-[#54656f]" />
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="px-3 py-[9px] bg-white">
          <div className="relative">
            <Search className="absolute left-[13px] top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#8696a0]" />
            <input
              type="text"
              placeholder="Buscar ou começar uma nova conversa"
              className="w-full bg-[#f0f2f5] border-none rounded-lg py-[7px] pl-[40px] pr-[12px] text-[14px] text-[#111b21] placeholder-[#8696a0] focus:outline-none"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        {/* Chat List */}
        <div className="flex-1 overflow-y-auto bg-white">
          {statusLoading ? (
            <div className="p-6 text-center">
              <div className="w-8 h-8 border-2 border-[#00a884] border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
              <p className="text-[#8696a0] text-sm">Verificando conexão...</p>
            </div>
          ) : !isConnected ? (
            <div className="p-6 text-center">
              <MessageSquare className="w-12 h-12 mx-auto mb-3 text-[#8696a0]" />
              <p className="text-[#8696a0] text-sm">WhatsApp não conectado</p>
              <button
                onClick={initializeConnection}
                className="mt-3 bg-[#00a884] text-white px-4 py-2 rounded-lg text-sm hover:bg-[#008f72] transition-colors"
              >
                Conectar WhatsApp
              </button>
            </div>
          ) : chatsLoading ? (
            <div className="p-6 text-center">
              <div className="w-8 h-8 border-2 border-[#00a884] border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
              <p className="text-[#8696a0] text-sm">Carregando conversas...</p>
            </div>
          ) : chatsError ? (
            <div className="p-6 text-center">
              <MessageSquare className="w-12 h-12 mx-auto mb-3 text-[#8696a0]" />
              <p className="text-[#8696a0] text-sm">Erro ao carregar conversas</p>
              <button
                onClick={() => window.location.reload()}
                className="mt-3 bg-[#00a884] text-white px-4 py-2 rounded-lg text-sm hover:bg-[#008f72] transition-colors"
              >
                Tentar novamente
              </button>
            </div>
          ) : filteredChats.length === 0 ? (
            <div className="p-6 text-center">
              <MessageSquare className="w-12 h-12 mx-auto mb-3 text-[#8696a0]" />
              <p className="text-[#8696a0] text-sm">
                {searchQuery ? 'Nenhuma conversa encontrada' : 'Nenhuma conversa disponível'}
              </p>
            </div>
          ) : (
            <div>
              {filteredChats.map((chat: WhatsAppChat, index: number) => (
                <div
                  key={`${chat.id}-${index}`}
                  onClick={() => setSelectedClient(chat.id)}
                  className={`px-[15px] py-[12px] cursor-pointer border-b border-[#e9edef] hover:bg-[#f5f6f6] ${
                    selectedClient === chat.id ? 'bg-[#f0f2f5]' : ''
                  }`}
                >
                  <div className="flex items-center space-x-[15px]">
                    <div className="relative">
                      {chat.profilePicUrl ? (
                        <img 
                          src={chat.profilePicUrl} 
                          alt={chat.name}
                          className="w-[49px] h-[49px] rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-[49px] h-[49px] bg-[#6bcf7f] rounded-full flex items-center justify-center text-white font-medium text-[19px]">
                          {getInitials(chat.name || chat.phone)}
                        </div>
                      )}
                      {chat.isOnline && (
                        <div className="absolute -bottom-[2px] -right-[2px] w-[15px] h-[15px] bg-[#06d755] border-2 border-white rounded-full"></div>
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-[2px]">
                        <h3 className="text-[17px] font-normal text-[#111b21] truncate leading-[21px]">
                          {chat.name || formatPhone(chat.phone)}
                        </h3>
                        {chat.lastMessage && (
                          <span className="text-[12px] text-[#667781] ml-[6px] leading-[20px] whitespace-nowrap">
                            {formatTime(typeof chat.lastMessage.timestamp === 'number' ? new Date(chat.lastMessage.timestamp) : chat.lastMessage.timestamp)}
                          </span>
                        )}
                      </div>
                      
                      <div className="flex items-center justify-between">
                        {chat.lastMessage ? (
                          <div className="flex items-center flex-1 min-w-0">
                            {chat.lastMessage.fromMe && (
                              <svg viewBox="0 0 16 15" width="16" height="15" className="text-[#667781] mr-[3px] flex-shrink-0">
                                <path fill="currentColor" d="M15.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L8.666 9.879a.32.32 0 0 1-.484.033l-1.91-1.909a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l2.258 2.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"/>
                                <path fill="currentColor" d="M12.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L5.666 9.879a.32.32 0 0 1-.484.033L3.191 7.769a.366.366 0 0 0-.515.006l-.423.433a.364.364 0 0 0 .006.514l2.258 2.185c.143.14.361.125.484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"/>
                              </svg>
                            )}
                            <p className="text-[14px] text-[#667781] truncate leading-[20px]">
                              {chat.lastMessage.body || chat.lastMessage.content}
                            </p>
                          </div>
                        ) : (
                          <p className="text-[14px] text-[#667781] leading-[20px]">Toque para conversar</p>
                        )}
                        
                        {chat.unreadCount > 0 && (
                          <div className="bg-[#00a884] text-white text-[12px] rounded-full min-w-[20px] h-[20px] flex items-center justify-center px-[6px] ml-[6px] font-medium">
                            {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col bg-[#efeae2]">
        {selectedClient && currentChat ? (
          <>
            {/* Chat Header */}
            <div className="bg-[#f0f2f5] h-[59px] px-4 flex items-center justify-between border-b border-[#d1d7db]">
              <div className="flex items-center space-x-[13px]">
                {currentChat.profilePicUrl ? (
                  <img 
                    src={currentChat.profilePicUrl} 
                    alt={currentChat.name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-10 h-10 bg-[#6bcf7f] rounded-full flex items-center justify-center">
                    <span className="text-white font-medium text-lg">
                      {getInitials(currentChat.name || currentChat.phone)}
                    </span>
                  </div>
                )}
                
                <div className="flex-1">
                  <h2 className="text-[16px] font-normal text-[#111b21] leading-[21px]">
                    {currentChat.name || formatPhone(currentChat.phone)}
                  </h2>
                  <p className="text-[13px] text-[#667781] leading-[20px]">
                    {currentChat.isOnline ? 'online' : `${formatPhone(currentChat.phone)} • clique para info do contato`}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-[10px]">
                <button className="w-10 h-10 hover:bg-[#f5f6f6] rounded-full transition-colors flex items-center justify-center">
                  <Search className="w-5 h-5 text-[#54656f]" />
                </button>
                
                <button 
                  onClick={async () => {
                    if (confirm('Desconectar WhatsApp? Você precisará escanear o QR Code novamente.')) {
                      try {
                        await fetch('/api/whatsapp/disconnect', { method: 'POST' });
                        toast({
                          title: "WhatsApp desconectado",
                          description: "Redirecionando para nova conexão...",
                        });
                        setTimeout(() => setLocation('/qr-code'), 1500);
                      } catch (error) {
                        toast({
                          title: "Erro",
                          description: "Falha ao desconectar WhatsApp",
                          variant: "destructive",
                        });
                      }
                    }
                  }}
                  className="w-10 h-10 hover:bg-[#f5f6f6] rounded-full transition-colors flex items-center justify-center"
                  title="Desconectar WhatsApp"
                >
                  <svg viewBox="0 0 24 24" width="20" height="20" className="text-[#54656f]">
                    <path fill="currentColor" d="M9,3V4H4V6H5V19A2,2 0 0,0 7,21H17A2,2 0 0,0 19,19V6H20V4H15V3H9M7,6H17V19H7V6M9,8V17H11V8H9M13,8V17H15V8H13Z"/>
                  </svg>
                </button>
                
                <button 
                  onClick={() => {
                    if (currentChat) {
                      // Open contact info or create task
                      toast({
                        title: "Ações disponíveis",
                        description: `Contato: ${currentChat.name || formatPhone(currentChat.phone)}`,
                      });
                    }
                  }}
                  className="w-10 h-10 hover:bg-[#f5f6f6] rounded-full transition-colors flex items-center justify-center"
                  title="Mais opções"
                >
                  <MoreVertical className="w-5 h-5 text-[#54656f]" />
                </button>
              </div>
            </div>

            {/* Messages Area */}
            <div 
              className="flex-1 overflow-y-auto p-6 bg-[#efeae2]"
              style={{ 
                backgroundImage: `url("data:image/svg+xml,%3csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='%23d1d7db' fill-opacity='0.05' fill-rule='evenodd'%3e%3cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7z'/%3e%3c/g%3e%3c/svg%3e")` 
              }}
            >
              {messagesLoading ? (
                <div className="flex flex-col justify-center items-center h-full gap-3">
                  <div className="w-10 h-10 border-3 border-[#00a884] border-t-transparent rounded-full animate-spin"></div>
                  <p className="text-[#8696a0] text-sm">Carregando mensagens...</p>
                </div>
              ) : messages.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <MessageSquare className="w-16 h-16 mx-auto mb-4 text-[#8696a0]" />
                    <p className="text-[#8696a0]">Nenhuma mensagem ainda</p>
                    <p className="text-sm text-[#8696a0] mt-1">Clique para carregar histórico do WhatsApp</p>
                    <button
                      onClick={() => {
                        console.log('Manual refresh clicked for:', selectedClient);
                        refetchMessages();
                      }}
                      className="mt-3 bg-[#00a884] text-white px-4 py-2 rounded-lg text-sm hover:bg-[#008f72] transition-colors"
                    >
                      Carregar histórico do WhatsApp
                    </button>
                    <p className="text-xs text-[#8696a0] mt-2">Debug: Mensagens carregadas: {messages.length}</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-1">
                  <div className="text-xs text-center text-[#8696a0] py-4 bg-[#e1f3fb] mx-4 rounded-lg mb-2">
                    <div className="flex items-center justify-center gap-2">
                      <svg viewBox="0 0 24 24" width="16" height="16" className="text-[#00a884]">
                        <path fill="currentColor" d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,17A1.5,1.5 0 0,0 13.5,15.5A1.5,1.5 0 0,0 12,14A1.5,1.5 0 0,0 10.5,15.5A1.5,1.5 0 0,0 12,17M12,10.5C12.8,10.5 13.5,9.8 13.5,9C13.5,8.2 12.8,7.5 12,7.5C11.2,7.5 10.5,8.2 10.5,9C10.5,9.8 11.2,10.5 12,10.5Z"/>
                      </svg>
                      <span className="text-[#00a884] font-medium">
                        Histórico completo carregado • {messages.length} mensagens
                      </span>
                    </div>
                    <div className="flex gap-3 mt-2">
                      <button
                        onClick={() => {
                          console.log('Loading more messages...');
                          refetchMessages();
                        }}
                        className="text-[#00a884] text-xs hover:underline"
                      >
                        Atualizar conversa
                      </button>
                      <button
                        onClick={async () => {
                          console.log('Loading extended history...');
                          const response = await fetch(`/api/whatsapp/chats/${selectedClient}/messages?limit=200`);
                          if (response.ok) {
                            const data = await response.json();
                            console.log('Extended history loaded:', data.length, 'messages');
                            queryClient.setQueryData(["/api/whatsapp/chats", selectedClient, "messages"], data.sort((a: WhatsAppMessage, b: WhatsAppMessage) => 
                              new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
                            ));
                          }
                        }}
                        className="text-[#00a884] text-xs hover:underline"
                      >
                        Carregar mais histórico
                      </button>
                    </div>
                  </div>
                  {messages.map((message: WhatsAppMessage, index: number) => {
                    console.log('Rendering message:', index, message.id, message.body);
                    const isSent = message.fromMe;
                    const nextMessage = messages[index + 1];
                    const prevMessage = messages[index - 1];
                    const isFirstInGroup = !prevMessage || prevMessage.fromMe !== message.fromMe;
                    const isLastInGroup = !nextMessage || nextMessage.fromMe !== message.fromMe;
                    
                    return (
                      <div
                        key={message.id}
                        className={`flex ${isSent ? 'justify-end' : 'justify-start'} ${
                          isLastInGroup ? 'mb-3' : 'mb-1'
                        }`}
                      >
                        <div
                          className={`relative max-w-[65%] px-3 py-2 cursor-pointer select-text ${
                            isSent
                              ? `bg-[#d9fdd3] text-[#111b21] ${
                                  isFirstInGroup && isLastInGroup 
                                    ? 'rounded-[7.5px]' 
                                    : isFirstInGroup 
                                      ? 'rounded-[7.5px] rounded-br-[2px]'
                                      : isLastInGroup
                                        ? 'rounded-[7.5px] rounded-tr-[2px]'
                                        : 'rounded-[7.5px] rounded-tr-[2px] rounded-br-[2px]'
                                }`
                              : `bg-white text-[#111b21] shadow-sm ${
                                  isFirstInGroup && isLastInGroup 
                                    ? 'rounded-[7.5px]' 
                                    : isFirstInGroup 
                                      ? 'rounded-[7.5px] rounded-bl-[2px]'
                                      : isLastInGroup
                                        ? 'rounded-[7.5px] rounded-tl-[2px]'
                                        : 'rounded-[7.5px] rounded-tl-[2px] rounded-bl-[2px]'
                                }`
                          } ${
                            isSent && isLastInGroup
                              ? 'before:absolute before:content-[""] before:right-[-8px] before:bottom-0 before:w-0 before:h-0 before:border-l-[8px] before:border-l-[#d9fdd3] before:border-b-[13px] before:border-b-transparent'
                              : !isSent && isLastInGroup
                                ? 'before:absolute before:content-[""] before:left-[-8px] before:bottom-0 before:w-0 before:h-0 before:border-r-[8px] before:border-r-white before:border-b-[13px] before:border-b-transparent'
                                : ''
                          }`}
                        >
                          {/* Media Content - WhatsApp Web Style */}
                          {message.hasMedia && message.mediaUrl && (
                            <div className="mb-2 max-w-[320px]">
                              {(message.mediaType === 'image' || message.mediaMimeType?.startsWith('image/')) && (
                                <div className="relative rounded-lg overflow-hidden bg-[#f0f2f5]">
                                  <img 
                                    src={message.mediaUrl} 
                                    alt={message.mediaName || 'Imagem'}
                                    className="w-full max-h-[320px] object-cover cursor-pointer hover:opacity-95 transition-opacity"
                                    onClick={() => {
                                      // Open image in fullscreen modal
                                      const modal = document.createElement('div');
                                      modal.className = 'fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4';
                                      modal.innerHTML = `
                                        <img src="${message.mediaUrl}" class="max-w-full max-h-full object-contain" />
                                        <button class="absolute top-4 right-4 text-white text-2xl">&times;</button>
                                      `;
                                      modal.onclick = () => document.body.removeChild(modal);
                                      document.body.appendChild(modal);
                                    }}
                                    onLoad={(e) => {
                                      const img = e.target as HTMLImageElement;
                                      if (img.naturalWidth > 0) {
                                        console.log('Image loaded successfully:', img.naturalWidth, 'x', img.naturalHeight);
                                      }
                                    }}
                                    onError={() => console.error('Failed to load image:', message.mediaUrl)}
                                  />
                                  {/* Image overlay with download button */}
                                  <div className="absolute bottom-2 right-2 opacity-70 hover:opacity-100">
                                    <button 
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        const link = document.createElement('a');
                                        if (message.mediaUrl) link.href = message.mediaUrl;
                                        link.download = message.mediaName || 'image';
                                        link.click();
                                      }}
                                      className="bg-black bg-opacity-50 text-white p-1.5 rounded-full hover:bg-opacity-70"
                                    >
                                      <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                                        <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
                                      </svg>
                                    </button>
                                  </div>
                                </div>
                              )}
                              
                              {(message.mediaType === 'audio' || message.mediaMimeType?.startsWith('audio/')) && (
                                <div className="bg-[#f0f2f5] rounded-lg p-3 min-w-[280px] flex items-center gap-3">
                                  <div className="w-10 h-10 bg-[#00a884] rounded-full flex items-center justify-center">
                                    <svg viewBox="0 0 24 24" width="20" height="20" className="text-white">
                                      <path fill="currentColor" d="M14,3.23V5.29C16.89,6.15 19,8.83 19,12C19,15.17 16.89,17.84 14,18.7V20.77C18,19.86 21,16.28 21,12C21,7.72 18,4.14 14,3.23M16.5,12C16.5,10.23 15.5,8.71 14,7.97V16C15.5,15.29 16.5,13.76 16.5,12M3,9V15H7L12,20V4L7,9H3Z"/>
                                    </svg>
                                  </div>
                                  <div className="flex-1">
                                    <audio 
                                      controls 
                                      className="w-full h-8"
                                      style={{ filter: 'sepia(1) hue-rotate(90deg) saturate(2)' }}
                                    >
                                      <source src={message.mediaUrl} type={message.mediaMimeType || 'audio/mpeg'} />
                                      Áudio não suportado
                                    </audio>
                                  </div>
                                </div>
                              )}
                              
                              {(message.mediaType === 'video' || message.mediaMimeType?.startsWith('video/')) && (
                                <div className="relative rounded-lg overflow-hidden bg-[#f0f2f5] max-w-[320px]">
                                  <video 
                                    controls 
                                    className="w-full max-h-[240px] object-cover"
                                    preload="metadata"
                                    poster=""
                                  >
                                    <source src={message.mediaUrl} type={message.mediaMimeType || 'video/mp4'} />
                                    Vídeo não suportado
                                  </video>
                                </div>
                              )}
                              
                              {(message.mediaType === 'document' || message.mediaType === 'sticker' || 
                                (!message.mediaMimeType?.startsWith('image/') && 
                                 !message.mediaMimeType?.startsWith('audio/') && 
                                 !message.mediaMimeType?.startsWith('video/') && 
                                 message.mediaUrl)) && (
                                <div 
                                  className="bg-[#f0f2f5] rounded-lg p-3 flex items-center gap-3 min-w-[240px] cursor-pointer hover:bg-[#e5e7eb] transition-colors"
                                  onClick={() => {
                                    if (message.mediaUrl) {
                                      const link = document.createElement('a');
                                      link.href = message.mediaUrl!;
                                      link.download = message.mediaName || 'arquivo';
                                      link.click();
                                    }
                                  }}
                                >
                                  <div className="w-12 h-12 bg-[#00a884] rounded-lg flex items-center justify-center">
                                    <svg viewBox="0 0 24 24" width="24" height="24" className="text-white">
                                      <path fill="currentColor" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                                    </svg>
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-[#111b21] truncate">
                                      {message.mediaName || 'Documento'}
                                    </p>
                                    <p className="text-xs text-[#667781]">
                                      {message.mediaMimeType || 'Arquivo'}
                                    </p>
                                  </div>
                                  <div className="text-[#54656f]">
                                    <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
                                      <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
                                    </svg>
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                          
                          {/* Text Content */}
                          <div className="text-[14.2px] leading-[19px] whitespace-pre-wrap break-words">
                            {message.body || message.content || (message.hasMedia ? '[Mídia]' : '[Mensagem vazia]')}
                          </div>
                          
                          <div className="flex items-center justify-end gap-1 mt-1 text-[#667781]">
                            <span className="text-[11px] leading-[14px]">
                              {formatTime(typeof message.timestamp === 'number' ? new Date(message.timestamp) : message.timestamp)}
                            </span>
                            {isSent && (
                              <div className="flex ml-1">
                                {renderMessageStatus(message.ack || 0)}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </div>

            {/* Message Input */}
            <div className="bg-[#f0f2f5] px-4 py-[5px] min-h-[62px] flex items-end">
              <div className="flex items-end gap-[5px] w-full">
                <div className="flex items-center gap-[3px] mb-[5px]">
                  <button className="w-[40px] h-[40px] hover:bg-[#f5f6f6] rounded-full transition-colors flex items-center justify-center">
                    <Paperclip className="w-6 h-6 text-[#54656f]" />
                  </button>
                </div>
                
                <div className="flex-1 bg-white rounded-[21px] min-h-[42px] max-h-[100px] flex items-center">
                  <div className="flex-1 flex items-center px-[12px] py-[9px]">
                    <button className="w-[26px] h-[26px] hover:bg-[#f5f6f6] rounded-full transition-colors flex items-center justify-center mr-[9px]">
                      <Smile className="w-6 h-6 text-[#54656f]" />
                    </button>
                    
                    <input
                      ref={inputRef}
                      type="text"
                      placeholder="Digite uma mensagem"
                      className="flex-1 outline-none bg-transparent text-[15px] text-[#111b21] placeholder-[#8696a0]"
                      value={messageInput}
                      onChange={(e) => setMessageInput(e.target.value)}
                      onKeyDown={handleKeyPress}
                      disabled={sendMessageMutation.isPending}
                    />
                  </div>
                </div>
                
                <div className="mb-[5px]">
                  {messageInput.trim() ? (
                    <button
                      onClick={handleSendMessage}
                      disabled={sendMessageMutation.isPending}
                      className="w-[40px] h-[40px] bg-[#00a884] hover:bg-[#017561] rounded-full transition-colors disabled:opacity-50 flex items-center justify-center"
                    >
                      {sendMessageMutation.isPending ? (
                        <div className="w-[20px] h-[20px] border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <Send className="w-5 h-5 text-white" />
                      )}
                    </button>
                  ) : (
                    <button className="w-[40px] h-[40px] hover:bg-[#f5f6f6] rounded-full transition-colors flex items-center justify-center">
                      <Mic className="w-6 h-6 text-[#54656f]" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-[#f8f9fa]">
            <div className="text-center max-w-md">
              <div className="w-[300px] h-[300px] mx-auto mb-8 bg-[#d9fdd3] rounded-full flex items-center justify-center">
                <MessageSquare className="w-32 h-32 text-[#00a884]" />
              </div>
              <h3 className="text-[32px] font-light text-[#41525d] mb-4">
                WhatsApp Web
              </h3>
              <p className="text-[14px] text-[#667781] leading-[20px]">
                Envie e receba mensagens sem precisar manter seu telefone conectado.
              </p>
              <p className="text-[14px] text-[#667781] leading-[20px] mt-4">
                Use o WhatsApp em até 4 dispositivos vinculados e 1 telefone ao mesmo tempo.
              </p>
              {!isConnected && !statusLoading && (
                <button
                  onClick={initializeConnection}
                  className="mt-6 bg-[#00a884] text-white px-6 py-3 rounded-lg hover:bg-[#008f72] transition-colors text-[14px] font-medium"
                >
                  Conectar WhatsApp
                </button>
              )}
              {statusLoading && (
                <div className="mt-6 flex items-center justify-center">
                  <div className="w-6 h-6 border-2 border-[#00a884] border-t-transparent rounded-full animate-spin mr-3"></div>
                  <span className="text-[#667781] text-[14px]">Verificando conexão...</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}