import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { toast } from '@/hooks/use-toast';
import { useWebSocket } from '@/hooks/useWebSocket';
import { useThemeStore } from '@/lib/theme-store';
import { apiRequest } from '@/lib/queryClient';
import { 
  Kanban as KanbanIcon, 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Calendar,
  User,
  Activity,
  Clock,
  AlertCircle,
  CheckCircle2,
  Moon,
  Sun,
  RefreshCw
} from 'lucide-react';
import { useState, useEffect } from 'react';

interface KanbanTask {
  id: string;
  title: string;
  description?: string;
  status: 'todo' | 'in_progress' | 'review' | 'done';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  clientId?: string;
  assignedTo?: string;
  dueDate?: Date;
  tags?: string[];
  position: number;
  createdAt: Date;
  updatedAt: Date;
}

const columns = [
  { id: 'todo', title: 'A Fazer', color: 'bg-gray-100 dark:bg-gray-800' },
  { id: 'in_progress', title: 'Em Progresso', color: 'bg-blue-50 dark:bg-blue-900/20' },
  { id: 'review', title: 'Revisão', color: 'bg-yellow-50 dark:bg-yellow-900/20' },
  { id: 'done', title: 'Concluído', color: 'bg-green-50 dark:bg-green-900/20' }
];

const priorityOptions = [
  { value: 'low', label: 'Baixa', color: 'bg-gray-100 text-gray-800' },
  { value: 'medium', label: 'Média', color: 'bg-blue-100 text-blue-800' },
  { value: 'high', label: 'Alta', color: 'bg-orange-100 text-orange-800' },
  { value: 'urgent', label: 'Urgente', color: 'bg-red-100 text-red-800' }
];

const teamMembers = ['João Silva', 'Maria Santos', 'Pedro Costa', 'Ana Oliveira'];

export default function KanbanPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [assigneeFilter, setAssigneeFilter] = useState('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<KanbanTask | null>(null);
  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    status: 'todo' as const,
    priority: 'medium' as const,
    clientId: '',
    assignedTo: '',
    dueDate: ''
  });

  const queryClient = useQueryClient();
  const { theme, toggleTheme } = useThemeStore();

  const { connected, emit } = useWebSocket({
    userId: `user_${Date.now()}`,
    autoConnect: true
  });

  // Fetch Kanban tasks
  const { data: tasks = [], isLoading, refetch } = useQuery<KanbanTask[]>({
    queryKey: ['/api/kanban/tasks'],
    refetchInterval: 30000
  });

  // Fetch CRM clients for task assignment
  const { data: clients = [] } = useQuery<any[]>({
    queryKey: ['/api/crm/clients']
  });

  // Real-time WebSocket events
  useEffect(() => {
    const handleTaskCreated = (event: CustomEvent) => {
      queryClient.invalidateQueries({ queryKey: ['/api/kanban/tasks'] });
      toast({
        title: "Nova tarefa criada",
        description: `Tarefa: ${event.detail.task.title}`,
      });
    };

    const handleTaskUpdated = (event: CustomEvent) => {
      queryClient.invalidateQueries({ queryKey: ['/api/kanban/tasks'] });
      toast({
        title: "Tarefa atualizada",
        description: `Tarefa ${event.detail.task.title} foi atualizada`,
      });
    };

    const handleTaskMoved = (event: CustomEvent) => {
      queryClient.invalidateQueries({ queryKey: ['/api/kanban/tasks'] });
      const statusLabels = {
        todo: 'A Fazer',
        in_progress: 'Em Progresso',
        review: 'Revisão',
        done: 'Concluído'
      };
      toast({
        title: "Tarefa movida",
        description: `Tarefa movida para ${statusLabels[event.detail.toStatus as keyof typeof statusLabels]}`,
      });
    };

    const handleTaskDeleted = (event: CustomEvent) => {
      queryClient.invalidateQueries({ queryKey: ['/api/kanban/tasks'] });
      toast({
        title: "Tarefa excluída",
        description: "A tarefa foi excluída com sucesso",
        variant: "destructive"
      });
    };

    window.addEventListener('kanban-task-created', handleTaskCreated as EventListener);
    window.addEventListener('kanban-task-updated', handleTaskUpdated as EventListener);
    window.addEventListener('kanban-task-moved', handleTaskMoved as EventListener);
    window.addEventListener('kanban-task-deleted', handleTaskDeleted as EventListener);

    return () => {
      window.removeEventListener('kanban-task-created', handleTaskCreated as EventListener);
      window.removeEventListener('kanban-task-updated', handleTaskUpdated as EventListener);
      window.removeEventListener('kanban-task-moved', handleTaskMoved as EventListener);
      window.removeEventListener('kanban-task-deleted', handleTaskDeleted as EventListener);
    };
  }, [queryClient]);

  // Create task mutation
  const createTaskMutation = useMutation({
    mutationFn: async (taskData: Partial<KanbanTask>) => {
      const response = await apiRequest('POST', '/api/kanban/tasks', taskData);
      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/kanban/tasks'] });
      setIsCreateDialogOpen(false);
      setNewTask({
        title: '',
        description: '',
        status: 'todo',
        priority: 'medium',
        clientId: '',
        assignedTo: '',
        dueDate: ''
      });
      
      // Emit WebSocket event
      emit('kanban-task-created', { task: data });
      
      toast({
        title: "Tarefa criada",
        description: `Tarefa "${data.title}" criada com sucesso`,
      });
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: "Falha ao criar tarefa",
        variant: "destructive"
      });
    }
  });

  // Update task mutation
  const updateTaskMutation = useMutation({
    mutationFn: async ({ id, ...taskData }: Partial<KanbanTask> & { id: string }) => {
      const response = await apiRequest('PUT', `/api/kanban/tasks/${id}`, taskData);
      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/kanban/tasks'] });
      setEditingTask(null);
      
      // Emit WebSocket event
      emit('kanban-task-updated', { task: data });
      
      toast({
        title: "Tarefa atualizada",
        description: `Tarefa "${data.title}" atualizada com sucesso`,
      });
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: "Falha ao atualizar tarefa",
        variant: "destructive"
      });
    }
  });

  // Delete task mutation
  const deleteTaskMutation = useMutation({
    mutationFn: async (taskId: string) => {
      const response = await apiRequest('DELETE', `/api/kanban/tasks/${taskId}`);
      return response.json();
    },
    onSuccess: (_, taskId) => {
      queryClient.invalidateQueries({ queryKey: ['/api/kanban/tasks'] });
      
      // Emit WebSocket event
      emit('kanban-task-deleted', { taskId });
      
      toast({
        title: "Tarefa excluída",
        description: "Tarefa excluída com sucesso",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: "Falha ao excluir tarefa",
        variant: "destructive"
      });
    }
  });

  // Move task mutation
  const moveTaskMutation = useMutation({
    mutationFn: async ({ taskId, newStatus, newPosition }: { taskId: string, newStatus: string, newPosition: number }) => {
      const response = await apiRequest('PUT', `/api/kanban/tasks/${taskId}/move`, {
        status: newStatus,
        position: newPosition
      });
      return response.json();
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['/api/kanban/tasks'] });
      
      // Emit WebSocket event
      emit('kanban-task-moved', { 
        task: data, 
        toStatus: variables.newStatus 
      });
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: "Falha ao mover tarefa",
        variant: "destructive"
      });
      // Revert optimistic update
      queryClient.invalidateQueries({ queryKey: ['/api/kanban/tasks'] });
    }
  });

  // Filter tasks
  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter;
    const matchesAssignee = assigneeFilter === 'all' || task.assignedTo === assigneeFilter;
    
    return matchesSearch && matchesPriority && matchesAssignee;
  });

  // Group tasks by status
  const tasksByStatus = columns.reduce((acc, column) => {
    acc[column.id] = filteredTasks
      .filter(task => task.status === column.id)
      .sort((a, b) => a.position - b.position);
    return acc;
  }, {} as Record<string, KanbanTask[]>);

  // Handle drag end
  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const { source, destination, draggableId } = result;
    
    if (source.droppableId === destination.droppableId && source.index === destination.index) {
      return;
    }

    const task = tasks.find(t => t.id === draggableId);
    if (!task) return;

    // Optimistic update
    const newStatus = destination.droppableId as KanbanTask['status'];
    const newPosition = destination.index;

    moveTaskMutation.mutate({
      taskId: draggableId,
      newStatus,
      newPosition
    });
  };

  const handleCreateTask = () => {
    if (!newTask.title.trim()) {
      toast({
        title: "Erro",
        description: "O título da tarefa é obrigatório",
        variant: "destructive"
      });
      return;
    }

    createTaskMutation.mutate({
      ...newTask,
      dueDate: newTask.dueDate ? new Date(newTask.dueDate) : undefined
    });
  };

  const handleUpdateTask = () => {
    if (!editingTask || !editingTask.title.trim()) {
      toast({
        title: "Erro",
        description: "O título da tarefa é obrigatório",
        variant: "destructive"
      });
      return;
    }

    updateTaskMutation.mutate(editingTask);
  };

  const getPriorityBadge = (priority: string) => {
    const option = priorityOptions.find(p => p.value === priority);
    return option ? (
      <Badge className={option.color}>
        {option.label}
      </Badge>
    ) : null;
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('pt-BR');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Carregando tarefas...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="flex items-center gap-3">
          <KanbanIcon className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold">Kanban - Gestão de Tarefas</h1>
            <p className="text-muted-foreground">
              Organize e acompanhe o progresso das suas tarefas
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={toggleTheme}
          >
            {theme === 'dark' ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
          </Button>
          
          <Button onClick={refetch} variant="outline" size="icon">
            <RefreshCw className="h-4 w-4" />
          </Button>
          
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Nova Tarefa
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Criar Nova Tarefa</DialogTitle>
                <DialogDescription>
                  Preencha os detalhes da nova tarefa
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="title">Título *</Label>
                  <Input
                    id="title"
                    value={newTask.title}
                    onChange={(e) => setNewTask({ ...newTask, title: e.target.value })}
                    placeholder="Digite o título da tarefa"
                  />
                </div>
                
                <div>
                  <Label htmlFor="description">Descrição</Label>
                  <Textarea
                    id="description"
                    value={newTask.description}
                    onChange={(e) => setNewTask({ ...newTask, description: e.target.value })}
                    placeholder="Descreva a tarefa"
                    rows={3}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="status">Status</Label>
                    <Select value={newTask.status} onValueChange={(value) => setNewTask({ ...newTask, status: value as any })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {columns.map(column => (
                          <SelectItem key={column.id} value={column.id}>
                            {column.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="priority">Prioridade</Label>
                    <Select value={newTask.priority} onValueChange={(value) => setNewTask({ ...newTask, priority: value as any })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {priorityOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="client">Cliente</Label>
                  <Select value={newTask.clientId} onValueChange={(value) => setNewTask({ ...newTask, clientId: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione um cliente" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">Nenhum cliente</SelectItem>
                      {clients.map(client => (
                        <SelectItem key={client.id} value={client.id}>
                          {client.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="assignee">Responsável</Label>
                  <Select value={newTask.assignedTo} onValueChange={(value) => setNewTask({ ...newTask, assignedTo: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Atribuir a alguém" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unassigned">Não atribuído</SelectItem>
                      {teamMembers.map(member => (
                        <SelectItem key={member} value={member}>
                          {member}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="dueDate">Data de Vencimento</Label>
                  <Input
                    id="dueDate"
                    type="date"
                    value={newTask.dueDate}
                    onChange={(e) => setNewTask({ ...newTask, dueDate: e.target.value })}
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2 mt-6">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancelar
                </Button>
                <Button onClick={handleCreateTask} disabled={createTaskMutation.isPending}>
                  {createTaskMutation.isPending ? 'Criando...' : 'Criar Tarefa'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar tarefas..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="flex flex-col md:flex-row gap-4">
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Filtrar por prioridade" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas prioridades</SelectItem>
                  {priorityOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={assigneeFilter} onValueChange={setAssigneeFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Responsável" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos responsáveis</SelectItem>
                  {teamMembers.map(member => (
                    <SelectItem key={member} value={member}>
                      {member}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Kanban Board */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {columns.map(column => (
            <div key={column.id} className="space-y-4">
              <div className={`p-4 rounded-lg ${column.color}`}>
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-lg">{column.title}</h3>
                  <Badge variant="secondary">
                    {tasksByStatus[column.id]?.length || 0}
                  </Badge>
                </div>
              </div>
              
              <Droppable droppableId={column.id}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className={`min-h-[200px] space-y-3 p-2 rounded-lg transition-colors ${
                      snapshot.isDraggingOver ? 'bg-muted/50' : ''
                    }`}
                  >
                    {tasksByStatus[column.id]?.map((task, index) => (
                      <Draggable key={task.id} draggableId={task.id} index={index}>
                        {(provided, snapshot) => (
                          <Card
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className={`cursor-move transition-shadow ${
                              snapshot.isDragging ? 'shadow-lg' : ''
                            }`}
                          >
                            <CardContent className="p-4">
                              <div className="space-y-3">
                                <div className="flex items-start justify-between">
                                  <h4 className="font-medium text-sm leading-tight">
                                    {task.title}
                                  </h4>
                                  <div className="flex gap-1">
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-6 w-6"
                                      onClick={() => setEditingTask(task)}
                                    >
                                      <Edit className="h-3 w-3" />
                                    </Button>
                                    <AlertDialog>
                                      <AlertDialogTrigger asChild>
                                        <Button
                                          variant="ghost"
                                          size="icon"
                                          className="h-6 w-6 text-destructive"
                                        >
                                          <Trash2 className="h-3 w-3" />
                                        </Button>
                                      </AlertDialogTrigger>
                                      <AlertDialogContent>
                                        <AlertDialogHeader>
                                          <AlertDialogTitle>Excluir tarefa</AlertDialogTitle>
                                          <AlertDialogDescription>
                                            Tem certeza que deseja excluir a tarefa "{task.title}"? 
                                            Esta ação não pode ser desfeita.
                                          </AlertDialogDescription>
                                        </AlertDialogHeader>
                                        <AlertDialogFooter>
                                          <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                          <AlertDialogAction
                                            onClick={() => deleteTaskMutation.mutate(task.id)}
                                            className="bg-destructive text-destructive-foreground"
                                          >
                                            Excluir
                                          </AlertDialogAction>
                                        </AlertDialogFooter>
                                      </AlertDialogContent>
                                    </AlertDialog>
                                  </div>
                                </div>
                                
                                {task.description && (
                                  <p className="text-xs text-muted-foreground line-clamp-2">
                                    {task.description}
                                  </p>
                                )}
                                
                                <div className="flex items-center justify-between">
                                  {getPriorityBadge(task.priority)}
                                  
                                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                    {task.assignedTo && (
                                      <div className="flex items-center gap-1">
                                        <User className="h-3 w-3" />
                                        <span className="truncate max-w-[80px]">
                                          {task.assignedTo}
                                        </span>
                                      </div>
                                    )}
                                    
                                    {task.dueDate && (
                                      <div className="flex items-center gap-1">
                                        <Calendar className="h-3 w-3" />
                                        <span>{formatDate(task.dueDate)}</span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                
                                <div className="flex items-center justify-between text-xs text-muted-foreground">
                                  <span>Criado: {formatDate(task.createdAt)}</span>
                                  {task.updatedAt !== task.createdAt && (
                                    <span>Atualizado: {formatDate(task.updatedAt)}</span>
                                  )}
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </div>
          ))}
        </div>
      </DragDropContext>

      {/* Edit Task Dialog */}
      {editingTask && (
        <Dialog open={!!editingTask} onOpenChange={() => setEditingTask(null)}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Editar Tarefa</DialogTitle>
              <DialogDescription>
                Atualize os detalhes da tarefa
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-title">Título *</Label>
                <Input
                  id="edit-title"
                  value={editingTask.title}
                  onChange={(e) => setEditingTask({ ...editingTask, title: e.target.value })}
                  placeholder="Digite o título da tarefa"
                />
              </div>
              
              <div>
                <Label htmlFor="edit-description">Descrição</Label>
                <Textarea
                  id="edit-description"
                  value={editingTask.description || ''}
                  onChange={(e) => setEditingTask({ ...editingTask, description: e.target.value })}
                  placeholder="Descreva a tarefa"
                  rows={3}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-status">Status</Label>
                  <Select 
                    value={editingTask.status} 
                    onValueChange={(value) => setEditingTask({ ...editingTask, status: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {columns.map(column => (
                        <SelectItem key={column.id} value={column.id}>
                          {column.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="edit-priority">Prioridade</Label>
                  <Select 
                    value={editingTask.priority} 
                    onValueChange={(value) => setEditingTask({ ...editingTask, priority: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {priorityOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <Label htmlFor="edit-client">Cliente</Label>
                <Select 
                  value={editingTask.clientId || 'none'} 
                  onValueChange={(value) => setEditingTask({ ...editingTask, clientId: value === 'none' ? undefined : value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um cliente" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Nenhum cliente</SelectItem>
                    {clients.map(client => (
                      <SelectItem key={client.id} value={client.id}>
                        {client.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="edit-assignee">Responsável</Label>
                <Select 
                  value={editingTask.assignedTo || 'unassigned'} 
                  onValueChange={(value) => setEditingTask({ ...editingTask, assignedTo: value === 'unassigned' ? undefined : value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Atribuir a alguém" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="unassigned">Não atribuído</SelectItem>
                    {teamMembers.map(member => (
                      <SelectItem key={member} value={member}>
                        {member}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="edit-dueDate">Data de Vencimento</Label>
                <Input
                  id="edit-dueDate"
                  type="date"
                  value={editingTask.dueDate ? new Date(editingTask.dueDate).toISOString().split('T')[0] : ''}
                  onChange={(e) => setEditingTask({ 
                    ...editingTask, 
                    dueDate: e.target.value ? new Date(e.target.value) : undefined 
                  })}
                />
              </div>
            </div>
            
            <div className="flex justify-end gap-2 mt-6">
              <Button variant="outline" onClick={() => setEditingTask(null)}>
                Cancelar
              </Button>
              <Button onClick={handleUpdateTask} disabled={updateTaskMutation.isPending}>
                {updateTaskMutation.isPending ? 'Salvando...' : 'Salvar Alterações'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Connection Status */}
      <div className="fixed bottom-4 right-4">
        <Badge variant={connected ? "default" : "destructive"}>
          <Activity className="h-3 w-3 mr-1" />
          {connected ? 'Conectado' : 'Desconectado'}
        </Badge>
      </div>
    </div>
  );
}

