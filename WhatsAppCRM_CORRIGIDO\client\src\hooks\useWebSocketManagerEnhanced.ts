import { useEffect, useRef, useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';

interface WebSocketManagerState {
  isConnected: boolean;
  connectionState: 'connecting' | 'connected' | 'disconnected' | 'reconnecting';
  reconnectAttempts: number;
  lastError: string | null;
  messageQueue: QueuedMessage[];
  connectionId: string;
  lastHeartbeat: Date | null;
  connectionHistory: ConnectionEvent[];
}

interface QueuedMessage {
  id: string;
  message: any;
  timestamp: Date;
  attempts: number;
  maxAttempts: number;
}

interface ConnectionEvent {
  timestamp: Date;
  type: 'connected' | 'disconnected' | 'error' | 'reconnect_attempt';
  details?: string;
  latency?: number;
}

interface WebSocketManagerOptions {
  maxReconnectAttempts?: number;
  reconnectInterval?: number;
  heartbeatInterval?: number;
  messageRetryAttempts?: number;
  enableMessageQueue?: boolean;
  enableConnectionHistory?: boolean;
  onMessage?: (data: any) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: string) => void;
  onReconnectSuccess?: (attempts: number, duration: number) => void;
  onMessageQueued?: (message: QueuedMessage) => void;
  onMessageDequeued?: (message: QueuedMessage) => void;
}

export function useWebSocketManagerEnhanced(options: WebSocketManagerOptions = {}) {
  const {
    maxReconnectAttempts = 10,
    reconnectInterval = 2000,
    heartbeatInterval = 30000,
    messageRetryAttempts = 3,
    enableMessageQueue = true,
    enableConnectionHistory = true,
    onMessage,
    onConnect,
    onDisconnect,
    onError,
    onReconnectSuccess,
    onMessageQueued,
    onMessageDequeued
  } = options;

  const [state, setState] = useState<WebSocketManagerState>({
    isConnected: false,
    connectionState: 'disconnected',
    reconnectAttempts: 0,
    lastError: null,
    messageQueue: [],
    connectionId: '',
    lastHeartbeat: null,
    connectionHistory: [],
  });

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectStartTime = useRef<number>(0);
  const connectionStartTime = useRef<number>(0);
  const messageIdCounter = useRef<number>(0);
  const { toast } = useToast();

  // Defensive logging utility
  const log = useCallback((level: 'info' | 'warn' | 'error', message: string, data?: any) => {
    const timestamp = new Date().toISOString();
    const logMessage = `[WebSocket ${timestamp}] ${level.toUpperCase()}: ${message}`;
    
    if (level === 'error') {
      console.error(logMessage, data || '');
    } else if (level === 'warn') {
      console.warn(logMessage, data || '');
    } else {
      console.log(logMessage, data || '');
    }
  }, []);

  // Add connection event to history
  const addConnectionEvent = useCallback((type: ConnectionEvent['type'], details?: string, latency?: number) => {
    if (!enableConnectionHistory) return;
    
    const event: ConnectionEvent = {
      timestamp: new Date(),
      type,
      details,
      latency
    };

    setState(prev => ({
      ...prev,
      connectionHistory: [event, ...prev.connectionHistory.slice(0, 49)] // Keep last 50 events
    }));

    log('info', `Connection event: ${type}`, { details, latency });
  }, [enableConnectionHistory, log]);

  // Generate unique connection ID
  const generateConnectionId = useCallback(() => {
    return `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Get WebSocket URL with enhanced error handling
  const getWebSocketUrl = useCallback(() => {
    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.host;
      const url = `${protocol}//${host}/ws`;
      log('info', `WebSocket URL constructed: ${url}`);
      return url;
    } catch (error) {
      log('error', 'Failed to construct WebSocket URL', error);
      throw new Error('Invalid WebSocket URL configuration');
    }
  }, [log]);

  // Enhanced heartbeat with response tracking
  const sendHeartbeat = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      const heartbeatTime = Date.now();
      wsRef.current.send(JSON.stringify({ 
        type: 'ping', 
        timestamp: heartbeatTime,
        connectionId: state.connectionId 
      }));
      
      setState(prev => ({ ...prev, lastHeartbeat: new Date() }));
      
      // Schedule next heartbeat
      heartbeatTimeoutRef.current = setTimeout(sendHeartbeat, heartbeatInterval);
      log('info', 'Heartbeat sent', { timestamp: heartbeatTime });
    }
  }, [heartbeatInterval, state.connectionId, log]);

  // Enhanced message queuing system
  const queueMessage = useCallback((message: any) => {
    if (!enableMessageQueue) return null;

    const queuedMessage: QueuedMessage = {
      id: `msg_${++messageIdCounter.current}_${Date.now()}`,
      message,
      timestamp: new Date(),
      attempts: 0,
      maxAttempts: messageRetryAttempts
    };

    setState(prev => ({
      ...prev,
      messageQueue: [...prev.messageQueue, queuedMessage]
    }));

    onMessageQueued?.(queuedMessage);
    log('info', 'Message queued', { messageId: queuedMessage.id });
    
    return queuedMessage.id;
  }, [enableMessageQueue, messageRetryAttempts, onMessageQueued, log]);

  // Process message queue after reconnection
  const processMessageQueue = useCallback(() => {
    if (!enableMessageQueue || state.messageQueue.length === 0) return;

    log('info', `Processing ${state.messageQueue.length} queued messages`);

    setState(prev => {
      const newQueue: QueuedMessage[] = [];
      
      prev.messageQueue.forEach(queuedMessage => {
        if (queuedMessage.attempts < queuedMessage.maxAttempts) {
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            try {
              wsRef.current.send(JSON.stringify(queuedMessage.message));
              queuedMessage.attempts++;
              
              onMessageDequeued?.(queuedMessage);
              log('info', 'Queued message sent', { 
                messageId: queuedMessage.id, 
                attempt: queuedMessage.attempts 
              });
            } catch (error) {
              log('error', 'Failed to send queued message', { 
                messageId: queuedMessage.id, 
                error 
              });
              newQueue.push(queuedMessage);
            }
          } else {
            newQueue.push(queuedMessage);
          }
        } else {
          log('warn', 'Message exceeded max retry attempts', { 
            messageId: queuedMessage.id 
          });
        }
      });

      return { ...prev, messageQueue: newQueue };
    });
  }, [enableMessageQueue, state.messageQueue, onMessageDequeued, log]);

  // Enhanced message handler with defensive parsing
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const data = JSON.parse(event.data);
      
      // Handle pong response with latency calculation
      if (data.type === 'pong') {
        const latency = Date.now() - data.timestamp;
        addConnectionEvent('connected', 'Heartbeat acknowledged', latency);
        return;
      }

      // Enhanced event dispatching with error handling
      try {
        if (data.type === 'message:received') {
          window.dispatchEvent(new CustomEvent('websocket:message:received', { detail: data }));
        } else if (data.type === 'message:sent') {
          window.dispatchEvent(new CustomEvent('websocket:message:sent', { detail: data }));
        } else if (data.type === 'whatsapp:connected') {
          window.dispatchEvent(new CustomEvent('websocket:whatsapp:connected', { detail: data }));
        } else if (data.type === 'whatsapp:disconnected') {
          window.dispatchEvent(new CustomEvent('websocket:whatsapp:disconnected', { detail: data }));
        } else if (data.type === 'session:restored') {
          window.dispatchEvent(new CustomEvent('websocket:session:restored', { detail: data }));
          log('info', 'WhatsApp session restored', data);
        } else if (data.type === 'typing:start') {
          window.dispatchEvent(new CustomEvent('websocket:typing:start', { detail: data }));
        } else if (data.type === 'typing:stop') {
          window.dispatchEvent(new CustomEvent('websocket:typing:stop', { detail: data }));
        }
      } catch (eventError) {
        log('error', 'Failed to dispatch WebSocket event', { type: data.type, error: eventError });
      }

      onMessage?.(data);
      log('info', 'Message received', { type: data.type });
      
    } catch (error) {
      log('error', 'Error parsing WebSocket message', { error, rawData: event.data });
    }
  }, [onMessage, addConnectionEvent, log]);

  // Enhanced connection function with comprehensive error handling
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      log('info', 'WebSocket already connected');
      return;
    }

    const isReconnect = state.reconnectAttempts > 0;
    const connectionId = generateConnectionId();
    
    if (isReconnect && reconnectStartTime.current === 0) {
      reconnectStartTime.current = Date.now();
    }
    
    connectionStartTime.current = Date.now();

    setState(prev => ({ 
      ...prev, 
      connectionState: isReconnect ? 'reconnecting' : 'connecting',
      lastError: null,
      connectionId
    }));

    addConnectionEvent(isReconnect ? 'reconnect_attempt' : 'connected', `Attempt ${state.reconnectAttempts + 1}`);

    try {
      const wsUrl = getWebSocketUrl();
      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      log('info', `${isReconnect ? 'Reconnecting' : 'Connecting'} WebSocket`, { 
        url: wsUrl, 
        connectionId,
        attempt: state.reconnectAttempts + 1 
      });

      ws.onopen = () => {
        const connectionTime = Date.now() - connectionStartTime.current;
        
        setState(prev => ({
          ...prev,
          isConnected: true,
          connectionState: 'connected',
          reconnectAttempts: 0,
          lastError: null,
          connectionId
        }));

        // Start heartbeat
        sendHeartbeat();
        
        onConnect?.();
        addConnectionEvent('connected', 'Connection established', connectionTime);

        if (isReconnect && reconnectStartTime.current > 0) {
          const reconnectDuration = Date.now() - reconnectStartTime.current;
          
          onReconnectSuccess?.(state.reconnectAttempts, reconnectDuration);
          
          toast({
            title: "Conexão reestabelecida",
            description: `Reconectado em ${Math.round(reconnectDuration / 1000)}s`,
          });
          
          log('info', 'Reconnection successful', { 
            attempts: state.reconnectAttempts,
            duration: reconnectDuration 
          });
          
          reconnectStartTime.current = 0;
          
          // Process queued messages after successful reconnection
          setTimeout(() => processMessageQueue(), 1000);
        } else {
          toast({
            title: "WebSocket conectado",
            description: "Conexão estabelecida com sucesso",
          });
          
          log('info', 'Initial connection successful', { connectionTime });
        }
      };

      ws.onmessage = handleMessage;

      ws.onerror = (error) => {
        const errorMessage = 'Erro de conexão WebSocket';
        
        setState(prev => ({ 
          ...prev, 
          lastError: errorMessage 
        }));

        addConnectionEvent('error', errorMessage);
        onError?.(errorMessage);
        log('error', 'WebSocket error', error);
      };

      ws.onclose = (event) => {
        const closeReason = `Code: ${event.code}, Reason: ${event.reason || 'No reason provided'}`;
        
        setState(prev => ({
          ...prev,
          isConnected: false,
          connectionState: 'disconnected',
        }));

        // Clear heartbeat
        if (heartbeatTimeoutRef.current) {
          clearTimeout(heartbeatTimeoutRef.current);
          heartbeatTimeoutRef.current = null;
        }

        addConnectionEvent('disconnected', closeReason);
        onDisconnect?.();
        log('warn', 'WebSocket connection closed', { code: event.code, reason: event.reason });

        // Enhanced reconnection logic with exponential backoff
        if (event.code !== 1000) {
          setState(prev => {
            const newAttempts = prev.reconnectAttempts + 1;
            
            if (newAttempts <= maxReconnectAttempts) {
              const backoffDelay = Math.min(
                reconnectInterval * Math.pow(1.5, newAttempts - 1),
                30000 // Max 30 seconds
              );
              
              reconnectTimeoutRef.current = setTimeout(() => {
                connect();
              }, backoffDelay);
              
              toast({
                title: "Conexão perdida",
                description: `Reconectando em ${Math.round(backoffDelay / 1000)}s (${newAttempts}/${maxReconnectAttempts})`,
                variant: "destructive"
              });
              
              log('warn', 'Scheduling reconnection', { 
                attempt: newAttempts, 
                delay: backoffDelay,
                maxAttempts: maxReconnectAttempts 
              });
            } else {
              toast({
                title: "Falha na conexão",
                description: "Não foi possível reestabelecer a conexão WebSocket",
                variant: "destructive"
              });
              
              log('error', 'Max reconnection attempts reached', { 
                attempts: newAttempts,
                maxAttempts: maxReconnectAttempts 
              });
            }
            
            return { ...prev, reconnectAttempts: newAttempts };
          });
        }
      };

    } catch (error) {
      const errorMessage = 'Falha ao criar conexão WebSocket';
      
      setState(prev => ({ 
        ...prev, 
        lastError: errorMessage 
      }));
      
      addConnectionEvent('error', errorMessage);
      log('error', 'Failed to create WebSocket connection', error);
    }
  }, [
    state.reconnectAttempts, generateConnectionId, getWebSocketUrl, sendHeartbeat, 
    onConnect, addConnectionEvent, onReconnectSuccess, toast, processMessageQueue, 
    handleMessage, onError, onDisconnect, maxReconnectAttempts, reconnectInterval, log
  ]);

  // Enhanced disconnect with cleanup
  const disconnect = useCallback(() => {
    log('info', 'Disconnecting WebSocket');
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (heartbeatTimeoutRef.current) {
      clearTimeout(heartbeatTimeoutRef.current);
      heartbeatTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    setState(prev => ({
      ...prev,
      isConnected: false,
      connectionState: 'disconnected',
      reconnectAttempts: 0,
      lastError: null,
    }));
    
    addConnectionEvent('disconnected', 'Manual disconnect');
  }, [addConnectionEvent, log]);

  // Enhanced reconnect function
  const reconnect = useCallback(() => {
    log('info', 'Manual reconnection triggered');
    disconnect();
    setState(prev => ({ ...prev, reconnectAttempts: 0 }));
    setTimeout(connect, 1000);
  }, [connect, disconnect, log]);

  // Enhanced send message with queuing support
  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      try {
        wsRef.current.send(JSON.stringify(message));
        log('info', 'Message sent successfully', { type: message.type });
        return true;
      } catch (error) {
        log('error', 'Failed to send message', { error, message });
        
        if (enableMessageQueue) {
          queueMessage(message);
          log('info', 'Message queued for retry');
        }
        
        return false;
      }
    } else {
      log('warn', 'Cannot send message - WebSocket not connected');
      
      if (enableMessageQueue) {
        queueMessage(message);
        log('info', 'Message queued - WebSocket disconnected');
      }
      
      return false;
    }
  }, [enableMessageQueue, queueMessage, log]);

  // Clear message queue
  const clearMessageQueue = useCallback(() => {
    setState(prev => ({ ...prev, messageQueue: [] }));
    log('info', 'Message queue cleared');
  }, [log]);

  // Get connection statistics
  const getConnectionStats = useCallback(() => {
    return {
      isConnected: state.isConnected,
      connectionState: state.connectionState,
      reconnectAttempts: state.reconnectAttempts,
      queuedMessages: state.messageQueue.length,
      connectionHistory: state.connectionHistory.length,
      lastHeartbeat: state.lastHeartbeat,
      connectionId: state.connectionId
    };
  }, [state]);

  // Auto-connect on mount
  useEffect(() => {
    log('info', 'WebSocket manager initializing');
    connect();

    return () => {
      log('info', 'WebSocket manager cleaning up');
      disconnect();
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (heartbeatTimeoutRef.current) {
        clearTimeout(heartbeatTimeoutRef.current);
      }
    };
  }, []);

  return {
    ...state,
    connect,
    disconnect,
    reconnect,
    sendMessage,
    clearMessageQueue,
    getConnectionStats,
  };
}