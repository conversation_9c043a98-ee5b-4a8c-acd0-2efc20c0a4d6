import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { MessageTestPanel } from '@/components/message-test-panel';
import { WebSocketTestPanel } from '@/components/websocket-test-panel';
import { Badge } from '@/components/ui/badge';
import { 
  Send, 
  Wifi, 
  TestTube,
  CheckCircle2
} from 'lucide-react';

export default function MessageValidationPage() {
  const [completedTests, setCompletedTests] = useState({
    websocket: false,
    text: false,
    image: false,
    video: false,
    document: false,
    audio: false,
    networkFailure: false
  });

  const validationSteps = [
    {
      id: 'websocket',
      title: 'Validação WebSocket',
      description: 'Teste de conectividade e comunicação em tempo real',
      icon: <Wifi className="h-5 w-5" />,
      completed: completedTests.websocket
    },
    {
      id: 'text',
      title: 'Mensage<PERSON> de Texto',
      description: 'Envio de mensagens de texto simples',
      icon: <Send className="h-5 w-5" />,
      completed: completedTests.text
    },
    {
      id: 'image',
      title: 'Envio de Imagem',
      description: 'Upload e envio de arquivos de imagem',
      icon: <Send className="h-5 w-5" />,
      completed: completedTests.image
    },
    {
      id: 'video',
      title: 'Envio de Vídeo',
      description: 'Upload e envio de arquivos de vídeo',
      icon: <Send className="h-5 w-5" />,
      completed: completedTests.video
    },
    {
      id: 'document',
      title: 'Envio de Documento',
      description: 'Upload e envio de documentos',
      icon: <Send className="h-5 w-5" />,
      completed: completedTests.document
    },
    {
      id: 'audio',
      title: 'Áudio Gravado',
      description: 'Gravação e envio de mensagens de áudio',
      icon: <Send className="h-5 w-5" />,
      completed: completedTests.audio
    },
    {
      id: 'networkFailure',
      title: 'Falha de Rede Simulada',
      description: 'Teste de retry automático com falha de rede',
      icon: <TestTube className="h-5 w-5" />,
      completed: completedTests.networkFailure
    }
  ];

  const completedCount = Object.values(completedTests).filter(Boolean).length;
  const totalTests = validationSteps.length;
  const progressPercentage = (completedCount / totalTests) * 100;

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Validação Completa de Envio de Mensagens</h1>
              <p className="text-muted-foreground mt-2">
                Etapa 4 - Teste abrangente de todos os tipos de mensagem com retry automático
              </p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-primary">{completedCount}/{totalTests}</div>
              <div className="text-sm text-muted-foreground">Testes Concluídos</div>
            </div>
          </div>

          {/* Progress Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TestTube className="h-5 w-5" />
                Progresso da Validação
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <div className="w-full bg-muted rounded-full h-3">
                      <div 
                        className="bg-primary h-3 rounded-full transition-all duration-500 ease-out"
                        style={{ width: `${progressPercentage}%` }}
                      />
                    </div>
                  </div>
                  <span className="text-sm font-medium">{progressPercentage.toFixed(0)}%</span>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {validationSteps.map((step) => (
                    <div 
                      key={step.id}
                      className={`flex items-center gap-3 p-3 rounded-lg border ${
                        step.completed 
                          ? 'bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800' 
                          : 'bg-muted/50 border-muted'
                      }`}
                    >
                      <div className={`flex-shrink-0 ${step.completed ? 'text-green-600' : 'text-muted-foreground'}`}>
                        {step.completed ? <CheckCircle2 className="h-5 w-5" /> : step.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm">{step.title}</div>
                        <div className="text-xs text-muted-foreground truncate">{step.description}</div>
                      </div>
                      <Badge variant={step.completed ? "default" : "secondary"} className="text-xs">
                        {step.completed ? "OK" : "Pendente"}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Test Panels */}
          <Tabs defaultValue="websocket" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="websocket" className="flex items-center gap-2">
                <Wifi className="h-4 w-4" />
                WebSocket
              </TabsTrigger>
              <TabsTrigger value="messages" className="flex items-center gap-2">
                <Send className="h-4 w-4" />
                Mensagens
              </TabsTrigger>
            </TabsList>

            <TabsContent value="websocket" className="space-y-6">
              <WebSocketTestPanel />
            </TabsContent>

            <TabsContent value="messages" className="space-y-6">
              <MessageTestPanel />
            </TabsContent>
          </Tabs>

          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle>Instruções de Validação</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2">Testes de WebSocket</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Teste de conexão e reconexão automática</li>
                      <li>• Medição de latência e estabilidade</li>
                      <li>• Validação de eventos em tempo real</li>
                      <li>• Monitoramento de métricas de performance</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Testes de Mensagens</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Envio de texto, imagem, vídeo, documento e áudio</li>
                      <li>• Simulação de falha de rede com retry automático</li>
                      <li>• Validação de status de entrega</li>
                      <li>• Teste de upload de arquivos com progress</li>
                    </ul>
                  </div>
                </div>
                <div className="p-4 bg-blue-50 dark:bg-blue-950 rounded-lg border border-blue-200 dark:border-blue-800">
                  <div className="text-sm">
                    <strong>Dica:</strong> Use o modo "Falha Simulada" na aba de Mensagens para testar o sistema de retry automático. 
                    O sistema tentará reenviar mensagens falhadas até 3 vezes com backoff exponencial.
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}