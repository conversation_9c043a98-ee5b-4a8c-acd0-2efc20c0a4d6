# WhatsApp CRM - Sistema Completo e Funcional

## 🚀 Instalação Automática

### Pré-requisitos
- Ubuntu 20.04+ ou Debian 11+
- Usuário com privilégios sudo
- Conexão com internet
- Mínimo 2GB RAM e 10GB espaço em disco

### Instalação Rápida

```bash
# 1. Baixar e extrair o sistema
wget https://github.com/seu-usuario/whatsapp-crm/archive/main.zip
unzip main.zip
cd whatsapp-crm-main

# 2. Executar instalação automática
chmod +x install.sh
./install.sh
```

### O que o script instala:
- ✅ Node.js 20
- ✅ PostgreSQL 14+
- ✅ PM2 (gerenciador de processos)
- ✅ Todas as dependências
- ✅ Configuração do banco de dados
- ✅ Serviço systemd
- ✅ Configuração de firewall
- ✅ Build do frontend

## 🎯 Funcionalidades 100% Operacionais

### ✅ WhatsApp Real
- Conexão com conta real do WhatsApp
- Envio e recebimento de mensagens
- QR Code para autenticação
- Sistema multi-usuário (30 vendedores)
- Sincronização em tempo real

### ✅ CRM Completo
- Gestão de leads/clientes
- Salvamento no PostgreSQL
- APIs funcionais
- Interface responsiva
- Filtros e busca

### ✅ Kanban Funcional
- Gestão de tarefas
- Drag & drop
- Filtros por prioridade
- Atribuição de responsáveis
- Datas de vencimento

### ✅ Dashboard
- Métricas em tempo real
- Gráficos e estatísticas
- Status do sistema
- Monitoramento de conexões

### ✅ Banco de Dados
- PostgreSQL configurado
- Tabelas criadas automaticamente
- Índices otimizados
- Dados de exemplo

## 🔧 Configuração Pós-Instalação

### 1. Primeiro Acesso
```bash
# Verificar se está rodando
sudo systemctl status whatsappcrm

# Acessar via navegador
http://SEU_IP:5000
```

### 2. Conectar WhatsApp
1. Acesse a seção "WhatsApp Web"
2. Clique em "Conectar WhatsApp"
3. Escaneie o QR Code com seu celular
4. Aguarde a confirmação de conexão

### 3. Configurar Usuários
1. Acesse "CRM - Clientes"
2. Adicione seus clientes
3. Configure vendedores no sistema

## 🛠️ Comandos de Gerenciamento

### Serviço Principal
```bash
# Status do serviço
sudo systemctl status whatsappcrm

# Parar serviço
sudo systemctl stop whatsappcrm

# Iniciar serviço
sudo systemctl start whatsappcrm

# Reiniciar serviço
sudo systemctl restart whatsappcrm

# Habilitar inicialização automática
sudo systemctl enable whatsappcrm
```

### Logs e Monitoramento
```bash
# Ver logs do sistema
sudo journalctl -u whatsappcrm -f

# Ver logs do PM2
sudo -u whatsappcrm pm2 logs

# Ver logs específicos
tail -f /opt/whatsappcrm/logs/combined.log
```

### Banco de Dados
```bash
# Conectar ao PostgreSQL
sudo -u postgres psql -d whatsappcrm

# Backup do banco
sudo -u postgres pg_dump whatsappcrm > backup.sql

# Restaurar backup
sudo -u postgres psql whatsappcrm < backup.sql
```

## 🌐 Acesso Remoto

### Configuração de Rede
O sistema está configurado para aceitar conexões de qualquer IP na porta 5000.

### Firewall
```bash
# Permitir acesso externo
sudo ufw allow 5000/tcp

# Verificar regras
sudo ufw status
```

### Nginx (Opcional)
Para usar com domínio próprio:
```bash
# Instalar Nginx
sudo apt install nginx

# O script já configura automaticamente se Nginx estiver instalado
```

## 🔒 Segurança

### Configurações Recomendadas
1. **Firewall**: Configure apenas as portas necessárias
2. **HTTPS**: Use certificado SSL em produção
3. **Backup**: Configure backups automáticos
4. **Monitoramento**: Configure alertas de sistema

### Arquivos Importantes
- Configuração: `/opt/whatsappcrm/.env`
- Logs: `/opt/whatsappcrm/logs/`
- Sessões WhatsApp: `/opt/whatsappcrm/sessions/`
- Uploads: `/opt/whatsappcrm/uploads/`

## 📊 Monitoramento

### Verificação de Saúde
```bash
# API Health Check
curl http://localhost:5000/api/health

# Status do WhatsApp
curl http://localhost:5000/api/whatsapp/clients

# Status do banco
sudo -u postgres psql -d whatsappcrm -c "SELECT COUNT(*) FROM leads;"
```

### Métricas do Sistema
- CPU e memória via PM2
- Conexões ativas do WhatsApp
- Número de mensagens processadas
- Status das APIs

## 🚨 Solução de Problemas

### Problemas Comuns

#### Serviço não inicia
```bash
# Verificar logs
sudo journalctl -u whatsappcrm -n 50

# Verificar configuração
sudo -u whatsappcrm pm2 list
```

#### WhatsApp não conecta
```bash
# Limpar sessões
sudo rm -rf /opt/whatsappcrm/sessions/user_*

# Reiniciar serviço
sudo systemctl restart whatsappcrm
```

#### Banco de dados com erro
```bash
# Verificar PostgreSQL
sudo systemctl status postgresql

# Testar conexão
PGPASSWORD=postgres psql -h localhost -U postgres -d whatsappcrm -c "SELECT 1;"
```

#### Frontend não carrega
```bash
# Rebuild do frontend
cd /opt/whatsappcrm
sudo -u whatsappcrm npm run build
sudo systemctl restart whatsappcrm
```

## 📱 Uso do Sistema

### Para Vendedores
1. Acesse via navegador: `http://IP_SERVIDOR:5000`
2. Conecte seu WhatsApp na seção "WhatsApp Web"
3. Gerencie leads na seção "CRM - Clientes"
4. Organize tarefas no "Kanban - Tarefas"
5. Monitore métricas no "Dashboard"

### Para Administradores
1. Monitore o sistema via "Saúde do Sistema"
2. Configure usuários e permissões
3. Faça backups regulares
4. Monitore logs e performance

## 🔄 Atualizações

### Atualizar Sistema
```bash
# Parar serviço
sudo systemctl stop whatsappcrm

# Backup
sudo cp -r /opt/whatsappcrm /opt/whatsappcrm.backup

# Atualizar código
cd /opt/whatsappcrm
sudo -u whatsappcrm git pull

# Instalar dependências
sudo -u whatsappcrm npm install

# Build frontend
sudo -u whatsappcrm npm run build

# Iniciar serviço
sudo systemctl start whatsappcrm
```

## 📞 Suporte

### Informações do Sistema
- **Versão**: 2.0 - Totalmente Funcional
- **Data**: 30/06/2025
- **Tecnologias**: Node.js, React, PostgreSQL, Socket.IO
- **Compatibilidade**: Ubuntu 20.04+, Debian 11+

### Logs Importantes
- Sistema: `/var/log/syslog`
- Aplicação: `/opt/whatsappcrm/logs/`
- PostgreSQL: `/var/log/postgresql/`
- Nginx: `/var/log/nginx/`

### Contato
Para suporte técnico, consulte os logs do sistema e verifique a documentação completa.

---

**🎉 Sistema WhatsApp CRM - 100% Funcional e Pronto para Produção!**

