import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Smartphone, QrCode, CheckCircle } from "lucide-react";

export default function Login() {
  const [userId, setUserId] = useState<string>(() => {
    // Try to get existing user ID from localStorage
    const saved = localStorage.getItem('whatsapp_user_id');
    return saved || `user_${Date.now()}`;
  });
  const [isGeneratingQR, setIsGeneratingQR] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Save user ID to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('whatsapp_user_id', userId);
  }, [userId]);

  // Check user connection status
  const { data: status, refetch: refetchStatus } = useQuery({
    queryKey: ["/api/whatsapp/status", userId],
    queryFn: async () => {
      const response = await fetch(`/api/whatsapp/status?userId=${encodeURIComponent(userId)}`);
      if (!response.ok) throw new Error('Failed to check status');
      return response.json();
    },
    refetchInterval: 2000, // Check every 2 seconds
  });

  // Generate QR code mutation
  const generateQRMutation = useMutation({
    mutationFn: async () => {
      setIsGeneratingQR(true);
      const response = await fetch(`/api/whatsapp/qr?userId=${encodeURIComponent(userId)}`);
      if (!response.ok) throw new Error('Failed to generate QR code');
      return response.json();
    },
    onSuccess: (data) => {
      setIsGeneratingQR(false);
      if (data.connected) {
        toast({
          title: "Conectado!",
          description: `WhatsApp conectado: ${data.phoneNumber}`,
        });
        // Redirect to WhatsApp interface
        window.location.href = '/messages-whatsapp';
      } else {
        refetchStatus();
      }
    },
    onError: () => {
      setIsGeneratingQR(false);
      toast({
        title: "Erro",
        description: "Falha ao gerar QR Code",
        variant: "destructive",
      });
    },
  });

  // Auto-redirect if already connected
  useEffect(() => {
    if (status?.connected) {
      // Store the successful session
      localStorage.setItem('whatsapp_user_id', userId);
      localStorage.setItem('whatsapp_connected', 'true');
      localStorage.setItem('whatsapp_phone', status.phoneNumber || '');
      
      // Force refresh to reload app with authenticated state
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  }, [status, userId]);

  const handleNewUser = () => {
    const newUserId = `user_${Date.now()}`;
    setUserId(newUserId);
    localStorage.setItem('whatsapp_user_id', newUserId);
    toast({
      title: "Nova sessão criada",
      description: "Agora você pode escanear seu QR Code",
    });
  };

  return (
    <div className="min-h-screen bg-[#f0f2f5] flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        <div className="text-center">
          <div className="w-20 h-20 bg-[#00a884] rounded-full flex items-center justify-center mx-auto mb-4">
            <Smartphone className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-[#111b21] mb-2">
            WhatsApp Web CRM
          </h1>
          <p className="text-[#667781] text-sm">
            Sistema multi-usuário de comunicação empresarial
          </p>
        </div>

        <Card className="border-none shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="text-center text-lg text-[#111b21]">
              Conectar WhatsApp
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-[#111b21] mb-2 block">
                ID do Usuário
              </label>
              <Input
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                placeholder="Seu ID único"
                className="mb-2"
              />
              <p className="text-xs text-[#667781]">
                Cada usuário precisa de um ID único para acessar seu WhatsApp
              </p>
            </div>

            {status?.connected ? (
              <div className="text-center space-y-4">
                <div className="flex items-center justify-center gap-2 text-[#00a884]">
                  <CheckCircle className="w-5 h-5" />
                  <span className="font-medium">Conectado!</span>
                </div>
                <p className="text-sm text-[#667781]">
                  Telefone: {status.phoneNumber}
                </p>
                <Button 
                  onClick={() => window.location.href = '/messages-whatsapp'}
                  className="w-full bg-[#00a884] hover:bg-[#008f72]"
                >
                  Acessar WhatsApp
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {status?.qrCode ? (
                  <div className="text-center space-y-4">
                    <div className="bg-white p-4 rounded-lg border">
                      <img 
                        src={status.qrCode} 
                        alt="QR Code WhatsApp" 
                        className="w-48 h-48 mx-auto"
                      />
                    </div>
                    <div className="text-sm text-[#667781] space-y-1">
                      <p className="font-medium">Como conectar:</p>
                      <ol className="text-left list-decimal list-inside space-y-1">
                        <li>Abra o WhatsApp no seu celular</li>
                        <li>Toque em Mais opções ou Configurações</li>
                        <li>Toque em Aparelhos conectados</li>
                        <li>Toque em Conectar um aparelho</li>
                        <li>Escaneie este código QR</li>
                      </ol>
                    </div>
                    <div className="flex items-center justify-center gap-2 text-[#00a884]">
                      <div className="w-2 h-2 bg-[#00a884] rounded-full animate-pulse"></div>
                      <span className="text-sm">Aguardando escaneamento...</span>
                    </div>
                  </div>
                ) : (
                  <Button
                    onClick={() => generateQRMutation.mutate()}
                    disabled={isGeneratingQR || generateQRMutation.isPending}
                    className="w-full bg-[#00a884] hover:bg-[#008f72]"
                  >
                    {(isGeneratingQR || generateQRMutation.isPending) ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Gerando QR Code...
                      </>
                    ) : (
                      <>
                        <QrCode className="w-4 h-4 mr-2" />
                        Gerar QR Code
                      </>
                    )}
                  </Button>
                )}
              </div>
            )}

            <div className="pt-4 border-t">
              <Button
                variant="outline"
                onClick={handleNewUser}
                className="w-full text-[#667781] border-[#e9edef] hover:bg-[#f5f6f6]"
              >
                Novo Usuário
              </Button>
              <p className="text-xs text-[#667781] text-center mt-2">
                Clique para criar uma nova sessão de usuário
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="text-center text-xs text-[#667781]">
          <p>
            Sistema seguro de comunicação empresarial
          </p>
          <p className="mt-1">
            Cada usuário mantém sua sessão individual do WhatsApp
          </p>
        </div>
      </div>
    </div>
  );
}