import { X, Reply } from "lucide-react";

interface MessageReplyProps {
  replyToMessage: any;
  onCancel: () => void;
}

export default function MessageReply({ replyToMessage, onCancel }: MessageReplyProps) {
  if (!replyToMessage) return null;

  return (
    <div className="bg-gray-100 border-l-4 border-green-500 p-3 mx-4 mb-2 rounded-r-lg">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-1">
            <Reply className="w-4 h-4 text-green-600" />
            <span className="text-sm font-medium text-green-600">
              Respondendo para {replyToMessage.fromMe ? 'Você' : replyToMessage.senderName || 'Contato'}
            </span>
          </div>
          <div className="text-sm text-gray-700 line-clamp-2">
            {replyToMessage.hasMedia ? (
              <span className="italic">
                {replyToMessage.type === 'image' ? '📷 Imagem' :
                 replyToMessage.type === 'video' ? '🎥 Vídeo' :
                 replyToMessage.type === 'audio' ? '🎵 Áudio' :
                 replyToMessage.type === 'document' ? '📄 Documento' : 'Mídia'}
              </span>
            ) : (
              replyToMessage.body || replyToMessage.content
            )}
          </div>
        </div>
        <button
          onClick={onCancel}
          className="p-1 hover:bg-gray-200 rounded-full ml-2"
        >
          <X className="w-4 h-4 text-gray-500" />
        </button>
      </div>
    </div>
  );
}