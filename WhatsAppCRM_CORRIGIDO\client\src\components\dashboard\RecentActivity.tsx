import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatRelativeTime } from '@/lib/utils';
import { Activity } from '@shared/schema';
import { MessageCircle, Users, CheckCircle, AlertCircle } from 'lucide-react';

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'message_received':
    case 'message_sent':
      return MessageCircle;
    case 'lead_created':
    case 'status_change':
      return Users;
    case 'lead_closed':
      return CheckCircle;
    default:
      return AlertCircle;
  }
};

const getActivityColor = (type: string) => {
  switch (type) {
    case 'message_received':
    case 'message_sent':
      return 'bg-whatsapp';
    case 'lead_created':
    case 'status_change':
      return 'bg-blue-500';
    case 'lead_closed':
      return 'bg-green-500';
    default:
      return 'bg-gray-500';
  }
};

export function RecentActivity() {
  const { data: activities = [], isLoading } = useQuery({
    queryKey: ['/api/activities'],
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Atividades Recentes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                <div className="flex-1 space-y-1">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/3 animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Atividades Recentes</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.length === 0 ? (
            <div className="text-center text-gray-500 dark:text-gray-400 py-8">
              <p className="text-sm">Nenhuma atividade recente</p>
            </div>
          ) : (
            activities.map((activity: Activity) => {
              const Icon = getActivityIcon(activity.type);
              const colorClass = getActivityColor(activity.type);
              
              return (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className={`w-8 h-8 ${colorClass} rounded-full flex items-center justify-center`}>
                    <Icon className="w-4 h-4 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900 dark:text-white">
                      {activity.description}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {formatRelativeTime(activity.createdAt!)}
                    </p>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </CardContent>
    </Card>
  );
}
