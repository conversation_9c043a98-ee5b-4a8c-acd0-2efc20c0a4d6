import { useState, useEffect } from 'react';
import { X, AlertCircle, CheckCircle, Info, AlertTriangle, Wifi, WifiOff } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info' | 'connection';
  title: string;
  message: string;
  timestamp: Date;
  persistent?: boolean;
  actionLabel?: string;
  onAction?: () => void;
}

interface NotificationCenterProps {
  className?: string;
}

const NotificationCenter = ({ className = '' }: NotificationCenterProps) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const { toast } = useToast();

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      addNotification({
        type: 'success',
        title: 'Conexão Restaurada',
        message: 'Você está online novamente',
        persistent: false
      });
    };

    const handleOffline = () => {
      setIsOnline(false);
      addNotification({
        type: 'connection',
        title: 'Sem Conexão',
        message: 'Verifique sua conexão com a internet',
        persistent: true
      });
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date()
    };

    setNotifications(prev => [newNotification, ...prev.slice(0, 4)]);

    // Auto-remove non-persistent notifications
    if (!notification.persistent) {
      setTimeout(() => {
        removeNotification(newNotification.id);
      }, 5000);
    }

    // Also show toast for important notifications
    if (notification.type === 'error' || notification.type === 'warning') {
      toast({
        title: notification.title,
        description: notification.message,
        variant: notification.type === 'error' ? 'destructive' : 'default'
      });
    }
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />;
      case 'connection':
        return isOnline ? 
          <Wifi className="h-4 w-4 text-green-500" /> : 
          <WifiOff className="h-4 w-4 text-red-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getNotificationColor = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20';
      case 'error':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20';
      case 'info':
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20';
      case 'connection':
        return isOnline 
          ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
          : 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20';
      default:
        return 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800';
    }
  };

  // Expose addNotification globally for use by other components
  useEffect(() => {
    (window as any).addNotification = addNotification;
    return () => {
      delete (window as any).addNotification;
    };
  }, []);

  if (notifications.length === 0) return null;

  return (
    <div className={`fixed top-4 right-4 z-50 space-y-2 max-w-sm ${className}`}>
      {notifications.map((notification) => (
        <Card 
          key={notification.id}
          className={`${getNotificationColor(notification.type)} border shadow-lg animate-in slide-in-from-right duration-300`}
        >
          <CardContent className="p-4">
            <div className="flex items-start justify-between space-x-3">
              <div className="flex items-start space-x-3 flex-1">
                {getNotificationIcon(notification.type)}
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                    {notification.title}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {notification.message}
                  </p>
                  {notification.actionLabel && notification.onAction && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={notification.onAction}
                      className="mt-2"
                    >
                      {notification.actionLabel}
                    </Button>
                  )}
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeNotification(notification.id)}
                className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default NotificationCenter;

// Helper hook for easy notification usage
export const useNotifications = () => {
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    if ((window as any).addNotification) {
      (window as any).addNotification(notification);
    }
  };

  return {
    notifySuccess: (title: string, message: string) => 
      addNotification({ type: 'success', title, message }),
    
    notifyError: (title: string, message: string, actionLabel?: string, onAction?: () => void) => 
      addNotification({ type: 'error', title, message, persistent: true, actionLabel, onAction }),
    
    notifyWarning: (title: string, message: string) => 
      addNotification({ type: 'warning', title, message }),
    
    notifyInfo: (title: string, message: string) => 
      addNotification({ type: 'info', title, message }),
    
    notifyConnection: (isConnected: boolean) => 
      addNotification({
        type: 'connection',
        title: isConnected ? 'Conectado' : 'Desconectado',
        message: isConnected ? 'WhatsApp está conectado' : 'WhatsApp desconectado',
        persistent: !isConnected
      })
  };
};