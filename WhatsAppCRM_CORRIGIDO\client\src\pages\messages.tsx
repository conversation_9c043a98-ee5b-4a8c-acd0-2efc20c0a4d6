import { useState, useRef, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { MessageSquare, Wifi, WifiOff, AlertCircle, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useWebSocket } from "@/hooks/use-websocket";
import { apiRequest } from "@/lib/queryClient";
import type { Client, Message } from "@shared/schema";

// Importar os componentes de chat
import ContactListWhatsApp from "@/components/chat/contact-list-whatsapp";
import ChatHeader from "@/components/chat/chat-header";
import MessageBubble from "@/components/chat/message-bubble";
import ChatInput from "@/components/chat/chat-input";

export default function Messages() {
  const [selectedClient, setSelectedClient] = useState<string>("");
  const [isConnected, setIsConnected] = useState(false);
  const [currentWhatsappNumber, setCurrentWhatsappNumber] = useState<string>("5511999999999");
  const [cachedChats, setCachedChats] = useState<any[]>([]);
  const [cachedMessages, setCachedMessages] = useState<{[chatId: string]: any[]}>({});
  const [dataSource, setDataSource] = useState<'whatsapp' | 'database' | 'cache'>('cache');
  const [hasRealData, setHasRealData] = useState(false);
  const [messageInput, setMessageInput] = useState("");
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // WebSocket com proteção contra sobrescrita de dados reais
  const shouldConnectWS = isConnected && currentWhatsappNumber;
  const { isConnected: wsConnected } = useWebSocket(
    shouldConnectWS ? 'ws://localhost:5001' : '', 
    {
      onMessage: (message: any) => {
        // Verificar se WhatsApp está conectado antes de processar
        if (!isConnected || !currentWhatsappNumber) {
          console.log('Ignoring WebSocket message - WhatsApp disconnected');
          return;
        }

        console.log('WebSocket message received:', message.type);

        switch (message.type) {
          case 'new_message':
            if (message.preserveExisting && message.chatId && message.message) {
              // Atualizar apenas mensagens do chat específico
              queryClient.invalidateQueries({ 
                queryKey: ["/api/whatsapp/chats", message.chatId, "messages", currentWhatsappNumber] 
              });
              
              console.log('Updated messages for specific chat:', message.chatId);
            }
            break;
            
          case 'new_client':
            if (message.preserveExisting && message.client && !hasRealData) {
              // Apenas adicionar novo cliente se não há dados reais carregados
              setCachedChats(prev => {
                const exists = prev.some(chat => chat.id === message.chatId);
                if (!exists) {
                  const newChat = {
                    id: message.chatId,
                    name: message.client.name,
                    phone: message.client.phone,
                    lastMessage: null,
                    unreadCount: 1,
                    isOnline: false
                  };
                  console.log('Added new chat to cache:', newChat);
                  return [...prev, newChat];
                }
                return prev;
              });
            }
            break;
            
          case 'connection_status':
            if (message.connected && message.phoneNumber) {
              setCurrentWhatsappNumber(message.phoneNumber);
              console.log('WhatsApp connection status updated:', message.phoneNumber);
            }
            break;
            
          default:
            console.log('Unknown WebSocket message type:', message.type);
        }
      },
      onConnectionChange: (connected) => {
        console.log('WebSocket connection changed:', connected);
      },
      reconnectAttempts: 3,
      reconnectInterval: 5000
    }
  );

  // Buscar chats com proteção robusta contra sobrescrita de dados reais
  const { data: fetchedChats = [], isLoading: chatsLoading } = useQuery({
    queryKey: ["/api/whatsapp/chats", currentWhatsappNumber],
    queryFn: async () => {
      if (!currentWhatsappNumber) return cachedChats;
      
      console.log(`Chat fetch attempt - hasRealData: ${hasRealData}, dataSource: ${dataSource}, cachedChats: ${cachedChats.length}`);
      
      // PROTEÇÃO CRÍTICA: Nunca sobrescrever dados reais validados
      if (hasRealData && cachedChats.length > 0 && dataSource === 'whatsapp') {
        console.log('Protecting real data - returning cached chats');
        return cachedChats;
      }
      
      // PROTEÇÃO: Nunca ativar fallback se WhatsApp conectado e dados reais já carregados
      if (isConnected && hasRealData && cachedChats.length > 0) {
        console.log('WhatsApp connected with real data - maintaining state');
        setDataSource('cache');
        return cachedChats;
      }
      
      // Tentar buscar chats reais do WhatsApp se conectado
      if (isConnected) {
        try {
          const response = await fetch(`/api/whatsapp/chats?whatsappNumber=${currentWhatsappNumber}`);
          if (response.ok) {
            const whatsappChats = await response.json();
            
            // Verificar se são dados reais válidos (não fallback do banco)
            if (Array.isArray(whatsappChats) && whatsappChats.length > 0) {
              const hasRealStructure = whatsappChats.some(chat => 
                chat.id && !chat.id.startsWith('5511') && (chat.lastMessage || chat.unreadCount >= 0)
              );
              
              const isFallbackData = whatsappChats.every(chat => 
                chat.name && ['Maria Silva', 'João Santos', 'Ana Costa'].includes(chat.name)
              );
              
              if (hasRealStructure && !isFallbackData) {
                console.log('DETECTED REAL WHATSAPP DATA:', whatsappChats.length, 'chats');
                setCachedChats(whatsappChats);
                setHasRealData(true);
                setDataSource('whatsapp');
                return whatsappChats;
              } else if (isFallbackData) {
                console.log('Detected fallback data from server - using only if no real data exists');
                if (!hasRealData && cachedChats.length === 0) {
                  setDataSource('database');
                  setHasRealData(false);
                  return whatsappChats;
                }
              }
            }
          }
        } catch (error) {
          console.log('WhatsApp API error:', error);
        }
      }
      
      // Usar cache existente com preferência
      if (cachedChats.length > 0) {
        console.log('Using existing cached chats:', cachedChats.length);
        setDataSource('cache');
        return cachedChats;
      }
      
      // Fallback APENAS se desconectado E sem dados reais
      if (!isConnected && !hasRealData) {
        try {
          const response = await fetch(`/api/whatsapp/chats?whatsappNumber=${currentWhatsappNumber}`);
          if (response.ok) {
            const fallbackChats = await response.json();
            if (Array.isArray(fallbackChats) && fallbackChats.length > 0) {
              console.log('Using database fallback (demo mode only)');
              setDataSource('database');
              setHasRealData(false);
              return fallbackChats;
            }
          }
        } catch (error) {
          console.log('Database fallback unavailable');
        }
      }
      
      return [];
    },
    enabled: !!currentWhatsappNumber,
    refetchInterval: hasRealData ? 60000 : 30000, // Menos frequente para dados reais
    staleTime: hasRealData ? 10 * 60 * 1000 : 5 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
  });

  // Buscar mensagens com proteção contra sobrescrita de dados reais
  const { data: fetchedMessages = [], isLoading: messagesLoading } = useQuery({
    queryKey: ["/api/whatsapp/chats", selectedClient, "messages", currentWhatsappNumber],
    queryFn: async () => {
      if (!selectedClient || !currentWhatsappNumber) {
        return cachedMessages[selectedClient] || [];
      }
      
      // Se já temos mensagens reais em cache, não buscar novamente durante envio
      const existingMessages = cachedMessages[selectedClient];
      if (existingMessages && existingMessages.length > 0 && hasRealData) {
        return existingMessages;
      }
      
      // Tentar buscar mensagens reais do WhatsApp se conectado
      if (isConnected && dataSource !== 'database') {
        try {
          const response = await fetch(`/api/whatsapp/chats/${selectedClient}/messages?whatsappNumber=${currentWhatsappNumber}&limit=50`);
          if (response.ok) {
            const whatsappMessages = await response.json();
            
            // Verificar se são mensagens reais (não vazias de erro)
            if (Array.isArray(whatsappMessages)) {
              // Se temos mensagens válidas ou erro real (não 0 por problemas de API)
              if (whatsappMessages.length > 0) {
                console.log(`Loaded ${whatsappMessages.length} real WhatsApp messages`);
                setCachedMessages(prev => ({
                  ...prev,
                  [selectedClient]: whatsappMessages
                }));
                return whatsappMessages;
              } 
              // Se retornou array vazio mas WhatsApp está conectado, pode ser chat sem mensagens
              else if (dataSource === 'whatsapp') {
                setCachedMessages(prev => ({
                  ...prev,
                  [selectedClient]: []
                }));
                return [];
              }
            }
          }
        } catch (error) {
          console.log('WhatsApp messages error, checking alternatives');
        }
      }
      
      // Usar cache existente se disponível e confiável
      if (existingMessages && existingMessages.length > 0) {
        return existingMessages;
      }
      
      // Fallback para banco apenas se não há dados reais e é modo demonstração
      if (dataSource === 'database' || (!isConnected && !hasRealData)) {
        try {
          const selectedChat = whatsappChats.find((chat: any) => chat.id === selectedClient);
          const clientPhone = selectedChat?.phone;
          
          if (clientPhone) {
            const response = await fetch(`/api/messages?whatsappNumber=${currentWhatsappNumber}&clientPhone=${clientPhone}`);
            if (response.ok) {
              const dbMessages = await response.json();
              if (Array.isArray(dbMessages) && dbMessages.length >= 0) {
                console.log(`Using database messages (demo mode): ${dbMessages.length}`);
                setCachedMessages(prev => ({
                  ...prev,
                  [selectedClient]: dbMessages
                }));
                return dbMessages;
              }
            }
          }
        } catch (error) {
          console.log('Database messages unavailable');
        }
      }
      
      return [];
    },
    enabled: !!selectedClient && !!currentWhatsappNumber,
    refetchInterval: hasRealData ? 10000 : 30000, // Refetch mais frequente para dados reais
    staleTime: 2 * 60 * 1000,
    gcTime: 15 * 60 * 1000,
  });

  // Usar dados em cache ou dados buscados
  const whatsappChats = fetchedChats.length > 0 ? fetchedChats : cachedChats;
  const chatMessages = fetchedMessages.length >= 0 ? fetchedMessages : (cachedMessages[selectedClient] || []);

  const { data: whatsappStatus } = useQuery({
    queryKey: ["/api/whatsapp/status"],
    queryFn: async () => {
      const response = await fetch("/api/whatsapp/status");
      return response.json() as Promise<{connected: boolean, phoneNumber?: string}>;
    },
    refetchInterval: 5000,
  });

  const sendMessageMutation = useMutation({
    mutationFn: async (data: { clientPhone: string; content: string }) => {
      const response = await fetch("/api/messages/send", {
        method: "POST",
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...data,
          whatsappNumber: currentWhatsappNumber
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to send message');
      }
      
      return response.json();
    },
    onSuccess: (result, variables) => {
      // Preservar dados reais durante envio - não invalidar chats se temos dados reais
      if (hasRealData && dataSource === 'whatsapp') {
        // Apenas invalidar mensagens, mantendo lista de chats
        setTimeout(() => {
          queryClient.invalidateQueries({ 
            queryKey: ["/api/whatsapp/chats", selectedClient, "messages", currentWhatsappNumber] 
          });
        }, 500);
      } else {
        // Para dados de demonstração, invalidar normalmente
        queryClient.invalidateQueries({ queryKey: ["/api/whatsapp/chats", selectedClient, "messages", currentWhatsappNumber] });
        queryClient.invalidateQueries({ queryKey: ["/api/whatsapp/chats", currentWhatsappNumber] });
      }
      
      toast({
        title: "Mensagem enviada",
        description: "Sua mensagem foi enviada com sucesso.",
      });
    },
    onError: () => {
      toast({
        title: "Erro",
        description: "Falha ao enviar mensagem. Verifique sua conexão.",
        variant: "destructive",
      });
    },
  });

  const handleSendMessage = (content: string) => {
    if (!selectedClient || !content.trim()) return;
    
    const selectedChat = getSelectedClientData();
    if (!selectedChat) return;
    
    sendMessageMutation.mutate({
      clientPhone: selectedChat.phone,
      content: content.trim(),
    });
  };

  const getSelectedClientData = () => {
    return whatsappChats.find((chat: any) => chat.id === selectedClient);
  };

  const getSelectedClientMessages = () => {
    if (!chatMessages || chatMessages.length === 0) return [];
    return chatMessages.sort((a: any, b: any) => new Date(a.timestamp || 0).getTime() - new Date(b.timestamp || 0).getTime());
  };

  // Auto-scroll para a última mensagem
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatMessages, selectedClient]);

  // Verificar status do WhatsApp e atualizar número conectado
  useEffect(() => {
    if (whatsappStatus?.connected) {
      setIsConnected(true);
      if (whatsappStatus.phoneNumber && whatsappStatus.phoneNumber !== currentWhatsappNumber) {
        setCurrentWhatsappNumber(whatsappStatus.phoneNumber);
      }
    } else {
      setIsConnected(false);
    }
  }, [whatsappStatus, currentWhatsappNumber]);

  // Persistir selectedClient no localStorage
  useEffect(() => {
    const savedSelectedClient = localStorage.getItem('selectedWhatsAppClient');
    if (savedSelectedClient && !selectedClient) {
      setSelectedClient(savedSelectedClient);
    }
  }, [selectedClient]);

  // Salvar selectedClient no localStorage quando mudar
  useEffect(() => {
    if (selectedClient) {
      localStorage.setItem('selectedWhatsAppClient', selectedClient);
    }
  }, [selectedClient]);

  // Persistir cachedChats e estado de dados no localStorage
  useEffect(() => {
    const savedChats = localStorage.getItem('cachedWhatsAppChats');
    const savedDataSource = localStorage.getItem('whatsappDataSource');
    const savedHasRealData = localStorage.getItem('whatsappHasRealData');
    
    if (savedChats && cachedChats.length === 0) {
      try {
        const parsedChats = JSON.parse(savedChats);
        if (Array.isArray(parsedChats) && parsedChats.length > 0) {
          setCachedChats(parsedChats);
        }
      } catch (error) {
        console.error('Error parsing cached chats:', error);
      }
    }
    
    if (savedDataSource) {
      setDataSource(savedDataSource as 'whatsapp' | 'database' | 'cache');
    }
    
    if (savedHasRealData) {
      setHasRealData(savedHasRealData === 'true');
    }
  }, [cachedChats]);

  // Debounced localStorage persistence para evitar perda de dados
  useEffect(() => {
    const saveChatsDebounced = setTimeout(() => {
      if (cachedChats.length > 0) {
        localStorage.setItem('cachedWhatsAppChats', JSON.stringify(cachedChats));
        console.log(`💾 Persisted ${cachedChats.length} chats to localStorage`);
      }
    }, 500);
    
    return () => clearTimeout(saveChatsDebounced);
  }, [cachedChats]);
  
  useEffect(() => {
    const saveStateDebounced = setTimeout(() => {
      localStorage.setItem('whatsappDataSource', dataSource);
      localStorage.setItem('whatsappHasRealData', hasRealData.toString());
      console.log(`💾 Persisted state - dataSource: ${dataSource}, hasRealData: ${hasRealData}`);
    }, 500);
    
    return () => clearTimeout(saveStateDebounced);
  }, [dataSource, hasRealData]);
  
  // Adicionar logging para substituições de estado de chats
  useEffect(() => {
    if (fetchedChats.length > 0 && fetchedChats !== cachedChats) {
      console.log(`🔄 CHAT STATE CHANGE: ${cachedChats.length} → ${fetchedChats.length} chats`);
      console.log(`   Source: ${dataSource}, Real Data: ${hasRealData}`);
      console.log(`   Previous: [${cachedChats.map(c => c.name).join(', ')}]`);
      console.log(`   Current: [${fetchedChats.map(c => c.name).join(', ')}]`);
    }
  }, [fetchedChats, cachedChats, dataSource, hasRealData]);

  // Mostrar interface mesmo sem conexão se há dados em cache ou modo demo
  if (!isConnected && cachedChats.length === 0 && whatsappChats.length === 0) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-500">Aguardando conexão com WhatsApp...</p>
          <p className="text-sm text-gray-400 mt-2">Escaneie o QR Code nas configurações para conectar</p>
        </div>
      </div>
    );
  }

  if (chatsLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-500">Carregando conversas...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-100 dark:bg-gray-900">
      {/* Header com status de conexão */}
      <div className="bg-green-600 text-white px-6 py-3 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <MessageSquare className="w-6 h-6" />
          <div>
            <h1 className="text-lg font-semibold">WhatsApp Business</h1>
            <p className="text-sm text-green-100">
              {isConnected ? `Conectado: ${currentWhatsappNumber}` : "Desconectado"}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {isConnected ? (
            <Wifi className="w-5 h-5 text-green-200" />
          ) : (
            <WifiOff className="w-5 h-5 text-red-300" />
          )}
          
          {isConnected && wsConnected && (
            <div className="w-2 h-2 bg-green-300 rounded-full animate-pulse" title="WhatsApp + WebSocket ativos" />
          )}
          
          {isConnected && !wsConnected && (
            <div className="w-2 h-2 bg-yellow-300 rounded-full" title="WhatsApp ativo, WebSocket desconectado" />
          )}
          
          {/* Indicadores de fonte de dados */}
          {dataSource === 'database' && (
            <div className="flex items-center gap-1 px-2 py-1 bg-amber-100/20 rounded-md border border-amber-300/30">
              <AlertCircle className="w-3 h-3 text-amber-200" />
              <span className="text-xs text-amber-100">Demo</span>
            </div>
          )}
          
          {dataSource === 'whatsapp' && hasRealData && (
            <div className="flex items-center gap-1 px-2 py-1 bg-green-100/20 rounded-md border border-green-300/30">
              <CheckCircle className="w-3 h-3 text-green-200" />
              <span className="text-xs text-green-100">Real</span>
            </div>
          )}
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* Lista de contatos */}
        <ContactListWhatsApp
          contacts={whatsappChats}
          selectedContact={selectedClient}
          onSelectContact={setSelectedClient}
        />

        {/* Área de chat */}
        <div className="flex-1 flex flex-col bg-gray-50 dark:bg-gray-900">
          {selectedClient ? (
            <>
              {/* Header do chat */}
              <ChatHeader
                clientName={getSelectedClientData()?.name || "Cliente"}
                clientPhone={selectedClient}
                isOnline={false}
                lastSeen={undefined}
              />

              {/* Mensagens */}
              <div 
                className="flex-1 overflow-y-auto bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIwIDIwQzIwIDIwIDIwIDIwIDIwIDIwWiIgZmlsbD0iIzAwMDAwMCIgZmlsbC1vcGFjaXR5PSIwLjA1Ii8+Cjwvc3ZnPgo=')] py-4"
                style={{ 
                  backgroundSize: '40px 40px',
                  backgroundRepeat: 'repeat',
                  minHeight: '400px'
                }}
              >
                {messagesLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mx-auto mb-2"></div>
                      <p className="text-gray-500 text-sm">Carregando mensagens...</p>
                    </div>
                  </div>
                ) : getSelectedClientMessages().length === 0 ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center text-gray-500 dark:text-gray-400">
                      <p className="text-sm">Nenhuma mensagem neste contato ainda</p>
                      <p className="text-xs mt-1">Envie a primeira mensagem para começar a conversa</p>
                    </div>
                  </div>
                ) : (
                  getSelectedClientMessages().map((message: any) => (
                    <MessageBubble
                      key={message.id}
                      content={message.content}
                      type={message.type as 'sent' | 'received'}
                      timestamp={message.timestamp}
                      senderName={message.type === 'received' ? getSelectedClientData()?.name : undefined}
                      status={message.type === 'sent' ? 'delivered' : undefined}
                    />
                  ))
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* Input de mensagem */}
              <ChatInput
                onSendMessage={handleSendMessage}
                disabled={!isConnected || sendMessageMutation.isPending}
                placeholder={
                  !isConnected 
                    ? "WhatsApp desconectado - conecte para enviar mensagens" 
                    : "Digite uma mensagem..."
                }
              />
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center text-gray-500 dark:text-gray-400">
                <MessageSquare className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium mb-2">Bem-vindo ao WhatsApp Business</h3>
                <p>Selecione uma conversa para começar a enviar mensagens</p>
                {!isConnected && (
                  <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                    <p className="text-yellow-800 dark:text-yellow-200">
                      WhatsApp não está conectado. Vá para as configurações para conectar.
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}