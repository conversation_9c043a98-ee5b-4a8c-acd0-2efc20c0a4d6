# Casa das Camisetas - WhatsApp CRM
## Guia Rápido de Instalação no Dell PowerEdge R420

### 📦 Arquivos de Instalação Criados

```
📁 Arquivos de Instalação:
├── install.sh                     # Script principal de instalação
├── configure-lan-access.sh        # Otimizações para rede local
├── TUTORIAL_POWEREDGE_R420.md     # Tutorial completo detalhado
└── README_INSTALACAO.md           # Este arquivo (guia rápido)
```

---

## 🚀 Instalação em 3 Passos

### 1. Preparar o Servidor
```bash
# Conectar no PowerEdge R420 via SSH
ssh usuario@*************

# Transferir arquivos do projeto
scp -r whatsappcrm/ usuario@*************:/home/<USER>/
```

### 2. Executar Instalação Principal
```bash
# Navegar para o diretório
cd /home/<USER>/

# Dar permissão e executar
chmod +x install.sh
./install.sh
```

**O script instala automaticamente:**
- Node.js 20 LTS + npm + PM2
- PostgreSQL 15 com configurações otimizadas
- Nginx como proxy reverso
- Sistema de firewall configurado
- Backup automático diário
- Monitoramento do sistema

### 3. Configurar Acesso LAN (Opcional)
```bash
# Para otimizar acesso multi-usuário
chmod +x configure-lan-access.sh
./configure-lan-access.sh
```

---

## 🌐 Acesso dos Usuários

**URL de Acesso:** `http://*************`

### Exemplo de Uso na Empresa:
```
🖥️ Servidor: Dell PowerEdge R420 (*************)

👥 Vendedores acessam via navegador:
   • Vendedor 1: http://************* (do PC ************)
   • Vendedor 2: http://************* (do PC ************)
   • ... até 30 vendedores simultâneos
```

---

## 📱 WhatsApp Multi-Device

### Configuração Inicial:
1. Acesse `http://*************`
2. Clique em "Conectar WhatsApp"
3. Escaneie o QR Code com WhatsApp Business
4. Sistema conecta automaticamente

### Recursos:
- ✅ Cada vendedor com seu próprio WhatsApp
- ✅ Mensagens em tempo real
- ✅ Suporte a mídias e grupos
- ✅ Reconexão automática
- ✅ Backup das sessões

---

## 🔧 Administração

### Comandos Essenciais:
```bash
pm2 status                    # Status da aplicação
pm2 logs casa-camisetas-crm   # Ver logs
pm2 restart casa-camisetas-crm # Reiniciar
./test-lan-access.sh          # Testar conectividade
```

### Diretórios Importantes:
```
/opt/whatsappcrm/          # Projeto
/opt/whatsappcrm/logs/     # Logs
/opt/whatsappcrm/sessions/ # Sessões WhatsApp
/opt/backups/              # Backups automáticos
```

---

## 🎯 Capacidades do Sistema

### Performance:
- **30 usuários simultâneos** via rede local
- **PM2 cluster mode** para alta disponibilidade
- **Nginx rate limiting** para estabilidade
- **PostgreSQL otimizado** para múltiplas conexões

### Funcionalidades:
- **WhatsApp CRM completo** com Baileys v2
- **Kanban de leads** visual e colaborativo
- **Dashboard analítico** para supervisores
- **Sistema de usuários** com permissões
- **Backup automático** diário às 2:00
- **Monitoramento 24/7** do sistema

---

## 🛠️ Troubleshooting Rápido

### Sistema não carrega:
```bash
pm2 restart casa-camisetas-crm
sudo systemctl restart nginx
```

### WhatsApp desconecta:
```bash
rm -rf /opt/whatsappcrm/sessions/*
pm2 restart casa-camisetas-crm
# Reconectar via QR Code
```

### Verificar conectividade:
```bash
./test-lan-access.sh
```

---

## 📞 Suporte

### Logs para Diagnóstico:
```bash
# Aplicação
tail -f /opt/whatsappcrm/logs/combined.log

# Sistema
./test-lan-access.sh

# Rede LAN
tail -f /opt/whatsappcrm/logs/lan-monitor.log
```

### Verificações de Saúde:
- Acesse: `http://*************/health`
- Monitor: `pm2 monit`
- Status: `systemctl status nginx postgresql`

---

## ✅ Resultado Final

Com a instalação completa você terá:

**🏢 Casa das Camisetas CRM Empresarial**
- Servidor Dell PowerEdge R420 otimizado
- Acesso via rede local para toda equipe
- WhatsApp Business integrado por vendedor
- Sistema robusto e monitorado 24/7

**🌐 Acesso Simples**
Todos acessam: `http://*************`

**📱 WhatsApp Real**
Baileys v2 Multi-Device com dados 100% autênticos

---

*Sistema pronto para uso empresarial com suporte a 30 vendedores simultâneos*