import { createContext, useContext, useState, useEffect, ReactNode, useCallback } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";

interface WhatsAppContextType {
  isConnected: boolean;
  phoneNumber: string | null;
  qrCode: string | null;
  isLoading: boolean;
  userId: string;
  sessionStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
  error: string | null;
  refreshStatus: () => void;
  initializeConnection: () => Promise<void>;
  disconnectSession: () => Promise<void>;
}

// Create context with default values to prevent undefined errors
const WhatsAppContext = createContext<WhatsAppContextType>({
  isConnected: false,
  phoneNumber: null,
  qrCode: null,
  isLoading: false,
  userId: '',
  sessionStatus: 'disconnected',
  error: null,
  refreshStatus: () => {},
  initializeConnection: async () => {},
  disconnectSession: async () => {}
});

export function WhatsAppProvider({ children }: { children: ReactNode }) {
  const [isConnected, setIsConnected] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState<string | null>(null);
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [sessionStatus, setSessionStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const [error, setError] = useState<string | null>(null);
  const [userId, setUserId] = useState<string>('');
  const [isInitialized, setIsInitialized] = useState(false);
  
  const queryClient = useQueryClient();

  // Initialize userId on mount
  useEffect(() => {
    const initializeUserId = async () => {
      if (typeof window !== 'undefined' && !isInitialized) {
        try {
          setSessionStatus('connecting');
          
          // First, check if there's an active session on the server
          const response = await fetch('/api/whatsapp/clients');
          const clients = await response.json();
          
          // Look for any active session
          const activeUserId = Object.keys(clients).find(userId => 
            clients[userId]?.isConnected
          );
          
          if (activeUserId) {
            const session = clients[activeUserId];
            setUserId(activeUserId);
            localStorage.setItem('userId', activeUserId);
            setIsConnected(true);
            setPhoneNumber(session.phoneNumber);
            setSessionStatus('connected');
            setError(null);
            setIsInitialized(true);
            return;
          }
        } catch (error) {
          console.log('No active sessions found, creating new userId');
          setError(null);
        }

        // Fallback: use stored or create new userId
        let storedUserId = localStorage.getItem('userId');
        if (!storedUserId) {
          storedUserId = `user_${Date.now()}`;
          localStorage.setItem('userId', storedUserId);
        }
        setUserId(storedUserId);
        setSessionStatus('disconnected');
        setIsInitialized(true);
      }
    };

    initializeUserId();
  }, [isInitialized]);

  const { data, isLoading, refetch, error: queryError } = useQuery({
    queryKey: ["/api/whatsapp/status", userId],
    queryFn: async () => {
      if (!userId) return { connected: false, phoneNumber: null, qrCode: null };
      
      try {
        // Use the clients endpoint for accurate status
        const response = await fetch('/api/whatsapp/clients');
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const clients = await response.json();
        const userSession = clients[userId];
        
        const result = {
          connected: userSession?.isConnected || false,
          isConnected: userSession?.isConnected || false,
          phoneNumber: userSession?.phoneNumber || null,
          activeChats: userSession?.activeChats || 0,
          qrCode: null // QR not needed when checking status
        };
        
        setError(null);
        return result;
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch status');
        throw err;
      }
    },
    enabled: !!userId && isInitialized,
    refetchInterval: (query) => {
      const data = query.state.data;
      // Faster polling when connecting, slower when stable
      if (!data?.connected && data?.qrCode) return 1000; // Connecting - check every second
      if (data?.connected) return 5000; // Connected - check every 5 seconds
      return 3000; // Default - check every 3 seconds
    },
    retry: (failureCount, error) => {
      // Retry up to 3 times with exponential backoff
      if (failureCount >= 3) return false;
      setTimeout(() => {}, Math.pow(2, failureCount) * 1000);
      return true;
    },
    staleTime: 1000, // Consider data stale after 1 second
  });

  // Update state when data changes
  useEffect(() => {
    if (data) {
      console.log('WhatsApp status update:', data);
      setIsConnected(data.connected || false);
      setPhoneNumber(data.phoneNumber || null);
      setQrCode(data.qrCode || null);
      
      // Update session status based on connection state
      if (data.connected) {
        setSessionStatus('connected');
      } else if (data.qrCode) {
        setSessionStatus('connecting');
      } else {
        setSessionStatus('disconnected');
      }
    }
  }, [data]);

  // Handle query errors
  useEffect(() => {
    if (queryError) {
      setSessionStatus('error');
      setError(queryError instanceof Error ? queryError.message : 'Connection error');
      setIsConnected(false);
      setPhoneNumber(null);
      setQrCode(null);
    }
  }, [queryError]);

  const refreshStatus = useCallback(() => {
    setError(null);
    refetch();
  }, [refetch]);

  const initializeConnection = useCallback(async () => {
    if (!userId) {
      setError('User ID not available');
      return;
    }

    try {
      setSessionStatus('connecting');
      setError(null);
      
      const response = await fetch('/api/whatsapp/initialize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) {
        throw new Error(`Failed to initialize: ${response.statusText}`);
      }

      const result = await response.json();
      
      // Invalidate and refetch status
      queryClient.invalidateQueries({ queryKey: ["/api/whatsapp/status", userId] });
      refetch();
      
    } catch (err) {
      setSessionStatus('error');
      setError(err instanceof Error ? err.message : 'Failed to initialize connection');
    }
  }, [userId, queryClient, refetch]);

  const disconnectSession = useCallback(async () => {
    if (!userId) return;

    try {
      const response = await fetch('/api/whatsapp/disconnect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) {
        throw new Error(`Failed to disconnect: ${response.statusText}`);
      }

      // Update state immediately
      setIsConnected(false);
      setPhoneNumber(null);
      setQrCode(null);
      setSessionStatus('disconnected');
      setError(null);
      
      // Invalidate queries
      queryClient.invalidateQueries({ queryKey: ["/api/whatsapp/status", userId] });
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to disconnect');
    }
  }, [userId, queryClient]);

  const contextValue: WhatsAppContextType = {
    isConnected,
    phoneNumber,
    qrCode,
    isLoading: isLoading || !isInitialized,
    userId,
    sessionStatus,
    error,
    refreshStatus,
    initializeConnection,
    disconnectSession
  };

  return (
    <WhatsAppContext.Provider value={contextValue}>
      {children}
    </WhatsAppContext.Provider>
  );
}

export function useWhatsApp() {
  const context = useContext(WhatsAppContext);
  // Since we provide default values in context creation, this should never happen
  // But keeping the check for type safety
  if (!context) {
    throw new Error("useWhatsApp must be used within a WhatsAppProvider");
  }
  return context;
}
