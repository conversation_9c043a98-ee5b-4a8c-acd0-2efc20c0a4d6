import { Boom } from '@hapi/boom';
import path from 'path';
import fs from 'fs';
import QRCode from 'qrcode';
import P from 'pino';
import { webSocketService } from './websocket';
import { storage } from '../storage';

const { 
  default: makeWASocket, 
  useMultiFileAuthState, 
  DisconnectReason,
  ConnectionState
} = require('@whiskeysockets/baileys');

interface WhatsAppUserSession {
  userId: number;
  sock?: any;
  qr?: string;
  isConnected: boolean;
  phoneNumber?: string;
  sessionPath: string;
  connectionAttempts: number;
  lastActivity: Date;
  activeChats: Set<string>;
}

class MultiUserWhatsAppService {
  private sessions: Map<number, WhatsAppUserSession> = new Map();
  private sessionsDir = process.env.SESSIONS_DIR || path.join(process.cwd(), 'sessions');
  private maxConcurrentUsers = 30;
  private reconnectDelays = [1000, 2000, 5000, 10000, 30000]; // Progressive delays

  constructor() {
    if (!fs.existsSync(this.sessionsDir)) {
      fs.mkdirSync(this.sessionsDir, { recursive: true });
    }
    
    // Cleanup disconnected sessions every 5 minutes
    setInterval(() => this.cleanupInactiveSessions(), 5 * 60 * 1000);
    
    console.log(`🏠 MULTI-USER WHATSAPP SERVICE INITIALIZED`);
    console.log(`📱 Suporte para ${this.maxConcurrentUsers} vendedores simultâneos`);
  }

  async initializeUserSession(userId: number): Promise<{ success: boolean; qr?: string; error?: string; phoneNumber?: string }> {
    try {
      console.log(`👤 INICIANDO SESSÃO WHATSAPP PARA VENDEDOR ${userId}`);
      
      // Verificar se usuário já tem sessão ativa
      if (this.sessions.has(userId)) {
        const session = this.sessions.get(userId)!;
        if (session.isConnected && session.sock && session.phoneNumber) {
          console.log(`✅ Vendedor ${userId} já conectado: ${session.phoneNumber}`);
          return { 
            success: true, 
            phoneNumber: session.phoneNumber 
          };
        } else {
          console.log(`🔄 Vendedor ${userId} tem sessão mas não está conectado: isConnected=${session.isConnected}, sock=${!!session.sock}, phone=${session.phoneNumber}`);
          // Remover sessão inválida
          this.sessions.delete(userId);
        }
      }

      // Verificar limite de usuários simultâneos
      const activeUsers = Array.from(this.sessions.values()).filter(s => s.isConnected).length;
      if (activeUsers >= this.maxConcurrentUsers) {
        return { 
          success: false, 
          error: `Limite de ${this.maxConcurrentUsers} usuários simultâneos atingido` 
        };
      }

      const sessionPath = path.join(this.sessionsDir, `user_${userId}`);
      if (!fs.existsSync(sessionPath)) {
        fs.mkdirSync(sessionPath, { recursive: true });
      }

      const session: WhatsAppUserSession = {
        userId,
        isConnected: false,
        sessionPath,
        connectionAttempts: 0,
        lastActivity: new Date(),
        activeChats: new Set(),
      };

      this.sessions.set(userId, session);

      // Inicializar conexão WhatsApp
      await this.createWhatsAppConnection(session);

      // Aguardar um pouco para o QR code ser gerado
      await new Promise(resolve => setTimeout(resolve, 3000));

      return { 
        success: true, 
        qr: session.qr,
        phoneNumber: session.phoneNumber
      };

    } catch (error) {
      console.error(`❌ ERRO AO INICIALIZAR SESSÃO VENDEDOR ${userId}:`, error);
      
      // Se a sessão foi criada (mesmo com erro), retornar sucesso
      if (this.sessions.has(userId)) {
        const session = this.sessions.get(userId)!;
        console.log(`✅ Sessão criada para vendedor ${userId}, aguardando QR code...`);
        return { 
          success: true, 
          qr: session.qr || 'QR code sendo gerado no console...',
          phoneNumber: session.phoneNumber
        };
      }
      
      return { 
        success: false, 
        error: 'Falha ao criar sessão WhatsApp'
      };
    }
  }

  private async createWhatsAppConnection(session: WhatsAppUserSession): Promise<void> {
    try {
      const { state, saveCreds } = await useMultiFileAuthState(session.sessionPath);

      // Configurações otimizadas para melhor compatibilidade
      const sock = makeWASocket({
        auth: state,
        printQRInTerminal: false,
        logger: P({ level: 'silent' }),
        browser: ['Casa das Camisetas CRM', 'Chrome', '1.0.0'],
        generateHighQualityLinkPreview: true,
        markOnlineOnConnect: false, // Mudança: não marcar como online automaticamente
        keepAliveIntervalMs: 30000,
        defaultQueryTimeoutMs: 60000,
        connectTimeoutMs: 60000,
        qrTimeout: 60000,
        // Configurações adicionais para melhor estabilidade
        retryRequestDelayMs: 250,
        maxMsgRetryCount: 5,
        getMessage: async (key) => {
          return { conversation: 'hello' };
        }
      });

      session.sock = sock;

      // Event handlers
      sock.ev.on('connection.update', (update: Partial<ConnectionState>) => {
        this.handleConnectionUpdate(update, session);
      });

      sock.ev.on('creds.update', saveCreds);

      sock.ev.on('messages.upsert', (messageUpdate) => {
        this.handleIncomingMessages(session, messageUpdate);
      });

      sock.ev.on('chats.update', (chatUpdates) => {
        this.handleChatUpdates(session, chatUpdates);
      });

      console.log(`🔗 Conexão WhatsApp criada para vendedor ${session.userId}`);

    } catch (error) {
      console.error(`❌ Erro ao criar conexão WhatsApp para vendedor ${session.userId}:`, error);
      throw error;
    }
  }

  private async handleConnectionUpdate(update: Partial<ConnectionState>, session: WhatsAppUserSession) {
    const { connection, lastDisconnect, qr } = update;

    if (qr) {
      console.log(`📱 QR CODE GERADO PARA VENDEDOR ${session.userId}`);
      console.log('🔐 QR CODE PARA AUTENTICAÇÃO REAL DO WHATSAPP');
      console.log('📱 ESCANEIE COM SUA CONTA REAL DO WHATSAPP:');
      console.log('⚠️  Este QR Code conecta sua conta real ao sistema. Nada será simulado.');
      console.log('📋 Por favor, escaneie com o aplicativo do WhatsApp...');
      
      try {
        // Gerar QR Code como string para o terminal
        const qrcode = require('qrcode-terminal');
        console.log('\n🔐 QR CODE PARA ESCANEAR:');
        console.log('=====================================');
        qrcode.generate(qr, { small: true });
        console.log('=====================================');
        console.log('📱 Escaneie este QR Code com seu WhatsApp');
        console.log('⚠️  Este QR Code conecta sua conta REAL ao sistema');
        console.log('=====================================\n');
      } catch (qrError) {
        console.warn('⚠️ Não foi possível exibir QR no terminal:', qrError);
      }
      
      try {
        const qrDataURL = await QRCode.toDataURL(qr, {
          width: 400,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        });
        session.qr = qrDataURL;
        
        // Enviar QR Code via WebSocket
        webSocketService.sendToUser(session.userId, 'whatsapp_qr', { 
          qr: qrDataURL,
          userId: session.userId 
        });

        // QR code gerado - disponível na interface
        console.log(`📱 QR Code gerado para vendedor ${session.userId}`);

        // Salvar QR como arquivo PNG
        const qrPath = path.join(this.sessionsDir, `qr_${session.userId}.png`);
        await QRCode.toFile(qrPath, qr, { 
          width: 400,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        });

      } catch (error) {
        console.error(`❌ Erro ao gerar QR Code para vendedor ${session.userId}:`, error);
      }
    }

    if (connection === 'close') {
      console.log(`🔌 Conexão fechada para vendedor ${session.userId}`);
      session.isConnected = false;
      
      const shouldReconnect = (lastDisconnect?.error as Boom)?.output?.statusCode !== DisconnectReason.loggedOut;
      
      if (shouldReconnect && session.connectionAttempts < 5) {
        console.log(`🔄 Reagendando reconexão para vendedor ${session.userId}`);
        this.scheduleReconnection(session);
      } else {
        console.log(`❌ Vendedor ${session.userId} deslogado ou muitas tentativas de reconexão`);
        await this.updateUserStatus(session.userId, false);
      }
    } else if (connection === 'open') {
      console.log(`✅ VENDEDOR ${session.userId} CONECTADO AO WHATSAPP`);
      
      session.isConnected = true;
      session.connectionAttempts = 0;
      session.lastActivity = new Date();
      
      if (session.sock?.user) {
        session.phoneNumber = session.sock.user.id;
        console.log(`📞 Telefone conectado vendedor ${session.userId}: ${session.phoneNumber}`);
        
        // Force session persistence - CRITICAL FIX
        this.sessions.set(session.userId, { ...session });
        
        await this.updateUserStatus(session.userId, true, session.phoneNumber);
        
        webSocketService.sendToUser(session.userId, 'whatsapp_connected', {
          userId: session.userId,
          phoneNumber: session.phoneNumber,
          connectedAt: new Date(),
          isConnected: true
        });
        
        console.log(`🎉 VENDEDOR ${session.userId} TOTALMENTE CONECTADO E OPERACIONAL`);
      }
    } else if (connection === 'connecting') {
      console.log(`🔄 Vendedor ${session.userId} conectando...`);
    }
  }

  private scheduleReconnection(session: WhatsAppUserSession) {
    const delay = this.reconnectDelays[Math.min(session.connectionAttempts, this.reconnectDelays.length - 1)];
    session.connectionAttempts++;
    
    console.log(`⏰ Reconexão agendada para vendedor ${session.userId} em ${delay}ms (tentativa ${session.connectionAttempts})`);
    
    setTimeout(async () => {
      try {
        await this.createWhatsAppConnection(session);
      } catch (error) {
        console.error(`❌ Erro na reconexão do vendedor ${session.userId}:`, error);
      }
    }, delay);
  }

  private async handleIncomingMessages(session: WhatsAppUserSession, messageUpdate: any) {
    try {
      const messages = messageUpdate.messages;
      
      for (const message of messages) {
        if (message.key.fromMe) continue; // Ignorar mensagens próprias
        
        const chatId = message.key.remoteJid;
        const messageText = message.message?.conversation || 
                           message.message?.extendedTextMessage?.text || 
                           '[Mídia]';
        
        console.log(`📨 Nova mensagem para vendedor ${session.userId} de ${chatId}: ${messageText}`);
        
        // Adicionar chat aos ativos
        session.activeChats.add(chatId);
        session.lastActivity = new Date();
        
        // Salvar mensagem no banco
        await storage.saveMessage({
          chatId,
          messageId: message.key.id,
          fromMe: false,
          content: messageText,
          timestamp: new Date(message.messageTimestamp * 1000),
          userId: session.userId
        });
        
        // Notificar via WebSocket
        webSocketService.sendToUser(session.userId, 'new_message', {
          chatId,
          message: messageText,
          timestamp: new Date(message.messageTimestamp * 1000),
          fromMe: false
        });
      }
    } catch (error) {
      console.error(`❌ Erro ao processar mensagens recebidas para vendedor ${session.userId}:`, error);
    }
  }

  private async handleChatUpdates(session: WhatsAppUserSession, chatUpdates: any) {
    try {
      for (const chat of chatUpdates) {
        session.activeChats.add(chat.id);
        
        // Notificar via WebSocket sobre atualização do chat
        webSocketService.sendToUser(session.userId, 'chat_update', {
          chatId: chat.id,
          name: chat.name,
          unreadCount: chat.unreadCount
        });
      }
    } catch (error) {
      console.error(`❌ Erro ao processar atualizações de chat para vendedor ${session.userId}:`, error);
    }
  }

  private async updateUserStatus(userId: number, isConnected: boolean, phoneNumber?: string) {
    try {
      await storage.updateSessionStatus(userId, isConnected, phoneNumber);
    } catch (error) {
      console.error(`❌ Erro ao atualizar status do usuário ${userId}:`, error);
    }
  }

  private cleanupInactiveSessions() {
    const now = new Date();
    const inactiveThreshold = 30 * 60 * 1000; // 30 minutos
    
    for (const [userId, session] of this.sessions.entries()) {
      if (!session.isConnected && (now.getTime() - session.lastActivity.getTime()) > inactiveThreshold) {
        console.log(`🧹 Limpando sessão inativa do vendedor ${userId}`);
        this.sessions.delete(userId);
      }
    }
  }

  // Métodos públicos para uso nas rotas
  getAllUsersStatus() {
    const status: any = {};
    
    for (const [userId, session] of this.sessions.entries()) {
      status[userId] = {
        isConnected: session.isConnected,
        phoneNumber: session.phoneNumber,
        activeChats: session.activeChats.size,
        lastActivity: session.lastActivity
      };
    }
    
    return status;
  }

  getUserStatus(userId: number) {
    const session = this.sessions.get(userId);
    
    if (!session) {
      return {
        isConnected: false,
        activeChats: 0
      };
    }
    
    return {
      isConnected: session.isConnected,
      phoneNumber: session.phoneNumber,
      activeChats: session.activeChats.size,
      lastActivity: session.lastActivity
    };
  }

  async disconnectUser(userId: number) {
    const session = this.sessions.get(userId);
    
    if (session && session.sock) {
      await session.sock.logout();
      session.isConnected = false;
      this.sessions.delete(userId);
      
      console.log(`👋 Vendedor ${userId} desconectado`);
    }
  }

  async getChats(userId: number) {
    const session = this.sessions.get(userId);
    
    if (!session || !session.sock || !session.isConnected) {
      return [];
    }
    
    try {
      const chats = await session.sock.getChats();
      return chats.map((chat: any) => ({
        id: chat.id,
        name: chat.name || chat.id,
        lastMessage: chat.lastMessage,
        unreadCount: chat.unreadCount || 0
      }));
    } catch (error) {
      console.error(`❌ Erro ao buscar chats do vendedor ${userId}:`, error);
      return [];
    }
  }

  async getMessages(userId: number, chatId: string, limit: number = 50) {
    const session = this.sessions.get(userId);
    
    if (!session || !session.sock || !session.isConnected) {
      return [];
    }
    
    try {
      const messages = await session.sock.fetchMessagesFromWA(chatId, limit);
      return messages.map((msg: any) => ({
        id: msg.key.id,
        content: msg.message?.conversation || msg.message?.extendedTextMessage?.text || '[Mídia]',
        fromMe: msg.key.fromMe,
        timestamp: new Date(msg.messageTimestamp * 1000)
      }));
    } catch (error) {
      console.error(`❌ Erro ao buscar mensagens do vendedor ${userId}:`, error);
      return [];
    }
  }

  async sendMessage(userId: number, chatId: string, message: string) {
    const session = this.sessions.get(userId);
    
    if (!session || !session.sock || !session.isConnected) {
      throw new Error('Vendedor não conectado');
    }
    
    try {
      const result = await session.sock.sendMessage(chatId, { text: message });
      
      // Salvar mensagem enviada no banco
      await storage.saveMessage({
        chatId,
        messageId: result.key.id,
        fromMe: true,
        content: message,
        timestamp: new Date(),
        userId
      });
      
      console.log(`📤 Mensagem enviada pelo vendedor ${userId} para ${chatId}: ${message}`);
      
      return { success: true, messageId: result.key.id };
    } catch (error) {
      console.error(`❌ Erro ao enviar mensagem do vendedor ${userId}:`, error);
      throw error;
    }
  }
}

export const multiUserWhatsAppService = new MultiUserWhatsAppService();

