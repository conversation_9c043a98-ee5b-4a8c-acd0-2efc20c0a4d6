import { useState } from "react";
import { Search, X, Send, Users, User, CheckCircle } from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";

interface ForwardMessageProps {
  isOpen: boolean;
  onClose: () => void;
  messageId: string;
  messageContent: string;
  userId: string;
  whatsappNumber: string;
}

interface Contact {
  id: string;
  name: string;
  phone: string;
  isGroup: boolean;
  lastSeen?: string;
  profilePic?: string;
}

export default function ForwardMessage({ 
  isOpen, 
  onClose, 
  messageId, 
  messageContent, 
  userId,
  whatsappNumber 
}: ForwardMessageProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);
  const [customMessage, setCustomMessage] = useState("");
  
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get contacts list
  const { data: contacts = [] } = useQuery({
    queryKey: ['/api/whatsapp/contacts', whatsappNumber],
    queryFn: async () => {
      const response = await fetch(`/api/whatsapp/contacts?whatsappNumber=${whatsappNumber}`);
      if (!response.ok) throw new Error('Failed to fetch contacts');
      return response.json();
    },
    enabled: isOpen,
  });

  // Forward message mutation
  const forwardMutation = useMutation({
    mutationFn: async ({ recipients, message }: { recipients: string[], message: string }) => {
      const response = await fetch('/api/whatsapp/forward', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId,
          messageId,
          recipients,
          customMessage: message,
          originalContent: messageContent
        }),
      });
      
      if (!response.ok) throw new Error('Failed to forward message');
      return response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Mensagem encaminhada",
        description: `Enviada para ${selectedContacts.length} contato(s)`,
      });
      
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['/api/whatsapp/messages'] });
      queryClient.invalidateQueries({ queryKey: ['/api/audit/logs'] });
      
      onClose();
      setSelectedContacts([]);
      setCustomMessage("");
      setSearchQuery("");
    },
    onError: () => {
      toast({
        title: "Erro no encaminhamento",
        description: "Não foi possível encaminhar a mensagem",
        variant: "destructive",
      });
    },
  });

  const filteredContacts = contacts.filter((contact: Contact) =>
    contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contact.phone.includes(searchQuery)
  );

  const toggleContact = (contactId: string) => {
    setSelectedContacts(prev => 
      prev.includes(contactId) 
        ? prev.filter(id => id !== contactId)
        : [...prev, contactId]
    );
  };

  const handleForward = () => {
    if (selectedContacts.length === 0) {
      toast({
        title: "Selecione contatos",
        description: "Você deve selecionar pelo menos um contato",
        variant: "destructive",
      });
      return;
    }

    const finalMessage = customMessage.trim() || messageContent;
    forwardMutation.mutate({
      recipients: selectedContacts,
      message: finalMessage
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[80vh] mx-4 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <Send className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Encaminhar Mensagem
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Original Message Preview */}
        <div className="p-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
          <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">Mensagem original:</div>
          <div className="bg-white dark:bg-gray-600 p-3 rounded-lg border-l-4 border-blue-500">
            <p className="text-gray-900 dark:text-white">
              {messageContent.length > 100 
                ? `${messageContent.substring(0, 100)}...` 
                : messageContent
              }
            </p>
          </div>
        </div>

        {/* Search */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Buscar contatos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>
          
          {selectedContacts.length > 0 && (
            <div className="mt-3 text-sm text-gray-600 dark:text-gray-400">
              {selectedContacts.length} contato(s) selecionado(s)
            </div>
          )}
        </div>

        {/* Contacts List */}
        <div className="flex-1 overflow-y-auto p-4">
          {filteredContacts.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Users className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <p>Nenhum contato encontrado</p>
            </div>
          ) : (
            <div className="space-y-2">
              {filteredContacts.map((contact: Contact) => (
                <div
                  key={contact.id}
                  onClick={() => toggleContact(contact.id)}
                  className={`p-3 rounded-lg border cursor-pointer transition-all ${
                    selectedContacts.includes(contact.id)
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                      {contact.isGroup ? (
                        <Users className="w-5 h-5 text-gray-600" />
                      ) : (
                        <User className="w-5 h-5 text-gray-600" />
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 dark:text-white">
                        {contact.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {contact.phone}
                        {contact.isGroup && <span className="ml-2">(Grupo)</span>}
                      </div>
                    </div>
                    
                    {selectedContacts.includes(contact.id) && (
                      <CheckCircle className="w-5 h-5 text-blue-600" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Custom Message */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Mensagem personalizada (opcional)
          </label>
          <textarea
            value={customMessage}
            onChange={(e) => setCustomMessage(e.target.value)}
            placeholder="Adicione uma mensagem personalizada..."
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            rows={3}
          />
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Se não preenchido, será enviada a mensagem original
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
          >
            Cancelar
          </button>
          
          <button
            onClick={handleForward}
            disabled={forwardMutation.isPending || selectedContacts.length === 0}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            <Send className="w-4 h-4" />
            <span>
              {forwardMutation.isPending ? 'Encaminhando...' : 'Encaminhar'}
            </span>
          </button>
        </div>
      </div>
    </div>
  );
}