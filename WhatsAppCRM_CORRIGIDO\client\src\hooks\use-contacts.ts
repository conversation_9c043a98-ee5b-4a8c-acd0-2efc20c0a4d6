import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "./use-toast";

interface Contact {
  id: number;
  whatsappNumber: string;
  name: string;
  phone: string;
  email?: string;
  company?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface CreateContactRequest {
  whatsappNumber: string;
  name: string;
  phone: string;
  email?: string;
  company?: string;
  notes?: string;
}

interface UpdateContactRequest {
  name?: string;
  phone?: string;
  email?: string;
  company?: string;
  notes?: string;
}

interface WhatsAppContact {
  id: string;
  name: string;
  phone: string;
  profilePicUrl?: string;
  isGroup: boolean;
  unreadCount: number;
  lastMessage?: {
    body: string;
    timestamp: number;
    fromMe: boolean;
  };
  timestamp: number;
}

export function useContacts(whatsappNumber?: string) {
  return useQuery({
    queryKey: ['/api/clients', whatsappNumber],
    queryFn: async () => {
      if (!whatsappNumber) return [];
      
      const response = await fetch(`/api/clients/${encodeURIComponent(whatsappNumber)}`);
      if (!response.ok) {
        throw new Error('Falha ao carregar contatos');
      }
      return response.json() as Promise<Contact[]>;
    },
    enabled: !!whatsappNumber,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

export function useContact(contactId: number, whatsappNumber?: string) {
  return useQuery({
    queryKey: ['/api/clients', whatsappNumber, contactId],
    queryFn: async () => {
      if (!whatsappNumber || !contactId) return null;
      
      const response = await fetch(`/api/clients/${contactId}?whatsappNumber=${encodeURIComponent(whatsappNumber)}`);
      if (!response.ok) {
        if (response.status === 404) return null;
        throw new Error('Falha ao carregar contato');
      }
      return response.json() as Promise<Contact>;
    },
    enabled: !!whatsappNumber && !!contactId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useWhatsAppChats(userId?: string) {
  return useQuery({
    queryKey: ['/api/whatsapp/chats', userId],
    queryFn: async () => {
      if (!userId) return [];
      
      const response = await fetch(`/api/whatsapp/chats?userId=${encodeURIComponent(userId)}`);
      if (!response.ok) {
        throw new Error('Falha ao carregar conversas');
      }
      return response.json() as Promise<WhatsAppContact[]>;
    },
    enabled: !!userId,
    refetchInterval: 10000, // Refetch every 10 seconds
    staleTime: 5000,
  });
}

export function useCreateContact() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (contact: CreateContactRequest) => {
      const response = await fetch('/api/clients', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contact),
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Erro desconhecido' }));
        throw new Error(error.message || 'Falha ao criar contato');
      }

      return response.json() as Promise<Contact>;
    },
    onSuccess: (newContact) => {
      // Update cache optimistically
      queryClient.setQueryData(
        ['/api/clients', newContact.whatsappNumber],
        (oldData: Contact[] | undefined) => {
          if (!oldData) return [newContact];
          return [...oldData, newContact];
        }
      );

      queryClient.invalidateQueries({ 
        queryKey: ['/api/clients'],
      });

      toast({
        title: "Contato criado",
        description: "Novo contato adicionado com sucesso",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao criar contato",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useUpdateContact() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ 
      id, 
      whatsappNumber, 
      updates 
    }: { 
      id: number; 
      whatsappNumber: string; 
      updates: UpdateContactRequest 
    }) => {
      const response = await fetch(`/api/clients/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ...updates, whatsappNumber }),
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Erro desconhecido' }));
        throw new Error(error.message || 'Falha ao atualizar contato');
      }

      return response.json() as Promise<Contact>;
    },
    onSuccess: (updatedContact) => {
      // Update specific contact in cache
      queryClient.setQueryData(
        ['/api/clients', updatedContact.whatsappNumber, updatedContact.id],
        updatedContact
      );

      // Update contact in list cache
      queryClient.setQueryData(
        ['/api/clients', updatedContact.whatsappNumber],
        (oldData: Contact[] | undefined) => {
          if (!oldData) return [updatedContact];
          return oldData.map(contact => 
            contact.id === updatedContact.id ? updatedContact : contact
          );
        }
      );

      queryClient.invalidateQueries({ 
        queryKey: ['/api/clients'],
      });

      toast({
        title: "Contato atualizado",
        description: "Informações do contato atualizadas com sucesso",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao atualizar contato",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useDeleteContact() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ 
      id, 
      whatsappNumber 
    }: { 
      id: number; 
      whatsappNumber: string; 
    }) => {
      const response = await fetch(`/api/clients/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ whatsappNumber }),
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Erro desconhecido' }));
        throw new Error(error.message || 'Falha ao deletar contato');
      }

      return { id, whatsappNumber };
    },
    onSuccess: ({ id, whatsappNumber }) => {
      // Remove contact from list cache
      queryClient.setQueryData(
        ['/api/clients', whatsappNumber],
        (oldData: Contact[] | undefined) => {
          if (!oldData) return [];
          return oldData.filter(contact => contact.id !== id);
        }
      );

      // Remove specific contact cache
      queryClient.removeQueries({
        queryKey: ['/api/clients', whatsappNumber, id],
      });

      queryClient.invalidateQueries({ 
        queryKey: ['/api/clients'],
      });

      toast({
        title: "Contato removido",
        description: "Contato deletado com sucesso",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao deletar contato",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useSaveWhatsAppContact() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ 
      userId, 
      contactPhone, 
      contactName 
    }: { 
      userId: string; 
      contactPhone: string; 
      contactName: string; 
    }) => {
      const response = await fetch('/api/whatsapp/save-contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, contactPhone, contactName }),
      });

      if (!response.ok) {
        throw new Error('Falha ao salvar contato');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ 
        queryKey: ['/api/whatsapp/chats'],
      });

      toast({
        title: "Contato salvo",
        description: "Contato salvo no WhatsApp com sucesso",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao salvar contato",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}