# Casa das Camisetas - WhatsApp CRM
## Tutorial Completo de Instalação no Dell PowerEdge R420
### Acesso Multi-usuário via Rede Local

---

## 🖥️ Especificações do Servidor

**Dell PowerEdge R420** é ideal para este CRM:
- **CPU**: Intel Xeon E5-2400 (suporte para até 30 usuários simultâneos)
- **RAM**: <PERSON><PERSON><PERSON> 8GB (recomendado 16GB+ para melhor performance)
- **Storage**: SSD recomendado para melhor I/O do banco de dados
- **Network**: Gigabit Ethernet para acesso LAN rápido

---

## 📋 Pré-requisitos

### Sistema Operacional
- **Ubuntu Server 22.04 LTS** (recomendado)
- **Debian 11+** (alternativo)
- Acesso SSH ou console físico
- Usuário com privilégios sudo

### Rede Local
- Servidor conectado na rede local da empresa
- IP fixo configurado (ex: *************)
- Porta 80 liberada no firewall
- Acesso à internet para downloads iniciais

---

## 🚀 Instalação Automática

### 1. Preparar os Arquivos

```bash
# 1. Baixar ou transferir arquivos do projeto para o servidor
scp -r whatsappcrm/ usuario@*************:/home/<USER>/

# 2. Conectar no servidor via SSH
ssh usuario@*************

# 3. Navegar para o diretório
cd /home/<USER>/

# 4. Dar permissão ao script
chmod +x install.sh
```

### 2. Executar Instalação

```bash
# Executar o script de instalação
./install.sh
```

**O script irá:**
- ✅ Instalar Node.js 20, PostgreSQL 15, Nginx
- ✅ Configurar banco de dados com credenciais seguras
- ✅ Instalar todas as dependências do projeto
- ✅ Configurar variáveis de ambiente
- ✅ Fazer build do projeto
- ✅ Configurar PM2 para execução em cluster
- ✅ Configurar Nginx como proxy reverso
- ✅ Configurar firewall para acesso LAN
- ✅ Configurar backups automáticos
- ✅ Configurar monitoramento

### 3. Verificar Instalação

Ao final da instalação, você verá:

```
🎉 CASA DAS CAMISETAS - WHATSAPP CRM INSTALADO COM SUCESSO!
🌍 URLS DE ACESSO:
   • Frontend: http://*************
   • API: http://*************/api
```

---

## 🌐 Configuração de Rede Local

### Acessos dos Usuários

Os vendedores acessarão via navegador nos computadores da rede:

**URL de Acesso**: `http://*************`

### Exemplo de IPs por Departamento:

```
🖥️ Servidor PowerEdge R420: *************
💼 Vendedores:
   • Vendedor 1: http://************* (PC: ************)
   • Vendedor 2: http://************* (PC: ************)
   • Vendedor 3: http://************* (PC: ************)
   ...
   • Vendedor 30: http://************* (PC: ************)

👨‍💼 Gerentes: http://*************/dashboard
📱 Mobile: http://************* (via WiFi da empresa)
```

---

## 📱 Configuração do WhatsApp Multi-Device

### 1. Primeiro Acesso
1. Abra `http://*************` no navegador
2. Clique em "Conectar WhatsApp"
3. Escaneie o QR Code com o WhatsApp Business
4. Aguarde confirmação de conexão

### 2. Multi-Usuários WhatsApp
- **Suporte**: Até 30 vendedores simultâneos
- **Sessões**: Uma por vendedor com WhatsApp próprio
- **Reconexão**: Automática em caso de queda

### 3. Funcionalidades Disponíveis
- ✅ Envio/recebimento de mensagens em tempo real
- ✅ Histórico completo de conversas
- ✅ Suporte a mídias (imagens, áudios, documentos)
- ✅ Grupos do WhatsApp integrados
- ✅ Status de leitura e online
- ✅ Backup automático das sessões

---

## 👥 Gestão de Usuários

### Sistema de Permissões

```
🔴 Admin Master:
   • Acesso total ao sistema
   • Gerenciamento de usuários
   • Configurações do servidor
   
🟡 Supervisores:
   • Dashboard analítico
   • Relatórios de vendas
   • Monitoramento de equipe
   
🟢 Vendedores:
   • WhatsApp próprio
   • CRM de leads
   • Kanban de tarefas
```

### Adicionar Usuários
1. Acesse: `http://*************/admin`
2. Clique em "Adicionar Usuário"
3. Defina: Nome, Email, Tipo de Acesso
4. Envie credenciais por email

---

## 🔧 Administração do Sistema

### Comandos Essenciais

```bash
# Status da aplicação
pm2 status

# Ver logs em tempo real
pm2 logs casa-camisetas-crm

# Reiniciar sistema
pm2 restart casa-camisetas-crm

# Monitor visual
pm2 monit

# Status do Nginx
sudo systemctl status nginx

# Status do PostgreSQL
sudo systemctl status postgresql
```

### Diretórios Importantes

```
📁 /opt/whatsappcrm/          # Projeto principal
📁 /opt/whatsappcrm/sessions/ # Sessões WhatsApp
📁 /opt/whatsappcrm/logs/     # Logs do sistema
📁 /opt/whatsappcrm/uploads/  # Arquivos enviados
📁 /opt/backups/              # Backups automáticos
```

### Arquivos de Configuração

```bash
# Variáveis de ambiente
/opt/whatsappcrm/.env

# Credenciais do banco
/opt/whatsappcrm/.credentials

# Configuração PM2
/opt/whatsappcrm/ecosystem.config.js

# Nginx
/etc/nginx/sites-available/whatsappcrm
```

---

## 🔒 Segurança e Backup

### Firewall Configurado
```
✅ SSH (22): Apenas rede local
✅ HTTP (80): Rede local liberada
✅ HTTPS (443): Preparado para certificado
✅ App (5000): Apenas localhost
```

### Backup Automático
- **Frequência**: Diário às 2:00
- **Retenção**: 7 dias
- **Inclui**: Banco de dados + sessões WhatsApp
- **Local**: `/opt/backups/`

### Monitoramento
- **Verificação**: A cada 5 minutos
- **Alerta**: Uso de memória > 85%
- **Alerta**: Espaço em disco < 15%
- **Logs**: `/opt/whatsappcrm/logs/monitor.log`

---

## 🛠️ Troubleshooting

### Problemas Comuns

#### 1. Sistema não carrega
```bash
# Verificar se está rodando
pm2 status

# Verificar logs
pm2 logs casa-camisetas-crm

# Reiniciar se necessário
pm2 restart casa-camisetas-crm
```

#### 2. WhatsApp desconecta
```bash
# Limpar sessões
rm -rf /opt/whatsappcrm/sessions/*

# Reiniciar aplicação
pm2 restart casa-camisetas-crm

# Reconectar via QR Code no navegador
```

#### 3. Lentidão no acesso
```bash
# Verificar recursos
htop

# Verificar logs do Nginx
sudo tail -f /var/log/nginx/error.log

# Verificar conexões de rede
netstat -an | grep :80
```

#### 4. Erro de banco de dados
```bash
# Verificar PostgreSQL
sudo systemctl status postgresql

# Testar conexão
PGPASSWORD=$(grep PGPASSWORD /opt/whatsappcrm/.env | cut -d'=' -f2) \
psql -h localhost -U whatsappuser -d whatsappcrm -c "SELECT version();"

# Reiniciar PostgreSQL se necessário
sudo systemctl restart postgresql
```

---

## 📊 Monitoramento e Performance

### Dashboard de Sistema
Acesse: `http://*************/admin/system`

**Métricas Disponíveis:**
- 📈 Usuários conectados em tempo real
- 📱 Sessões WhatsApp ativas
- 💬 Mensagens processadas/hora
- 🎯 Leads convertidos
- 📊 Performance do servidor

### Otimizações PowerEdge R420

```bash
# Configurações recomendadas no .env
MAX_OLD_SPACE_SIZE=4096          # 4GB para Node.js
UV_THREADPOOL_SIZE=16            # Threads para I/O
PM2_INSTANCES=2                  # Cluster com 2 instâncias
DATABASE_POOL_SIZE=20            # Pool do PostgreSQL
```

---

## 🚀 Expansão e Crescimento

### Aumentar Capacidade

**Para mais de 30 usuários:**
1. Aumentar RAM para 32GB
2. Configurar cluster com mais instâncias PM2
3. Otimizar PostgreSQL para mais conexões
4. Considerar load balancer se necessário

**Configuração para 50+ usuários:**
```bash
# Editar ecosystem.config.js
instances: 4  # 4 instâncias Node.js

# Editar postgresql.conf
max_connections = 200
shared_buffers = 2GB
effective_cache_size = 6GB
```

---

## 📞 Suporte Técnico

### Logs Importantes
```bash
# Aplicação principal
tail -f /opt/whatsappcrm/logs/combined.log

# Erro da aplicação
tail -f /opt/whatsappcrm/logs/err.log

# Monitor do sistema
tail -f /opt/whatsappcrm/logs/monitor.log

# Nginx
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# Sistema
journalctl -f -u pm2-usuario
```

### Comandos de Diagnóstico
```bash
# Verificar portas
sudo netstat -tulpn | grep -E ':(80|5000|5432)'

# Verificar processos
ps aux | grep -E '(node|postgres|nginx)'

# Verificar espaço
df -h

# Verificar memória
free -h

# Verificar conectividade
curl -I http://localhost:5000
```

---

## ✅ Checklist de Instalação

### Antes da Instalação
- [ ] Ubuntu Server 22.04 instalado
- [ ] Usuário com sudo configurado
- [ ] Rede local configurada
- [ ] IP fixo definido
- [ ] Arquivos do projeto disponíveis

### Durante a Instalação
- [ ] Script install.sh executado
- [ ] Sem erros no processo
- [ ] Banco de dados conectando
- [ ] PM2 iniciado com sucesso
- [ ] Nginx respondendo

### Após a Instalação
- [ ] Site carregando em http://IP_DO_SERVIDOR
- [ ] QR Code do WhatsApp aparecendo
- [ ] Login de usuário funcionando
- [ ] Dashboard carregando
- [ ] Backup automático configurado

### Teste Multi-usuário
- [ ] Acesso simultâneo de 5+ usuários
- [ ] WhatsApp funcionando para cada vendedor
- [ ] Mensagens em tempo real
- [ ] Kanban sincronizando entre usuários
- [ ] Performance estável

---

## 🎯 Resultado Final

Com a instalação completa, você terá:

### 🏢 **Casa das Camisetas CRM Completo**
- ✅ **30 vendedores simultâneos** via rede local
- ✅ **WhatsApp Multi-Device** com Baileys v2
- ✅ **CRM completo** com gestão de leads
- ✅ **Kanban visual** para organização
- ✅ **Dashboard analítico** para supervisores
- ✅ **Backup automático** diário
- ✅ **Monitoramento 24/7** do sistema
- ✅ **Alta disponibilidade** com PM2 cluster

### 🌐 **Acesso Simples**
Todos os usuários acessam via navegador:
**`http://*************`**

### 📱 **WhatsApp Real**
- Cada vendedor com seu próprio WhatsApp Business
- Mensagens em tempo real
- Histórico completo de conversas
- Suporte a mídias e grupos

---

*Sistema 100% funcional no Dell PowerEdge R420 para rede local empresarial*