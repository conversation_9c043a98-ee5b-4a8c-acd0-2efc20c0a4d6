import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Smartphone, QrCode, CheckCircle, Users } from "lucide-react";

export default function LoginFixed() {
  const [userId, setUserId] = useState<string>(() => {
    const saved = localStorage.getItem('whatsapp_user_id');
    return saved || '';
  });
  const [isGeneratingQR, setIsGeneratingQR] = useState(false);
  const [qrCodeData, setQrCodeData] = useState<string>('');
  const { toast } = useToast();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (userId) {
      localStorage.setItem('whatsapp_user_id', userId);
    }
  }, [userId]);

  const { data: status, refetch: refetchStatus, isLoading } = useQuery({
    queryKey: ["/api/whatsapp/status", userId],
    queryFn: async () => {
      if (!userId) return null;
      const response = await fetch(`/api/whatsapp/status/${userId}`);
      if (!response.ok) throw new Error('Failed to check status');
      return response.json();
    },
    refetchInterval: (data: any) => data?.isConnected ? false : 3000,
    retry: 3,
    enabled: !!userId,
  });

  const { data: allUsers } = useQuery({
    queryKey: ["/api/whatsapp/clients"],
    queryFn: async () => {
      const response = await fetch(`/api/whatsapp/clients`);
      if (!response.ok) throw new Error('Failed to get clients');
      return response.json();
    },
    refetchInterval: 5000,
  });

  const connectMutation = useMutation({
    mutationFn: async () => {
      setIsGeneratingQR(true);
      const response = await fetch(`/api/whatsapp/connect/${userId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      if (!response.ok) throw new Error('Failed to connect');
      return response.json();
    },
    onSuccess: (data) => {
      setIsGeneratingQR(false);
      if (data.success) {
        if (data.qr) {
          setQrCodeData(data.qr);
          toast({
            title: "QR Code Gerado",
            description: "Escaneie o QR Code com seu WhatsApp para conectar",
          });
        } else {
          localStorage.setItem('whatsapp_connected', 'true');
          localStorage.setItem('whatsapp_phone', data.phoneNumber || '');
          toast({
            title: "Conectado",
            description: `WhatsApp conectado com sucesso!`,
          });
          setTimeout(() => window.location.reload(), 1000);
        }
      }
      refetchStatus();
      queryClient.invalidateQueries({ queryKey: ["/api/whatsapp/clients"] });
    },
    onError: (error: any) => {
      setIsGeneratingQR(false);
      toast({
        title: "Erro de Conexão",
        description: error.message || "Falha ao conectar WhatsApp",
        variant: "destructive",
      });
    },
  });

  const handleConnect = () => {
    if (!userId || userId.trim() === '') {
      toast({
        title: "ID do Vendedor Obrigatório",
        description: "Digite seu ID de vendedor para continuar",
        variant: "destructive",
      });
      return;
    }
    connectMutation.mutate();
  };

  const handleLogin = () => {
    if (status?.isConnected) {
      localStorage.setItem('whatsapp_connected', 'true');
      localStorage.setItem('whatsapp_phone', status.phoneNumber || '');
      window.location.reload();
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#f0f2f5] flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin text-[#00a884]" />
          <span className="text-[#667781]">Verificando conexão...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#f0f2f5] flex items-center justify-center p-4">
      <div className="w-full max-w-4xl grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="border-2 border-[#00a884]/20">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-[#00a884] rounded-full flex items-center justify-center mb-4">
              <Smartphone className="w-8 h-8 text-white" />
            </div>
            <CardTitle className="text-2xl text-[#111b21]">
              Casa das Camisetas CRM
            </CardTitle>
            <p className="text-gray-600">
              Sistema Multi-Usuário - Suporte para 30 Vendedores
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ID do Vendedor (1-30)
              </label>
              <Input
                type="number"
                placeholder="Digite seu ID (ex: 1, 2, 3...)"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                className="text-center text-lg"
                min="1"
                max="30"
              />
            </div>

            {status?.isConnected ? (
              <div className="space-y-4">
                <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg border border-green-200">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <div>
                    <p className="font-medium text-green-800">WhatsApp Conectado</p>
                    <p className="text-sm text-green-600">
                      {status.phoneNumber || `Vendedor ${userId}`}
                    </p>
                  </div>
                </div>
                <Button onClick={handleLogin} className="w-full bg-[#00a884] hover:bg-[#00a884]/90">
                  Acessar CRM
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {qrCodeData && (
                  <div className="p-4 bg-white rounded-lg border text-center space-y-3">
                    <div className="flex justify-center">
                      <img 
                        src={qrCodeData}
                        alt="QR Code WhatsApp" 
                        className="w-48 h-48 object-contain border border-gray-200 rounded"
                      />
                    </div>
                    <div className="space-y-2">
                      <p className="font-medium text-green-600">✅ QR Code Gerado!</p>
                      <p className="text-sm text-gray-600">
                        Escaneie com seu WhatsApp para conectar
                      </p>
                      <ol className="text-xs text-gray-500 text-left space-y-1">
                        <li>1. Abra WhatsApp no celular</li>
                        <li>2. Menu → "Dispositivos conectados"</li>
                        <li>3. "Conectar um dispositivo"</li>
                        <li>4. Escaneie o QR code acima</li>
                      </ol>
                    </div>
                  </div>
                )}
                
                <Button
                  onClick={handleConnect}
                  disabled={isGeneratingQR || isLoading || !userId}
                  className="w-full bg-[#00a884] hover:bg-[#00a884]/90"
                >
                  {isGeneratingQR ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Gerando QR Code...
                    </>
                  ) : (
                    <>
                      <QrCode className="w-4 h-4 mr-2" />
                      Conectar WhatsApp
                    </>
                  )}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="border-2 border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Status do Sistema
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                <span className="font-medium">Vendedores Conectados:</span>
                <span className="text-green-600 font-bold">
                  {allUsers ? Object.values(allUsers).filter((user: any) => user.isConnected).length : 0}/30
                </span>
              </div>
              
              {allUsers && (
                <div className="max-h-60 overflow-y-auto space-y-2">
                  {Object.entries(allUsers).map(([id, user]: [string, any]) => (
                    <div key={id} className="flex items-center justify-between p-2 bg-white rounded border">
                      <span className="font-medium">Vendedor {id}</span>
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${user.isConnected ? 'bg-green-500' : 'bg-gray-300'}`} />
                        <span className="text-sm text-gray-600">
                          {user.isConnected ? (user.phoneNumber || 'Conectado') : 'Desconectado'}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}