import { useState } from "react";
import { 
  <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>3, Trash2, Forward, <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle, X, Send, Reply 
} from "lucide-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";

interface MessageActionsProps {
  messageId: string;
  userId: string;
  isStarred: boolean;
  isOwn: boolean;
  canEdit: boolean;
  canDelete: boolean;
  messageContent: string;
  contactPhone: string;
  onClose: () => void;
  onReply?: (message: any) => void;
  onForward?: (message: any) => void;
}

interface EditMessageModalProps {
  messageId: string;
  userId: string;
  originalContent: string;
  isOpen: boolean;
  onClose: () => void;
}

interface DeleteConfirmModalProps {
  messageId: string;
  userId: string;
  isOpen: boolean;
  onClose: () => void;
}

function EditMessageModal({ messageId, userId, originalContent, isOpen, onClose }: EditMessageModalProps) {
  const [content, setContent] = useState(originalContent);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const editMutation = useMutation({
    mutationFn: async ({ messageId, content, userId }: { messageId: string; content: string; userId: string }) => {
      const response = await fetch(`/api/messages/${messageId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, content })
      });
      if (!response.ok) throw new Error('Failed to edit message');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/whatsapp/messages'] });
      toast({
        title: "Mensagem editada",
        description: "A mensagem foi editada com sucesso e registrada na auditoria"
      });
      onClose();
    },
    onError: () => {
      toast({
        title: "Erro ao editar",
        description: "Não foi possível editar a mensagem",
        variant: "destructive"
      });
    }
  });

  const handleSave = () => {
    if (content.trim() && content !== originalContent) {
      editMutation.mutate({ messageId, content: content.trim(), userId });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-md mx-4">
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="text-lg font-semibold">Editar Mensagem</h3>
          <button onClick={onClose} className="p-1 hover:bg-gray-100 rounded">
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="p-4">
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Digite sua mensagem..."
            className="w-full h-32 p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:border-green-500"
          />
          
          <div className="flex items-center text-xs text-gray-500 mt-2">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Edições são registradas para auditoria empresarial
          </div>
        </div>
        
        <div className="flex justify-end space-x-2 p-4 border-t">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancelar
          </button>
          <button
            onClick={handleSave}
            disabled={!content.trim() || content === originalContent || editMutation.isPending}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 flex items-center space-x-2"
          >
            {editMutation.isPending ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Send className="w-4 h-4" />
            )}
            <span>Salvar</span>
          </button>
        </div>
      </div>
    </div>
  );
}

function DeleteConfirmModal({ messageId, userId, isOpen, onClose }: DeleteConfirmModalProps) {
  const [deleteType, setDeleteType] = useState<'for_me' | 'for_everyone'>('for_me');
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const deleteMutation = useMutation({
    mutationFn: async ({ messageId, userId, deleteType }: { 
      messageId: string; 
      userId: string; 
      deleteType: 'for_me' | 'for_everyone' 
    }) => {
      const response = await fetch(`/api/messages/${messageId}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, deleteType })
      });
      if (!response.ok) throw new Error('Failed to delete message');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/whatsapp/messages'] });
      toast({
        title: "Mensagem apagada",
        description: "A mensagem foi apagada com registro completo de auditoria"
      });
      onClose();
    },
    onError: () => {
      toast({
        title: "Erro ao apagar",
        description: "Não foi possível apagar a mensagem",
        variant: "destructive"
      });
    }
  });

  const handleDelete = () => {
    deleteMutation.mutate({ messageId, userId, deleteType });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-md mx-4">
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="text-lg font-semibold text-red-600">Apagar Mensagem</h3>
          <button onClick={onClose} className="p-1 hover:bg-gray-100 rounded">
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="p-4">
          <p className="text-gray-700 mb-4">
            Como você deseja apagar esta mensagem?
          </p>
          
          <div className="space-y-3">
            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="radio"
                name="deleteType"
                value="for_me"
                checked={deleteType === 'for_me'}
                onChange={(e) => setDeleteType(e.target.value as 'for_me')}
                className="text-green-600"
              />
              <div>
                <div className="font-medium">Apagar para mim</div>
                <div className="text-sm text-gray-600">A mensagem será removida apenas para você</div>
              </div>
            </label>
            
            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="radio"
                name="deleteType"
                value="for_everyone"
                checked={deleteType === 'for_everyone'}
                onChange={(e) => setDeleteType(e.target.value as 'for_everyone')}
                className="text-green-600"
              />
              <div>
                <div className="font-medium">Apagar para todos</div>
                <div className="text-sm text-gray-600">A mensagem será removida para todos os participantes</div>
              </div>
            </label>
          </div>
          
          <div className="flex items-center text-xs text-amber-600 mt-4 p-2 bg-amber-50 rounded">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Todas as exclusões são registradas permanentemente para auditoria
          </div>
        </div>
        
        <div className="flex justify-end space-x-2 p-4 border-t">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancelar
          </button>
          <button
            onClick={handleDelete}
            disabled={deleteMutation.isPending}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 flex items-center space-x-2"
          >
            {deleteMutation.isPending ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Trash2 className="w-4 h-4" />
            )}
            <span>Apagar</span>
          </button>
        </div>
      </div>
    </div>
  );
}

export default function MessageActions({
  messageId,
  userId,
  isStarred,
  isOwn,
  canEdit,
  canDelete,
  messageContent,
  contactPhone,
  onClose,
  onReply,
  onForward
}: MessageActionsProps) {
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const starMutation = useMutation({
    mutationFn: async ({ messageId, userId, starred }: { 
      messageId: string; 
      userId: string; 
      starred: boolean 
    }) => {
      const response = await fetch(`/api/messages/${messageId}/star`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, starred })
      });
      if (!response.ok) throw new Error('Failed to star message');
      return response.json();
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['/api/whatsapp/messages'] });
      toast({
        title: variables.starred ? "Mensagem favoritada" : "Removido dos favoritos",
        description: "Ação registrada na auditoria"
      });
      onClose();
    },
    onError: () => {
      toast({
        title: "Erro",
        description: "Não foi possível executar a ação",
        variant: "destructive"
      });
    }
  });

  const forwardMutation = useMutation({
    mutationFn: async ({ messageId, userId, fromPhone, toPhone }: { 
      messageId: string; 
      userId: string; 
      fromPhone: string;
      toPhone: string;
    }) => {
      const response = await fetch(`/api/messages/${messageId}/forward`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, fromPhone, toPhone })
      });
      if (!response.ok) throw new Error('Failed to forward message');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/whatsapp/messages'] });
      toast({
        title: "Mensagem encaminhada",
        description: "Encaminhamento registrado na auditoria"
      });
      onClose();
    },
    onError: () => {
      toast({
        title: "Erro ao encaminhar",
        description: "Não foi possível encaminhar a mensagem",
        variant: "destructive"
      });
    }
  });

  const handleStar = () => {
    starMutation.mutate({ messageId, userId, starred: !isStarred });
  };

  const handleEdit = () => {
    setShowEditModal(true);
  };

  const handleDelete = () => {
    setShowDeleteModal(true);
  };

  const handleForward = () => {
    if (onForward) {
      onForward({
        id: messageId,
        body: messageContent,
        fromMe: isOwn
      });
    }
    onClose();
  };

  const handleReply = () => {
    if (onReply) {
      onReply({
        id: messageId,
        content: messageContent,
        fromMe: isOwn,
        senderName: isOwn ? 'Você' : 'Contato'
      });
    }
    onClose();
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow-lg border p-3 min-w-48">
        <div className="space-y-2">
          <button
            onClick={handleReply}
            className="w-full flex items-center space-x-3 px-3 py-2 text-left hover:bg-gray-100 rounded"
          >
            <Reply className="w-4 h-4 text-blue-600" />
            <span className="text-sm">Responder</span>
          </button>

          <button
            onClick={handleStar}
            disabled={starMutation.isPending}
            className="w-full flex items-center space-x-3 px-3 py-2 text-left hover:bg-gray-100 rounded"
          >
            {isStarred ? (
              <StarOff className="w-4 h-4 text-amber-500" />
            ) : (
              <Star className="w-4 h-4 text-gray-600" />
            )}
            <span className="text-sm">
              {isStarred ? 'Remover dos favoritos' : 'Adicionar aos favoritos'}
            </span>
          </button>

          {canEdit && (
            <button
              onClick={handleEdit}
              className="w-full flex items-center space-x-3 px-3 py-2 text-left hover:bg-gray-100 rounded"
            >
              <Edit3 className="w-4 h-4 text-blue-600" />
              <span className="text-sm">Editar mensagem</span>
            </button>
          )}

          <button
            onClick={handleForward}
            disabled={forwardMutation.isPending}
            className="w-full flex items-center space-x-3 px-3 py-2 text-left hover:bg-gray-100 rounded"
          >
            <Forward className="w-4 h-4 text-green-600" />
            <span className="text-sm">Encaminhar</span>
          </button>

          {canDelete && (
            <button
              onClick={handleDelete}
              className="w-full flex items-center space-x-3 px-3 py-2 text-left hover:bg-gray-100 rounded text-red-600"
            >
              <Trash2 className="w-4 h-4" />
              <span className="text-sm">Apagar mensagem</span>
            </button>
          )}
        </div>
        
        <div className="border-t pt-2 mt-2">
          <div className="flex items-center text-xs text-gray-500">
            <CheckCircle className="w-3 h-3 mr-1" />
            Todas as ações são auditadas
          </div>
        </div>
      </div>

      <EditMessageModal
        messageId={messageId}
        userId={userId}
        originalContent={messageContent}
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
      />

      <DeleteConfirmModal
        messageId={messageId}
        userId={userId}
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
      />
    </>
  );
}