import { drizzle } from "drizzle-orm/node-postgres";
import { Pool } from "pg";
import { eq, desc, and, or, sql, count } from "drizzle-orm";
import { 
  users, leads, chats, messages, activities, whatsappSessions, kanbanTasks,
  type User, type InsertUser, type Lead, type InsertLead, 
  type Chat, type InsertChat, type Message, type InsertMessage,
  type Activity, type InsertActivity, type WhatsappSession,
  type KanbanTask, type InsertKanbanTask, type LeadStatusType
} from "@shared/schema";

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

const db = drizzle(pool);

export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUsers(): Promise<User[]>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, user: Partial<InsertUser>): Promise<User | undefined>;
  
  // WhatsApp session operations
  getWhatsappSession(userId: number): Promise<WhatsappSession | undefined>;
  createOrUpdateWhatsappSession(userId: number, sessionData: any): Promise<WhatsappSession>;
  updateSessionStatus(userId: number, isConnected: boolean, phoneNumber?: string): Promise<void>;
  
  // Lead operations
  getLeads(assignedTo?: number): Promise<Lead[]>;
  getLeadsByStatus(status: LeadStatusType): Promise<Lead[]>;
  getLead(id: number): Promise<Lead | undefined>;
  getLeadByPhone(phone: string): Promise<Lead | undefined>;
  createLead(lead: InsertLead): Promise<Lead>;
  updateLead(id: number, lead: Partial<InsertLead>): Promise<Lead | undefined>;
  updateLeadStatus(id: number, status: LeadStatusType): Promise<Lead | undefined>;
  deleteLead(id: number): Promise<boolean>;
  
  // Chat operations
  getChats(leadId?: number): Promise<Chat[]>;
  getChat(id: number): Promise<Chat | undefined>;
  getChatByWhatsappId(whatsappChatId: string): Promise<Chat | undefined>;
  createChat(chat: InsertChat): Promise<Chat>;
  updateChat(id: number, chat: Partial<InsertChat>): Promise<Chat | undefined>;
  updateChatLastMessage(id: number, timestamp: Date): Promise<void>;
  
  // Message operations
  getMessages(chatId: number, limit?: number): Promise<Message[]>;
  getMessage(id: number): Promise<Message | undefined>;
  getMessageByWhatsappId(whatsappMessageId: string): Promise<Message | undefined>;
  createMessage(message: InsertMessage): Promise<Message>;
  markMessagesAsRead(chatId: number): Promise<void>;
  
  // Activity operations
  getActivities(leadId?: number, limit?: number): Promise<Activity[]>;
  createActivity(activity: InsertActivity): Promise<Activity>;
  
  // Dashboard statistics
  getDashboardStats(): Promise<{
    activeChats: number;
    totalLeads: number;
    pendingTasks: number;
    leadsByStatus: Record<string, number>;
  }>;

  // Kanban operations
  getKanbanTasks(): Promise<KanbanTask[]>;
  getKanbanTask(id: string): Promise<KanbanTask | undefined>;
  createKanbanTask(task: Partial<InsertKanbanTask> & { id: string }): Promise<KanbanTask>;
  updateKanbanTask(id: string, task: Partial<InsertKanbanTask>): Promise<KanbanTask | undefined>;
  deleteKanbanTask(id: string): Promise<boolean>;
}

export class DatabaseStorage implements IStorage {
  // User operations
  async getUser(id: number): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
    return result[0];
  }

  async getUsers(): Promise<User[]> {
    return await db.select().from(users).orderBy(users.id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.username, username)).limit(1);
    return result[0];
  }

  async createUser(user: InsertUser): Promise<User> {
    const result = await db.insert(users).values(user).returning();
    return result[0];
  }

  async updateUser(id: number, user: Partial<InsertUser>): Promise<User | undefined> {
    const result = await db.update(users).set({
      ...user,
      updatedAt: new Date()
    }).where(eq(users.id, id)).returning();
    return result[0];
  }

  // WhatsApp session operations
  async getWhatsappSession(userId: number): Promise<WhatsappSession | undefined> {
    const result = await db.select().from(whatsappSessions).where(eq(whatsappSessions.userId, userId)).limit(1);
    return result[0];
  }

  async createOrUpdateWhatsappSession(userId: number, sessionData: any): Promise<WhatsappSession> {
    const existing = await this.getWhatsappSession(userId);
    
    if (existing) {
      const result = await db.update(whatsappSessions).set({
        sessionData,
        updatedAt: new Date()
      }).where(eq(whatsappSessions.userId, userId)).returning();
      return result[0];
    } else {
      const result = await db.insert(whatsappSessions).values({
        userId,
        sessionId: `session_${userId}_${Date.now()}`,
        sessionData,
        isConnected: false
      }).returning();
      return result[0];
    }
  }

  async updateSessionStatus(userId: number, isConnected: boolean, phoneNumber?: string): Promise<void> {
    await db.update(whatsappSessions).set({
      isConnected,
      phoneNumber,
      lastConnected: isConnected ? new Date() : undefined,
      updatedAt: new Date()
    }).where(eq(whatsappSessions.userId, userId));
  }

  // Lead operations
  async getLeads(assignedTo?: number): Promise<Lead[]> {
    if (assignedTo) {
      return await db.select().from(leads).where(eq(leads.assignedTo, assignedTo)).orderBy(desc(leads.updatedAt));
    }
    
    return await db.select().from(leads).orderBy(desc(leads.updatedAt));
  }

  async getLeadsByStatus(status: LeadStatusType): Promise<Lead[]> {
    return await db.select().from(leads).where(eq(leads.status, status)).orderBy(desc(leads.updatedAt));
  }

  async getLead(id: number): Promise<Lead | undefined> {
    const result = await db.select().from(leads).where(eq(leads.id, id)).limit(1);
    return result[0];
  }

  async getLeadByPhone(phone: string): Promise<Lead | undefined> {
    const result = await db.select().from(leads).where(eq(leads.phone, phone)).limit(1);
    return result[0];
  }

  async createLead(lead: InsertLead): Promise<Lead> {
    const result = await db.insert(leads).values(lead).returning();
    return result[0];
  }

  async updateLead(id: number, lead: Partial<InsertLead>): Promise<Lead | undefined> {
    const result = await db.update(leads).set({
      ...lead,
      updatedAt: new Date()
    }).where(eq(leads.id, id)).returning();
    return result[0];
  }

  async updateLeadStatus(id: number, status: LeadStatusType): Promise<Lead | undefined> {
    const result = await db.update(leads).set({
      status,
      updatedAt: new Date()
    }).where(eq(leads.id, id)).returning();
    return result[0];
  }

  async deleteLead(id: number): Promise<boolean> {
    const result = await db.delete(leads).where(eq(leads.id, id));
    return (result.rowCount ?? 0) > 0;
  }

  // Chat operations
  async getChats(leadId?: number): Promise<Chat[]> {
    if (leadId) {
      return await db.select().from(chats).where(eq(chats.leadId, leadId)).orderBy(desc(chats.lastMessageAt));
    }
    
    return await db.select().from(chats).orderBy(desc(chats.lastMessageAt));
  }

  async getChat(id: number): Promise<Chat | undefined> {
    const result = await db.select().from(chats).where(eq(chats.id, id)).limit(1);
    return result[0];
  }

  async getChatByWhatsappId(whatsappChatId: string): Promise<Chat | undefined> {
    const result = await db.select().from(chats).where(eq(chats.whatsappChatId, whatsappChatId)).limit(1);
    return result[0];
  }

  async createChat(chat: InsertChat): Promise<Chat> {
    const result = await db.insert(chats).values(chat).returning();
    return result[0];
  }

  async updateChat(id: number, chat: Partial<InsertChat>): Promise<Chat | undefined> {
    const result = await db.update(chats).set({
      ...chat,
      updatedAt: new Date()
    }).where(eq(chats.id, id)).returning();
    return result[0];
  }

  async updateChatLastMessage(id: number, timestamp: Date): Promise<void> {
    await db.update(chats).set({
      lastMessageAt: timestamp,
      updatedAt: new Date()
    }).where(eq(chats.id, id));
  }

  // Message operations
  async getMessages(chatId: number, limit: number = 50): Promise<Message[]> {
    return await db.select().from(messages)
      .where(eq(messages.chatId, chatId))
      .orderBy(desc(messages.timestamp))
      .limit(limit);
  }

  async getMessage(id: number): Promise<Message | undefined> {
    const result = await db.select().from(messages).where(eq(messages.id, id)).limit(1);
    return result[0];
  }

  async getMessageByWhatsappId(whatsappMessageId: string): Promise<Message | undefined> {
    const result = await db.select().from(messages).where(eq(messages.whatsappMessageId, whatsappMessageId)).limit(1);
    return result[0];
  }

  async createMessage(message: InsertMessage): Promise<Message> {
    const result = await db.insert(messages).values(message).returning();
    return result[0];
  }

  async markMessagesAsRead(chatId: number): Promise<void> {
    await db.update(messages).set({
      isRead: true
    }).where(and(eq(messages.chatId, chatId), eq(messages.isRead, false)));
  }

  // Activity operations
  async getActivities(leadId?: number, limit: number = 20): Promise<Activity[]> {
    if (leadId) {
      return await db.select().from(activities).where(eq(activities.leadId, leadId)).orderBy(desc(activities.createdAt)).limit(limit);
    }
    
    return await db.select().from(activities).orderBy(desc(activities.createdAt)).limit(limit);
  }

  async createActivity(activity: InsertActivity): Promise<Activity> {
    const result = await db.insert(activities).values(activity).returning();
    return result[0];
  }

  // Dashboard statistics
  async getDashboardStats(): Promise<{
    activeChats: number;
    totalLeads: number;
    pendingTasks: number;
    leadsByStatus: Record<string, number>;
  }> {
    const [activeChatCount] = await db.select({ count: count() }).from(chats).where(eq(chats.unreadCount, 0));
    const [totalLeadCount] = await db.select({ count: count() }).from(leads);
    const [pendingTaskCount] = await db.select({ count: count() }).from(leads).where(or(eq(leads.status, "new"), eq(leads.status, "contact")));
    
    const leadsByStatusQuery = await db.select({
      status: leads.status,
      count: count()
    }).from(leads).groupBy(leads.status);
    
    const leadsByStatus = leadsByStatusQuery.reduce((acc, item) => {
      acc[item.status] = item.count;
      return acc;
    }, {} as Record<string, number>);

    return {
      activeChats: activeChatCount.count,
      totalLeads: totalLeadCount.count,
      pendingTasks: pendingTaskCount.count,
      leadsByStatus
    };
  }

  // Kanban operations
  async getKanbanTasks(): Promise<KanbanTask[]> {
    try {
      return await db.select().from(kanbanTasks).orderBy(kanbanTasks.position);
    } catch (error) {
      console.error("Error fetching kanban tasks:", error);
      return [];
    }
  }

  async getKanbanTask(id: string): Promise<KanbanTask | undefined> {
    try {
      const [task] = await db.select().from(kanbanTasks).where(eq(kanbanTasks.id, id));
      return task;
    } catch (error) {
      console.error("Error fetching kanban task:", error);
      return undefined;
    }
  }

  async createKanbanTask(task: Partial<InsertKanbanTask> & { id: string }): Promise<KanbanTask> {
    try {
      const [newTask] = await db.insert(kanbanTasks).values(task as any).returning();
      return newTask;
    } catch (error) {
      console.error("Error creating kanban task:", error);
      throw error;
    }
  }

  async updateKanbanTask(id: string, task: Partial<InsertKanbanTask>): Promise<KanbanTask | undefined> {
    try {
      const [updatedTask] = await db
        .update(kanbanTasks)
        .set(task)
        .where(eq(kanbanTasks.id, id))
        .returning();
      return updatedTask;
    } catch (error) {
      console.error("Error updating kanban task:", error);
      return undefined;
    }
  }

  async deleteKanbanTask(id: string): Promise<boolean> {
    try {
      const result = await db.delete(kanbanTasks).where(eq(kanbanTasks.id, id));
      return result.rowCount ? result.rowCount > 0 : false;
    } catch (error) {
      console.error("Error deleting kanban task:", error);
      return false;
    }
  }
}

export const storage = new DatabaseStorage();
