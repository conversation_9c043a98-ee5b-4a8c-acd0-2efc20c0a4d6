# Casa das Camisetas - WhatsApp CRM

## Overview

Sistema CRM WhatsApp moderno desenvolvido especificamente para a "Casa das Camisetas" que integra capacidades reais de mensagens WhatsApp com uma interface abrangente de gerenciamento de relacionamento com clientes. O sistema usa Baileys v2 Multi-Device API para integração autêntica do WhatsApp e fornece um sistema visual de gerenciamento de leads estilo Kanban com capacidades de chat em tempo real.

## Características

- **Integração Real do WhatsApp**: Conecte sua conta real do WhatsApp usando QR code
- **Suporte Multi-Usuário**: Suporte para 30 vendedores simultâneos via LAN
- **Gerenciamento de Leads**: Gestão completa do ciclo de vida do cliente
- **Chat em Tempo Real**: Conversas WhatsApp ao vivo dentro do CRM
- **Kanban Board**: Gerenciamento visual de pipeline
- **Dashboard Analytics**: Métricas de desempenho e estatísticas
- **Design Responsivo**: Funciona em desktop e dispositivos móveis
- **Containerização Docker**: Deploy fácil e escalável
- **Arquitetura de Produção**: Pronto para uso empresarial

## Stack Tecnológico

- **Frontend**: React 18, TypeScript, Tailwind CSS, Shadcn/UI, Nginx
- **Backend**: Express.js, TypeScript, Baileys v2
- **Database**: PostgreSQL com Drizzle ORM
- **Real-time**: WebSocket communication
- **Build**: Vite
- **Containerização**: Docker & Docker Compose
- **Proxy**: Nginx para servir arquivos estáticos

## Requisitos do Sistema

- **Servidor**: Linux (Ubuntu 20.04+ recomendado)
- **Docker**: 20.10+
- **Docker Compose**: 1.29+
- **RAM**: Mínimo 4GB (8GB recomendado para 30 usuários)
- **CPU**: 2+ cores
- **Armazenamento**: 20GB+ livres
- **Rede**: Acesso à internet e rede LAN configurada

## Instalação para Produção (Docker)

### 1. Preparação do Ambiente

```bash
# Clone o repositório
git clone <repository-url>
cd casa-das-camisetas-crm

# Torne os scripts executáveis
chmod +x bootstrap.sh deploy.sh

# Execute o bootstrap (instala Docker se necessário)
./bootstrap.sh
```

### 2. Configuração

```bash
# Configure as variáveis de ambiente
cp .env.example .env
nano .env

# Configure especialmente:
# - POSTGRES_PASSWORD (senha segura)
# - JWT_SECRET (chave secreta longa)
# - SESSION_SECRET (chave de sessão segura)
```

### 3. Deploy

```bash
# Execute o deploy completo
./deploy.sh

# Para deploy limpo (remove dados existentes):
./deploy.sh --clean
```

### 4. Conexão WhatsApp Real

1. Acesse o sistema: `http://localhost:3000`
2. Faça login com suas credenciais
3. Vá para a seção de integração WhatsApp
4. Clique "Conectar WhatsApp"
5. **ESCANEIE O QR CODE COM SUA CONTA REAL DO WHATSAPP**
6. Aguarde a confirmação de conexão

⚠️ **IMPORTANTE**: Este QR Code conecta sua conta real do WhatsApp. Nenhuma simulação será usada.

## Acesso via Rede LAN

Para permitir que outros computadores da rede acessem:

1. **Descubra o IP do servidor**:
```bash
hostname -I | awk '{print $1}'
```

2. **Acesse de outros computadores**:
```
http://IP_DO_SERVIDOR:3000
```
Exemplo: `http://*************:3000`

3. **Configure firewall se necessário**:
```bash
sudo ufw allow from ***********/16 to any port 3000
sudo ufw allow from ***********/16 to any port 4000
```

## Comandos Úteis

### Gerenciamento Docker
```bash
# Ver status dos containers
docker-compose ps

# Ver logs em tempo real
docker-compose logs -f

# Parar sistema
docker-compose down

# Reiniciar sistema
docker-compose restart

# Atualizar containers
docker-compose pull && docker-compose up -d
```

### Backup e Restauração
```bash
# Backup do banco de dados
docker-compose exec db pg_dump -U postgres whatsappcrm > backup_$(date +%Y%m%d_%H%M%S).sql

# Restaurar banco de dados
cat backup_file.sql | docker-compose exec -T db psql -U postgres whatsappcrm

# Backup completo das sessões WhatsApp
sudo tar -czf sessions_backup_$(date +%Y%m%d_%H%M%S).tar.gz sessions/
```

### Monitoramento
```bash
# Ver recursos utilizados
docker stats

# Ver logs específicos
docker-compose logs api
docker-compose logs web
docker-compose logs db

# Verificar saúde dos serviços
curl http://localhost:4000/api/health
curl http://localhost:3000
```

## Configuração Systemd (Auto-inicialização)

Para inicializar automaticamente com o sistema:

```bash
# Copiar arquivo de serviço
sudo cp whatsappcrm.service /etc/systemd/system/

# Criar usuário para o serviço
sudo useradd -r -s /bin/false whatsappcrm
sudo chown -R whatsappcrm:whatsappcrm /opt/whatsappcrm

# Habilitar e iniciar serviço
sudo systemctl daemon-reload
sudo systemctl enable whatsappcrm
sudo systemctl start whatsappcrm

# Verificar status
sudo systemctl status whatsappcrm
```

## Arquitetura do Sistema

### Containers Docker
- **web**: Frontend React com Nginx (porta 3000)
- **api**: Backend Express.js (porta 4000)
- **db**: PostgreSQL (porta 5432)

### Volumes Persistentes
- **postgres_data**: Dados do banco PostgreSQL
- **whatsapp_sessions**: Sessões autenticadas do WhatsApp

### Rede
- **whatsapp-network**: Rede privada Docker para comunicação entre containers

## Capacidade e Performance

- **Usuários Simultâneos**: 30 vendedores
- **Conexões WhatsApp**: Múltiplas contas por usuário
- **Mensagens**: Processamento em tempo real
- **Backup Automático**: Configurável via variáveis
- **Escalabilidade**: Horizontal via Docker Swarm (futuro)

## Solução de Problemas

### Container não inicia
```bash
# Verificar logs
docker-compose logs [serviço]

# Verificar recursos
docker system df
df -h

# Limpar recursos
docker system prune
```

### WhatsApp não conecta
```bash
# Verificar logs do backend
docker-compose logs api

# Limpar sessões
sudo rm -rf sessions/*
docker-compose restart api
```

### Problemas de rede LAN
```bash
# Verificar portas
netstat -tlnp | grep -E ':(3000|4000|5432)'

# Testar conectividade
curl http://IP_SERVIDOR:3000
curl http://IP_SERVIDOR:4000/api/health
```

## Estrutura do Projeto

```
casa-das-camisetas-crm/
├── client/                 # Frontend React
├── server/                 # Backend Express
├── shared/                 # Schemas compartilhados
├── sessions/              # Sessões WhatsApp (persistente)
├── logs/                  # Logs do sistema
├── backups/               # Backups automáticos
├── Dockerfile.backend     # Container do backend
├── Dockerfile.frontend    # Container do frontend
├── docker-compose.yml     # Orquestração Docker
├── nginx.conf            # Configuração Nginx
├── deploy.sh             # Script de deploy
├── bootstrap.sh          # Script de preparação
├── whatsappcrm.service   # Serviço systemd
└── .env.example          # Variáveis de ambiente
```

## Suporte e Manutenção

### Atualizações
```bash
# Atualizar código
git pull origin main

# Rebuild e redeploy
./deploy.sh
```

### Monitoramento de Logs
```bash
# Logs em tempo real
tail -f logs/whatsapp-crm.log

# Logs Docker
docker-compose logs -f --tail=100
```

### Manutenção do Banco
```bash
# Executar migrações
docker-compose exec api npm run db:push

# Acessar banco diretamente
docker-compose exec db psql -U postgres whatsappcrm
```

## Versão e Histórico

- **v4.0**: Reestruturação completa com Docker para produção
- **v3.0**: Implementação Baileys v2 com dados reais
- **v2.0**: Interface CRM com Kanban
- **v1.0**: Prova de conceito inicial

**Status**: ✅ PRODUÇÃO - Sistema testado com conta WhatsApp real (556181084062)

## Licença

Este projeto é licenciado sob a Licença MIT.