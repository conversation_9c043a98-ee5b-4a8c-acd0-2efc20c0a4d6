/**
 * Defensive utility functions to prevent message state regression
 * These functions provide type-safe array access and normalization
 */

export interface MessagesPaginatedResponse {
  data?: any[];
  messages?: any[];
  total?: number;
  hasMore?: boolean;
  offset?: number;
  page?: number;
}

/**
 * Safely extracts messages array from any response format
 * Prevents "messages.filter is not a function" errors
 */
export function safeExtractMessages(response: any): any[] {
  if (!response) return [];
  
  // Handle direct array responses
  if (Array.isArray(response)) {
    return response;
  }
  
  // Handle paginated responses with 'data' field
  if (response.data && Array.isArray(response.data)) {
    return response.data;
  }
  
  // Handle legacy responses with 'messages' field
  if (response.messages && Array.isArray(response.messages)) {
    return response.messages;
  }
  
  // Fallback to empty array
  return [];
}

/**
 * Normalizes any response to paginated structure
 */
export function normalizeToPaginated(response: any): MessagesPaginatedResponse {
  if (!response) {
    return { data: [], total: 0, hasMore: false };
  }
  
  // Already paginated
  if (response.data && Array.isArray(response.data)) {
    return {
      data: response.data,
      total: response.total || response.data.length,
      hasMore: response.hasMore || false,
      offset: response.offset || 0,
      page: response.page || 1
    };
  }
  
  // Direct array - convert to paginated
  if (Array.isArray(response)) {
    return {
      data: response,
      total: response.length,
      hasMore: false,
      offset: 0,
      page: 1
    };
  }
  
  // Legacy messages field
  if (response.messages && Array.isArray(response.messages)) {
    return {
      data: response.messages,
      total: response.total || response.messages.length,
      hasMore: response.hasMore || false,
      offset: response.offset || 0,
      page: response.page || 1
    };
  }
  
  // Fallback
  return { data: [], total: 0, hasMore: false };
}

/**
 * Defensive guard for message array operations
 */
export function withMessageSafety<T>(
  messages: any,
  operation: (safeMessages: any[]) => T,
  fallback: T
): T {
  try {
    const safeMessages = safeExtractMessages(messages);
    if (!Array.isArray(safeMessages)) {
      console.warn('Message safety: Expected array, got:', typeof safeMessages);
      return fallback;
    }
    return operation(safeMessages);
  } catch (error) {
    console.error('Message safety operation failed:', error);
    return fallback;
  }
}

/**
 * Type guard to check if response is paginated
 */
export function isPaginatedResponse(response: any): response is MessagesPaginatedResponse {
  return response && 
    typeof response === 'object' && 
    (Array.isArray(response.data) || Array.isArray(response.messages));
}