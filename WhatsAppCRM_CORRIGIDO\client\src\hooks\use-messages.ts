import { useQuery, useInfiniteQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "./use-toast";

// WhatsApp Message interface - normalized and defensive
interface WhatsAppMessage {
  id: string;
  chatId: string;
  fromMe: boolean;
  author: string;
  body: string;
  type: string;
  timestamp: string;
  ack: number;
  status: string;
  hasMedia: boolean;
  mediaUrl?: string;
  mediaType?: string;
  mediaMimeType?: string;
  mediaSize?: number;
  mediaName?: string;
  mediaThumbnail?: string;
  starred: boolean;
  isForwarded: boolean;
  quotedMsgBody?: string;
  quotedMsgAuthor?: string;
}

interface MessagesResponse {
  messages: WhatsAppMessage[];
  hasMore: boolean;
  page: number;
  totalMessages: number;
}

interface SendMessageRequest {
  to: string;
  message: string;
  userId?: string;
}

// Safe message loader with pagination and deduplication
export function useWhatsAppMessages(userId?: string, chatId?: string) {
  return useInfiniteQuery({
    queryKey: ['/api/whatsapp/messages', userId, chatId],
    queryFn: async ({ pageParam }: { pageParam: number }) => {
      if (!userId || !chatId) {
        return { messages: [], hasMore: false, page: 1, totalMessages: 0 };
      }
      
      const params = new URLSearchParams({
        userId,
        chatId,
        page: pageParam.toString(),
        limit: '50'
      });
      
      const response = await fetch(`/api/whatsapp/messages?${params}`);
      if (!response.ok) {
        console.error('Failed to load messages:', response.status);
        return { messages: [], hasMore: false, page: pageParam, totalMessages: 0 };
      }
      
      const data = await response.json() as MessagesResponse;
      
      // Defensive data validation
      const safeMessages = Array.isArray(data.messages) ? data.messages.filter(msg => 
        msg?.id && typeof msg.id === 'string'
      ) : [];
      
      return {
        messages: safeMessages,
        hasMore: data.hasMore ?? false,
        page: pageParam,
        totalMessages: data.totalMessages ?? 0
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage: MessagesResponse) => lastPage.hasMore ? lastPage.page + 1 : undefined,
    enabled: !!userId && !!chatId,
    staleTime: 30000, // 30 seconds
    refetchInterval: false, // Only refetch on focus/reconnect
    refetchOnWindowFocus: true,
  });
}

// Get all messages flattened with deduplication - NO ZOMBIE STATES
export function useAllMessages(userId?: string, chatId?: string) {
  const { data, ...rest } = useWhatsAppMessages(userId, chatId);
  
  // Safe defensive flattening with ID-based deduplication
  const safeMessages = Array.isArray(data?.pages) ? 
    data.pages.flatMap((page: MessagesResponse) => Array.isArray(page?.messages) ? page.messages : []) : [];
  
  // Strict deduplication by ID to prevent zombie states
  const messageMap = new Map();
  safeMessages.forEach(msg => {
    if (msg?.id && typeof msg.id === 'string') {
      messageMap.set(msg.id, msg);
    }
  });
  
  const deduplicatedMessages = Array.from(messageMap.values());
  
  return {
    messages: deduplicatedMessages,
    totalMessages: data?.pages?.[0]?.totalMessages ?? 0,
    hasNextPage: rest.hasNextPage ?? false,
    fetchNextPage: rest.fetchNextPage,
    isFetchingNextPage: rest.isFetchingNextPage ?? false,
    isLoading: rest.isLoading ?? false,
    error: rest.error,
  };
}

// Send message with deduplication protection
export function useSendMessage() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: SendMessageRequest) => {
      const response = await fetch('/api/whatsapp/send-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Erro desconhecido' }));
        throw new Error(error.message || 'Falha ao enviar mensagem');
      }

      return response.json();
    },
    onSuccess: (newMessage, variables) => {
      // STRICT anti-duplication protection
      if (newMessage?.id && variables.userId && typeof newMessage.id === 'string') {
        queryClient.setQueryData(
          ['/api/whatsapp/messages', variables.userId],
          (oldData: any) => {
            if (!oldData?.pages || !Array.isArray(oldData.pages)) return oldData;
            
            const firstPage = oldData.pages[0];
            if (!firstPage?.messages || !Array.isArray(firstPage.messages)) return oldData;
            
            // STRICT duplication check by ID
            const messageExists = firstPage.messages.some((msg: WhatsAppMessage) => 
              msg?.id === newMessage.id
            );
            
            if (messageExists) {
              console.log(`Message ${newMessage.id} already exists, preventing duplication`);
              return oldData;
            }
            
            // Safe addition to prevent zombie states
            return {
              ...oldData,
              pages: [
                {
                  ...firstPage,
                  messages: [newMessage, ...firstPage.messages],
                  totalMessages: (firstPage.totalMessages || 0) + 1
                },
                ...oldData.pages.slice(1)
              ]
            };
          }
        );
      }
      
      toast({
        title: "Mensagem enviada",
        description: "Sua mensagem foi enviada com sucesso",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao enviar mensagem",
        description: error instanceof Error ? error.message : 'Erro desconhecido',
        variant: "destructive",
      });
    },
  });
}

// Media loading with URL validation, safe fallbacks and exponential backoff
export function useMediaUrl(mediaUrl?: string, mediaType?: string) {
  return useQuery({
    queryKey: ['/api/media', mediaUrl, mediaType],
    queryFn: async () => {
      // Validate URL before attempting fetch
      if (!mediaUrl || typeof mediaUrl !== 'string' || mediaUrl.trim() === '') {
        return getMediaFallback(mediaType);
      }
      
      // URL validation
      try {
        new URL(mediaUrl);
      } catch {
        console.warn('Invalid media URL format:', mediaUrl);
        return getMediaFallback(mediaType);
      }
      
      let lastError: Error | null = null;
      
      // Retry with exponential backoff
      for (let attempt = 0; attempt < 3; attempt++) {
        try {
          const response = await fetch(`/api/media/${encodeURIComponent(mediaUrl)}`, {
            method: 'GET',
            headers: {
              'Accept': getAcceptHeader(mediaType),
            },
            signal: AbortSignal.timeout(10000) // 10s timeout
          });
          
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          
          const blob = await response.blob();
          
          // Validate blob size and type
          if (blob.size === 0) {
            throw new Error('Empty media file received');
          }
          
          if (blob.size > 50 * 1024 * 1024) { // 50MB limit
            throw new Error('Media file too large');
          }
          
          return URL.createObjectURL(blob);
          
        } catch (error) {
          lastError = error as Error;
          console.warn(`Media load attempt ${attempt + 1} failed:`, error);
          
          if (attempt < 2) {
            // Exponential backoff: 1s, 2s, 4s
            await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempt)));
          }
        }
      }
      
      console.error('All media load attempts failed for:', mediaUrl, lastError);
      return getMediaFallback(mediaType);
    },
    enabled: !!mediaUrl && typeof mediaUrl === 'string',
    staleTime: 300000, // 5 minutes
    retry: false, // Custom retry logic above
    refetchOnWindowFocus: false,
  });
}

// Safe fallback URLs based on media type
function getMediaFallback(mediaType?: string): string {
  const fallbacks = {
    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjZjNmNGY2Ii8+CjxwYXRoIGQ9Im0xNSA5LTYgNi0yLTIiIHN0cm9rZT0iIzk0YTNiOCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+',
    video: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjZjNmNGY2Ii8+Cjxwb2x5Z29uIHBvaW50cz0iMTAgOCAxNiAxMiAxMCAxNiIgZmlsbD0iIzk0YTNiOCIvPgo8L3N2Zz4=',
    audio: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjZjNmNGY2Ii8+CjxwYXRoIGQ9Im0xMiA4IDQgNHYtMmMwLTEuMS0uOS0yLTItMmgtMnoiIGZpbGw9IiM5NGEzYjgiLz4KPC9zdmc+',
    document: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjZjNmNGY2Ii8+CjxyZWN0IHg9IjYiIHk9IjQiIHdpZHRoPSIxMiIgaGVpZ2h0PSIxNiIgcng9IjIiIGZpbGw9IiM5NGEzYjgiLz4KPC9zdmc+',
  };
  
  return fallbacks[mediaType as keyof typeof fallbacks] || fallbacks.document;
}

// Accept headers for different media types
function getAcceptHeader(mediaType?: string): string {
  switch (mediaType) {
    case 'image':
      return 'image/webp,image/png,image/jpeg,image/gif,image/*;q=0.8,*/*;q=0.5';
    case 'video':
      return 'video/mp4,video/webm,video/*;q=0.8,*/*;q=0.5';
    case 'audio':
      return 'audio/mpeg,audio/wav,audio/ogg,audio/*;q=0.8,*/*;q=0.5';
    default:
      return '*/*';
  }
}

// Create message hook for database storage
export function useCreateMessage() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (messageData: {
      chatId: string;
      content: string;
      type: 'sent' | 'received';
      messageId?: string;
      fromMe: boolean;
      timestamp?: Date;
      mediaType?: string;
      mediaUrl?: string;
      mediaName?: string;
      mediaMimeType?: string;
    }) => {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(messageData),
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Erro desconhecido' }));
        throw new Error(error.message || 'Falha ao criar mensagem');
      }

      return response.json();
    },
    onSuccess: (newMessage) => {
      // Invalidate messages queries to refetch
      queryClient.invalidateQueries({ queryKey: ['/api/messages'] });
      queryClient.invalidateQueries({ queryKey: ['/api/whatsapp/messages'] });
      
      toast({
        title: "Mensagem salva",
        description: "Mensagem armazenada no banco de dados",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao salvar mensagem",
        description: error instanceof Error ? error.message : 'Erro desconhecido',
        variant: "destructive",
      });
    },
  });
}

// Legacy compatibility functions (marked for removal)
/** @deprecated Use useWhatsAppMessages instead */
export function useMessages(whatsappNumber?: string, clientPhone?: string) {
  console.warn('useMessages is deprecated. Use useWhatsAppMessages instead.');
  return useQuery({
    queryKey: ['/api/messages/legacy', whatsappNumber, clientPhone],
    queryFn: async () => [],
    enabled: false,
  });
}

/** @deprecated Use useAllMessages instead */
export function useMessagesByContact(whatsappNumber?: string, clientPhone?: string) {
  console.warn('useMessagesByContact is deprecated. Use useAllMessages instead.');
  return useQuery({
    queryKey: ['/api/messages/contact/legacy', whatsappNumber, clientPhone],
    queryFn: async () => [],
    enabled: false,
  });
}