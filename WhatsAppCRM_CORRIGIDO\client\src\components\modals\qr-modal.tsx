import { Di<PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { useQuery } from "@tanstack/react-query";
import { useWhatsApp } from "@/hooks/use-whatsapp";
import { QrCode, CheckCircle, Loader2 } from "lucide-react";
import { useEffect } from "react";

interface QRModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function QRModal({ isOpen, onClose }: QRModalProps) {
  const { isConnected, refreshStatus } = useWhatsApp();

  const { data: qrData, isLoading } = useQuery({
    queryKey: ["/api/whatsapp/qr"],
    enabled: isOpen && !isConnected,
    refetchInterval: 3000,
  });

  useEffect(() => {
    if (isConnected) {
      onClose();
      refreshStatus();
    }
  }, [isConnected, onClose, refreshStatus]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center">
            <div className="flex flex-col items-center space-y-4">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <QrCode className="h-8 w-8 text-green-600" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-slate-800">
                  Conectar WhatsApp Web
                </h3>
                <p className="text-slate-600 mt-2">
                  Escaneie o QR Code com seu WhatsApp para conectar
                </p>
              </div>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-col items-center space-y-6">
          {isLoading ? (
            <div className="w-64 h-64 bg-slate-100 border-2 border-dashed border-slate-300 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <Loader2 className="h-8 w-8 text-slate-400 animate-spin mb-2 mx-auto" />
                <p className="text-sm text-slate-500">Carregando QR Code...</p>
              </div>
            </div>
          ) : qrData?.qrCode ? (
            <>
              <div className="w-64 h-64 bg-white border rounded-lg overflow-hidden">
                <img 
                  src={qrData.qrCode} 
                  alt="QR Code WhatsApp" 
                  className="w-full h-full object-contain"
                />
              </div>
              <div className="flex items-center space-x-2 text-amber-600">
                <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse" />
                <span className="text-sm font-medium">Aguardando conexão...</span>
              </div>
            </>
          ) : qrData?.connected ? (
            <div className="text-center">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-green-600">
                WhatsApp Conectado!
              </h3>
              <p className="text-slate-600">
                Número: {qrData.phoneNumber}
              </p>
            </div>
          ) : (
            <div className="w-64 h-64 bg-slate-100 border-2 border-dashed border-slate-300 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <QrCode className="h-8 w-8 text-slate-400 mb-2 mx-auto" />
                <p className="text-sm text-slate-500">Erro ao carregar QR Code</p>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
