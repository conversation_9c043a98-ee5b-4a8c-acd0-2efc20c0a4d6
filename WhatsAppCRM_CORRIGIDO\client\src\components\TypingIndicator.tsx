import { useEffect, useState } from "react";

interface TypingIndicatorProps {
  isVisible: boolean;
  userName?: string;
}

export default function TypingIndicator({ isVisible, userName = "Alguém" }: TypingIndicatorProps) {
  const [dots, setDots] = useState(".");

  useEffect(() => {
    if (!isVisible) return;

    const interval = setInterval(() => {
      setDots(prev => {
        switch (prev) {
          case ".": return "..";
          case "..": return "...";
          case "...": return ".";
          default: return ".";
        }
      });
    }, 500);

    return () => clearInterval(interval);
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <div className="flex items-center space-x-2 px-4 py-2 text-gray-500 text-sm animate-pulse">
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
      </div>
      <span>{userName} está digitando{dots}</span>
    </div>
  );
}