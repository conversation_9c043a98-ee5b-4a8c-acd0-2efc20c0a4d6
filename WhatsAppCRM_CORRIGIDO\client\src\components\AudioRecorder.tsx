import { useState, useRef, useEffect } from "react";
import { Mic, MicOff, Send, X, Square, Play, Pause } from "lucide-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";

interface AudioRecorderProps {
  userId: string;
  contactPhone: string;
  isOpen: boolean;
  onClose: () => void;
  onSend?: (audioBlob: Blob) => void;
}

interface AudioVisualizerProps {
  analyser: AnalyserNode | null;
  isRecording: boolean;
}

function AudioVisualizer({ analyser, isRecording }: AudioVisualizerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  useEffect(() => {
    if (!analyser || !isRecording) {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      return;
    }

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const draw = () => {
      if (!isRecording) return;

      analyser.getByteFrequencyData(dataArray);

      ctx.fillStyle = '#f3f4f6';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      const barWidth = canvas.width / bufferLength * 2.5;
      let barHeight;
      let x = 0;

      for (let i = 0; i < bufferLength; i++) {
        barHeight = (dataArray[i] / 255) * canvas.height * 0.8;

        // Gradient from green to red based on frequency
        const intensity = dataArray[i] / 255;
        const red = Math.floor(intensity * 255);
        const green = Math.floor((1 - intensity) * 255) + 100;
        
        ctx.fillStyle = `rgb(${red}, ${green}, 50)`;
        ctx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);

        x += barWidth + 1;
      }

      animationRef.current = requestAnimationFrame(draw);
    };

    draw();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [analyser, isRecording]);

  return (
    <canvas
      ref={canvasRef}
      width={300}
      height={60}
      className="w-full h-15 bg-gray-100 rounded-lg"
    />
  );
}

export default function AudioRecorder({ userId, contactPhone, isOpen, onClose, onSend }: AudioRecorderProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [duration, setDuration] = useState(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const chunksRef = useRef<Blob[]>([]);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const uploadMutation = useMutation({
    mutationFn: async (audioBlob: Blob) => {
      const formData = new FormData();
      formData.append('media', audioBlob, 'audio.webm');
      formData.append('userId', userId);
      formData.append('to', contactPhone);

      const response = await fetch('/api/whatsapp/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to send audio');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ 
        queryKey: ['/api/whatsapp/messages', userId, contactPhone] 
      });
      toast({
        title: "Áudio enviado",
        description: "Sua mensagem de áudio foi enviada com sucesso",
      });
      handleClose();
    },
    onError: () => {
      toast({
        title: "Erro no envio",
        description: "Não foi possível enviar o áudio",
        variant: "destructive",
      });
    },
  });

  const requestPermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100
        } 
      });
      setHasPermission(true);
      return stream;
    } catch (error) {
      console.error('Error accessing microphone:', error);
      setHasPermission(false);
      toast({
        title: "Permissão negada",
        description: "Acesso ao microfone é necessário para gravar áudio",
        variant: "destructive",
      });
      return null;
    }
  };

  const startRecording = async () => {
    const stream = await requestPermission();
    if (!stream) return;

    try {
      streamRef.current = stream;
      chunksRef.current = [];

      // Setup audio context for visualization
      audioContextRef.current = new AudioContext();
      const source = audioContextRef.current.createMediaStreamSource(stream);
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 256;
      source.connect(analyserRef.current);

      // Setup MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: 'audio/webm' });
        setAudioBlob(blob);
        setAudioUrl(URL.createObjectURL(blob));
      };

      mediaRecorder.start();
      setIsRecording(true);
      setDuration(0);

      // Start timer
      timerRef.current = setInterval(() => {
        setDuration(prev => prev + 1);
      }, 1000);

    } catch (error) {
      console.error('Error starting recording:', error);
      toast({
        title: "Erro na gravação",
        description: "Não foi possível iniciar a gravação",
        variant: "destructive",
      });
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
    }
    
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }
    
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
    }

    setIsRecording(false);
    setIsPaused(false);
  };

  const pauseRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      if (isPaused) {
        mediaRecorderRef.current.resume();
        timerRef.current = setInterval(() => {
          setDuration(prev => prev + 1);
        }, 1000);
      } else {
        mediaRecorderRef.current.pause();
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
      }
      setIsPaused(!isPaused);
    }
  };

  const playRecording = () => {
    if (audioRef.current && audioUrl) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const sendAudio = () => {
    if (audioBlob) {
      if (onSend) {
        onSend(audioBlob);
      } else {
        uploadMutation.mutate(audioBlob);
      }
    }
  };

  const handleClose = () => {
    if (isRecording) {
      stopRecording();
    }
    
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
    
    setAudioBlob(null);
    setAudioUrl(null);
    setDuration(0);
    setIsPlaying(false);
    onClose();
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  useEffect(() => {
    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold">Gravar Áudio</h2>
          <button
            onClick={handleClose}
            className="p-1 hover:bg-gray-100 rounded-full"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {hasPermission === false ? (
            <div className="text-center py-8">
              <MicOff className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Permissão Necessária
              </h3>
              <p className="text-gray-600 mb-4">
                Precisamos de acesso ao seu microfone para gravar áudio.
              </p>
              <button
                onClick={requestPermission}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
              >
                Permitir Acesso
              </button>
            </div>
          ) : (
            <>
              {/* Audio Visualizer */}
              {isRecording && (
                <div className="mb-6">
                  <AudioVisualizer 
                    analyser={analyserRef.current} 
                    isRecording={isRecording && !isPaused} 
                  />
                </div>
              )}

              {/* Audio Player */}
              {audioUrl && !isRecording && (
                <div className="mb-6">
                  <audio
                    ref={audioRef}
                    src={audioUrl}
                    onEnded={() => setIsPlaying(false)}
                    onPlay={() => setIsPlaying(true)}
                    onPause={() => setIsPlaying(false)}
                    className="hidden"
                  />
                  <div className="bg-gray-100 rounded-lg p-4 flex items-center space-x-3">
                    <button
                      onClick={playRecording}
                      className="p-2 bg-green-600 text-white rounded-full hover:bg-green-700"
                    >
                      {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                    </button>
                    <div className="flex-1">
                      <div className="text-sm font-medium">Gravação</div>
                      <div className="text-xs text-gray-600">{formatDuration(duration)}</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Timer */}
              <div className="text-center mb-6">
                <div className="text-2xl font-mono font-bold text-gray-900">
                  {formatDuration(duration)}
                </div>
                {isRecording && (
                  <div className="text-sm text-gray-600 mt-1">
                    {isPaused ? 'Pausado' : 'Gravando...'}
                  </div>
                )}
              </div>

              {/* Controls */}
              <div className="flex items-center justify-center space-x-4">
                {!isRecording && !audioBlob && (
                  <button
                    onClick={startRecording}
                    className="p-4 bg-red-600 text-white rounded-full hover:bg-red-700 shadow-lg"
                  >
                    <Mic className="w-6 h-6" />
                  </button>
                )}

                {isRecording && (
                  <>
                    <button
                      onClick={pauseRecording}
                      className="p-3 bg-yellow-600 text-white rounded-full hover:bg-yellow-700"
                    >
                      {isPaused ? <Play className="w-5 h-5" /> : <Pause className="w-5 h-5" />}
                    </button>
                    
                    <button
                      onClick={stopRecording}
                      className="p-3 bg-gray-600 text-white rounded-full hover:bg-gray-700"
                    >
                      <Square className="w-5 h-5" />
                    </button>
                  </>
                )}

                {audioBlob && !isRecording && (
                  <>
                    <button
                      onClick={() => {
                        setAudioBlob(null);
                        setAudioUrl(null);
                        setDuration(0);
                      }}
                      className="px-4 py-2 text-gray-600 hover:text-gray-800"
                    >
                      Cancelar
                    </button>
                    
                    <button
                      onClick={sendAudio}
                      disabled={uploadMutation.isPending}
                      className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center space-x-2"
                    >
                      <Send className="w-4 h-4" />
                      <span>
                        {uploadMutation.isPending ? 'Enviando...' : 'Enviar'}
                      </span>
                    </button>
                  </>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}