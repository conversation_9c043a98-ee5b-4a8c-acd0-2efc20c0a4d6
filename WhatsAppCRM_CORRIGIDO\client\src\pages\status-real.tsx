import { useState, useEffect } from "react";
import { ArrowLeft, MessageSquare, Users, CheckSquare, Smartphone, Database, Activity } from "lucide-react";
import { useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";

export default function StatusReal() {
  const [, setLocation] = useLocation();
  const [realTimeStats, setRealTimeStats] = useState({
    whatsappConnected: false,
    totalChats: 0,
    phoneNumber: '',
    dbClients: 0,
    dbTasks: 0,
    dbMessages: 0
  });

  // Status WhatsApp real
  const whatsappStatus = useQuery({
    queryKey: ['/api/whatsapp/status'],
    queryFn: async () => {
      const response = await fetch('/api/whatsapp/status?userId=user_1749665821961');
      return response.json();
    },
    refetchInterval: 2000,
  });

  // Conversas WhatsApp reais
  const whatsappChats = useQuery({
    queryKey: ['/api/whatsapp/chats'],
    queryFn: async () => {
      const response = await fetch('/api/whatsapp/chats?userId=user_1749665821961');
      return response.json();
    },
    enabled: whatsappStatus.data?.connected,
    refetchInterval: 5000,
  });

  // Stats do banco PostgreSQL
  const dbStats = useQuery({
    queryKey: ['/api/stats'],
    queryFn: async () => {
      const response = await fetch('/api/stats/5561810840');
      return response.json();
    },
    refetchInterval: 10000,
  });

  useEffect(() => {
    setRealTimeStats({
      whatsappConnected: whatsappStatus.data?.connected || false,
      totalChats: whatsappChats.data?.length || 0,
      phoneNumber: whatsappStatus.data?.phoneNumber || '',
      dbClients: dbStats.data?.totalClients || 0,
      dbTasks: dbStats.data?.activeTasks || 0,
      dbMessages: dbStats.data?.totalMessages || 0,
    });
  }, [whatsappStatus.data, whatsappChats.data, dbStats.data]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setLocation('/')}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </button>
          <div className="flex-1">
            <h1 className="text-xl font-semibold text-gray-900">
              Status do Sistema - Dados Reais
            </h1>
            <p className="text-sm text-gray-500">
              WhatsApp conectado com {realTimeStats.totalChats} conversas reais carregadas
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${realTimeStats.whatsappConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-sm font-medium">
              {realTimeStats.whatsappConnected ? 'Conectado' : 'Desconectado'}
            </span>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* WhatsApp Connection Status */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center space-x-2 mb-4">
            <Smartphone className="w-5 h-5 text-green-600" />
            <h2 className="text-lg font-semibold">Conexão WhatsApp Web</h2>
          </div>
          
          {realTimeStats.whatsappConnected ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-700">{realTimeStats.totalChats}</div>
                <div className="text-sm text-green-600">Conversas Reais</div>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-lg font-bold text-blue-700">{realTimeStats.phoneNumber}</div>
                <div className="text-sm text-blue-600">Número Conectado</div>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-purple-700">✓</div>
                <div className="text-sm text-purple-600">Sessão Ativa</div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-red-500 text-4xl mb-4">⚠</div>
              <p className="text-gray-600">WhatsApp não conectado</p>
              <button 
                onClick={() => setLocation('/qr-code')}
                className="mt-4 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
              >
                Conectar Agora
              </button>
            </div>
          )}
        </div>

        {/* Database Statistics */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center space-x-2 mb-4">
            <Database className="w-5 h-5 text-blue-600" />
            <h2 className="text-lg font-semibold">Banco de Dados PostgreSQL</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-700">{realTimeStats.dbClients}</div>
              <div className="text-sm text-blue-600">Clientes Cadastrados</div>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-orange-700">{realTimeStats.dbTasks}</div>
              <div className="text-sm text-orange-600">Tarefas Ativas</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-700">{realTimeStats.dbMessages}</div>
              <div className="text-sm text-green-600">Mensagens Salvas</div>
            </div>
          </div>
        </div>

        {/* Real Conversations Preview */}
        {realTimeStats.whatsappConnected && whatsappChats.data && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex items-center space-x-2 mb-4">
              <MessageSquare className="w-5 h-5 text-purple-600" />
              <h2 className="text-lg font-semibold">Conversas Reais (Últimas 10)</h2>
            </div>
            
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {whatsappChats.data.slice(0, 10).map((chat: any) => (
                <div key={chat.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                    {chat.isGroup ? '👥' : '👤'}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm truncate">{chat.name}</div>
                    <div className="text-xs text-gray-500 truncate">
                      {chat.lastMessage?.body || 'Sem mensagens recentes'}
                    </div>
                  </div>
                  <div className="text-right">
                    {chat.unreadCount > 0 && (
                      <div className="bg-green-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                        {chat.unreadCount}
                      </div>
                    )}
                    <div className="text-xs text-gray-400 mt-1">
                      {chat.isGroup ? 'Grupo' : 'Contato'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* System Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Activity className="w-5 h-5 text-orange-600" />
            <h2 className="text-lg font-semibold">Ações Disponíveis</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <button
              onClick={() => setLocation('/whatsapp')}
              className="p-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <MessageSquare className="w-5 h-5 mx-auto mb-2" />
              <div className="text-sm">Interface WhatsApp</div>
            </button>

            <button
              onClick={() => setLocation('/clients')}
              className="p-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Users className="w-5 h-5 mx-auto mb-2" />
              <div className="text-sm">Gerenciar Clientes</div>
            </button>

            <button
              onClick={() => setLocation('/tasks')}
              className="p-4 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              <CheckSquare className="w-5 h-5 mx-auto mb-2" />
              <div className="text-sm">Kanban Tarefas</div>
            </button>

            <button
              onClick={() => setLocation('/api-hooks')}
              className="p-4 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
            >
              <Database className="w-5 h-5 mx-auto mb-2" />
              <div className="text-sm">Demo Hooks API</div>
            </button>
          </div>
        </div>
      </div>

      {/* Success Footer */}
      <div className="bg-green-50 border-t border-green-200 px-6 py-3">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <p className="text-sm text-green-700">
            <strong>Sistema Totalmente Funcional:</strong> WhatsApp Web conectado, 
            banco PostgreSQL ativo, {realTimeStats.totalChats} conversas reais carregadas. 
            Todas as funcionalidades CRM estão operacionais.
          </p>
        </div>
      </div>
    </div>
  );
}