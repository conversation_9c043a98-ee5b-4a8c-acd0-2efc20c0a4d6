import { useEffect, useRef, useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';

interface WebSocketManagerState {
  isConnected: boolean;
  connectionState: 'connecting' | 'connected' | 'disconnected' | 'reconnecting';
  reconnectAttempts: number;
  lastError: string | null;
  messageQueue: any[];
  connectionId: string;
}

interface WebSocketManagerOptions {
  maxReconnectAttempts?: number;
  reconnectInterval?: number;
  heartbeatInterval?: number;
  onMessage?: (data: any) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: string) => void;
}

export function useWebSocketManager(options: WebSocketManagerOptions = {}) {
  const {
    maxReconnectAttempts = 5,
    reconnectInterval = 3000,
    heartbeatInterval = 30000,
    onMessage,
    onConnect,
    onDisconnect,
    onError
  } = options;

  const [state, setState] = useState<WebSocketManagerState>({
    isConnected: false,
    connectionState: 'disconnected',
    reconnectAttempts: 0,
    lastError: null,
    messageQueue: [],
    connectionId: '',
  });

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const { toast } = useToast();

  const getWebSocketUrl = useCallback(() => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    return `${protocol}//${host}`;
  }, []);

  const sendHeartbeat = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({ type: 'ping' }));
      
      // Schedule next heartbeat
      heartbeatTimeoutRef.current = setTimeout(sendHeartbeat, heartbeatInterval);
    }
  }, [heartbeatInterval]);

  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const data = JSON.parse(event.data);
      
      // Handle pong response
      if (data.type === 'pong') {
        return;
      }

      // Emit events for different message types
      if (data.type === 'message:received') {
        window.dispatchEvent(new CustomEvent('websocket:message:received', { detail: data }));
      } else if (data.type === 'message:sent') {
        window.dispatchEvent(new CustomEvent('websocket:message:sent', { detail: data }));
      } else if (data.type === 'whatsapp:connected') {
        window.dispatchEvent(new CustomEvent('websocket:whatsapp:connected', { detail: data }));
      } else if (data.type === 'whatsapp:disconnected') {
        window.dispatchEvent(new CustomEvent('websocket:whatsapp:disconnected', { detail: data }));
      } else if (data.type === 'typing:start') {
        window.dispatchEvent(new CustomEvent('websocket:typing:start', { detail: data }));
      } else if (data.type === 'typing:stop') {
        window.dispatchEvent(new CustomEvent('websocket:typing:stop', { detail: data }));
      }

      onMessage?.(data);
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }, [onMessage]);

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setState(prev => ({ 
      ...prev, 
      connectionState: prev.reconnectAttempts > 0 ? 'reconnecting' : 'connecting',
      lastError: null 
    }));

    try {
      const ws = new WebSocket(getWebSocketUrl());
      wsRef.current = ws;

      ws.onopen = () => {
        setState(prev => ({
          ...prev,
          isConnected: true,
          connectionState: 'connected',
          reconnectAttempts: 0,
          lastError: null,
        }));

        // Start heartbeat
        sendHeartbeat();
        
        onConnect?.();
        
        if (state.reconnectAttempts > 0) {
          toast({
            title: "Conexão reestabelecida",
            description: "WebSocket reconectado com sucesso",
          });
        }
      };

      ws.onmessage = handleMessage;

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        const errorMessage = 'Erro de conexão WebSocket';
        
        setState(prev => ({ 
          ...prev, 
          lastError: errorMessage 
        }));

        onError?.(errorMessage);
      };

      ws.onclose = (event) => {
        setState(prev => ({
          ...prev,
          isConnected: false,
          connectionState: 'disconnected',
        }));

        // Clear heartbeat
        if (heartbeatTimeoutRef.current) {
          clearTimeout(heartbeatTimeoutRef.current);
          heartbeatTimeoutRef.current = null;
        }

        onDisconnect?.();

        // Attempt reconnection if not manually closed
        if (event.code !== 1000) {
          setState(prev => {
            const newAttempts = prev.reconnectAttempts + 1;
            
            if (newAttempts <= maxReconnectAttempts) {
              reconnectTimeoutRef.current = setTimeout(() => {
                connect();
              }, reconnectInterval * Math.min(newAttempts, 3)); // Exponential backoff
              
              toast({
                title: "Conexão perdida",
                description: `Tentativa de reconexão ${newAttempts}/${maxReconnectAttempts}`,
                variant: "destructive"
              });
            }
            
            return { ...prev, reconnectAttempts: newAttempts };
          });
        }
        
        // Show final failure message
        if (state.reconnectAttempts >= maxReconnectAttempts) {
          toast({
            title: "Falha na conexão",
            description: "Não foi possível reestabelecer a conexão WebSocket",
            variant: "destructive"
          });
        }
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setState(prev => ({ 
        ...prev, 
        lastError: 'Falha ao criar conexão WebSocket' 
      }));
    }
  }, [getWebSocketUrl, handleMessage, maxReconnectAttempts, reconnectInterval, sendHeartbeat, onConnect, onDisconnect, onError, state.reconnectAttempts, toast]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (heartbeatTimeoutRef.current) {
      clearTimeout(heartbeatTimeoutRef.current);
      heartbeatTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    setState({
      isConnected: false,
      connectionState: 'disconnected',
      reconnectAttempts: 0,
      lastError: null,
      messageQueue: [],
      connectionId: '',
    });
  }, []);

  const reconnect = useCallback(() => {
    disconnect();
    setState(prev => ({ ...prev, reconnectAttempts: 0 }));
    connect();
  }, [connect, disconnect]);

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
      return true;
    }
    return false;
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (heartbeatTimeoutRef.current) {
        clearTimeout(heartbeatTimeoutRef.current);
      }
    };
  }, []);

  return {
    ...state,
    connect,
    disconnect,
    reconnect,
    sendMessage,
  };
}