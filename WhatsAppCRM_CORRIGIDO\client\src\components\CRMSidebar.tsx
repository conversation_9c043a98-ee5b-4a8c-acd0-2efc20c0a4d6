import { useState, useRef } from "react";
import { 
  User, Mail, Phone, Building, Tag, Plus, Edit3, Save, X, 
  MessageSquare, Calendar, FileText, Clock, Star
} from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import KanbanBoard from "./KanbanBoard";

interface CRMSidebarProps {
  contactPhone: string;
  whatsappNumber: string;
  userId: string;
  isOpen: boolean;
  onClose: () => void;
}

interface ContactInfo {
  id?: number;
  phone: string;
  name?: string;
  email?: string;
  company?: string;
  tags?: string[];
  notes?: string;
  lastInteraction?: string;
  totalMessages?: number;
  responseTime?: number;
}

interface Note {
  id: number;
  content: string;
  createdAt: string;
  authorId: string;
  authorName: string;
}

export default function CRMSidebar({ 
  contactPhone, 
  whatsappNumber, 
  userId, 
  isOpen, 
  onClose 
}: CRMSidebarProps) {
  const [activeTab, setActiveTab] = useState<'info' | 'notes' | 'tasks'>('info');
  const [isEditingContact, setIsEditingContact] = useState(false);
  const [newNote, setNewNote] = useState("");
  const [newTag, setNewTag] = useState("");
  const [contactData, setContactData] = useState<ContactInfo>({
    phone: contactPhone,
    name: '',
    email: '',
    company: '',
    tags: [],
    notes: ''
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get contact information
  const { data: contact, isLoading: loadingContact } = useQuery({
    queryKey: ['/api/contacts', whatsappNumber, contactPhone],
    queryFn: async () => {
      const response = await fetch(`/api/contacts?whatsappNumber=${whatsappNumber}&phone=${contactPhone}`);
      if (!response.ok) throw new Error('Failed to fetch contact');
      return response.json();
    },
    enabled: isOpen && !!contactPhone,
  });

  // Get contact notes
  const { data: notes = [] } = useQuery({
    queryKey: ['/api/contacts/notes', contact?.id],
    queryFn: async () => {
      if (!contact?.id) return [];
      const response = await fetch(`/api/contacts/${contact.id}/notes`);
      if (!response.ok) throw new Error('Failed to fetch notes');
      return response.json();
    },
    enabled: isOpen && !!contact?.id,
  });

  // Update contact mutation
  const updateContactMutation = useMutation({
    mutationFn: async (data: Partial<ContactInfo>) => {
      const url = contact?.id ? `/api/contacts/${contact.id}` : '/api/contacts';
      const method = contact?.id ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...data,
          whatsappNumber,
          phone: contactPhone
        }),
      });
      
      if (!response.ok) throw new Error('Failed to update contact');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/contacts'] });
      setIsEditingContact(false);
      toast({
        title: "Contato atualizado",
        description: "Informações salvas com sucesso",
      });
    },
    onError: () => {
      toast({
        title: "Erro",
        description: "Não foi possível salvar as informações",
        variant: "destructive",
      });
    },
  });

  // Add note mutation
  const addNoteMutation = useMutation({
    mutationFn: async (content: string) => {
      if (!contact?.id) throw new Error('Contact not found');
      
      const response = await fetch(`/api/contacts/${contact.id}/notes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content,
          authorId: userId,
          authorName: 'Usuário' // In production, get from user session
        }),
      });
      
      if (!response.ok) throw new Error('Failed to add note');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/contacts/notes'] });
      setNewNote("");
      toast({
        title: "Nota adicionada",
        description: "Nova nota interna criada",
      });
    },
  });

  const handleSaveContact = () => {
    updateContactMutation.mutate(contactData);
  };

  const handleAddNote = () => {
    if (newNote.trim()) {
      addNoteMutation.mutate(newNote.trim());
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && !contactData.tags?.includes(newTag.trim())) {
      const updatedTags = [...(contactData.tags || []), newTag.trim()];
      setContactData(prev => ({ ...prev, tags: updatedTags }));
      updateContactMutation.mutate({ tags: updatedTags });
      setNewTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const updatedTags = contactData.tags?.filter(tag => tag !== tagToRemove) || [];
    setContactData(prev => ({ ...prev, tags: updatedTags }));
    updateContactMutation.mutate({ tags: updatedTags });
  };

  if (!isOpen) return null;

  return (
    <div className="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            CRM do Contato
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('info')}
            className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'info'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <User className="w-4 h-4 mx-auto" />
          </button>
          <button
            onClick={() => setActiveTab('notes')}
            className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'notes'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <FileText className="w-4 h-4 mx-auto" />
          </button>
          <button
            onClick={() => setActiveTab('tasks')}
            className={`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'tasks'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <Calendar className="w-4 h-4 mx-auto" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'info' && (
          <div className="p-4 space-y-6">
            {/* Contact Info */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                  Informações do Contato
                </h3>
                <button
                  onClick={() => {
                    if (isEditingContact) {
                      handleSaveContact();
                    } else {
                      setIsEditingContact(true);
                      setContactData({
                        phone: contactPhone,
                        name: contact?.name || '',
                        email: contact?.email || '',
                        company: contact?.company || '',
                        tags: contact?.tags || [],
                        notes: contact?.notes || ''
                      });
                    }
                  }}
                  disabled={updateContactMutation.isPending}
                  className="p-2 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-full"
                >
                  {isEditingContact ? (
                    <Save className="w-4 h-4" />
                  ) : (
                    <Edit3 className="w-4 h-4" />
                  )}
                </button>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Phone className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-900 dark:text-white font-mono">
                    {contactPhone}
                  </span>
                </div>

                <div className="flex items-center space-x-3">
                  <User className="w-4 h-4 text-gray-400" />
                  {isEditingContact ? (
                    <input
                      type="text"
                      value={contactData.name}
                      onChange={(e) => setContactData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Nome do contato"
                      className="flex-1 text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  ) : (
                    <span className="text-sm text-gray-900 dark:text-white">
                      {contact?.name || 'Nome não informado'}
                    </span>
                  )}
                </div>

                <div className="flex items-center space-x-3">
                  <Mail className="w-4 h-4 text-gray-400" />
                  {isEditingContact ? (
                    <input
                      type="email"
                      value={contactData.email}
                      onChange={(e) => setContactData(prev => ({ ...prev, email: e.target.value }))}
                      placeholder="<EMAIL>"
                      className="flex-1 text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  ) : (
                    <span className="text-sm text-gray-900 dark:text-white">
                      {contact?.email || 'Email não informado'}
                    </span>
                  )}
                </div>

                <div className="flex items-center space-x-3">
                  <Building className="w-4 h-4 text-gray-400" />
                  {isEditingContact ? (
                    <input
                      type="text"
                      value={contactData.company}
                      onChange={(e) => setContactData(prev => ({ ...prev, company: e.target.value }))}
                      placeholder="Nome da empresa"
                      className="flex-1 text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  ) : (
                    <span className="text-sm text-gray-900 dark:text-white">
                      {contact?.company || 'Empresa não informada'}
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Tags */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">Tags</h4>
              
              <div className="flex flex-wrap gap-2">
                {(contact?.tags || contactData.tags || []).map((tag: string) => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded-full"
                  >
                    <Tag className="w-3 h-3 mr-1" />
                    {tag}
                    {isEditingContact && (
                      <button
                        onClick={() => handleRemoveTag(tag)}
                        className="ml-1 hover:text-red-600"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    )}
                  </span>
                ))}
              </div>

              {isEditingContact && (
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="Nova tag"
                    className="flex-1 text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                  />
                  <button
                    onClick={handleAddTag}
                    className="p-1 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
              )}
            </div>

            {/* Statistics */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">Estatísticas</h4>
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <MessageSquare className="w-4 h-4 text-blue-600" />
                    <span className="text-xs text-gray-600 dark:text-gray-400">Mensagens</span>
                  </div>
                  <div className="text-lg font-semibold text-gray-900 dark:text-white mt-1">
                    {contact?.totalMessages || 0}
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-green-600" />
                    <span className="text-xs text-gray-600 dark:text-gray-400">Resp. Média</span>
                  </div>
                  <div className="text-lg font-semibold text-gray-900 dark:text-white mt-1">
                    {contact?.responseTime || 0}min
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'notes' && (
          <div className="p-4 space-y-4">
            {/* Add new note */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">Nova Nota Interna</h4>
              <div className="space-y-2">
                <textarea
                  value={newNote}
                  onChange={(e) => setNewNote(e.target.value)}
                  placeholder="Adicione uma nota interna sobre este contato..."
                  className="w-full text-sm border border-gray-300 dark:border-gray-600 rounded px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white h-20 resize-none"
                />
                <button
                  onClick={handleAddNote}
                  disabled={!newNote.trim() || addNoteMutation.isPending}
                  className="w-full py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {addNoteMutation.isPending ? 'Salvando...' : 'Adicionar Nota'}
                </button>
              </div>
            </div>

            {/* Notes list */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                Histórico de Notas ({notes.length})
              </h4>
              
              {notes.length === 0 ? (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <FileText className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm">Nenhuma nota ainda</p>
                </div>
              ) : (
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {notes.map((note: Note) => (
                    <div
                      key={note.id}
                      className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                          {note.authorName}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-500">
                          {new Date(note.createdAt).toLocaleDateString('pt-BR')}
                        </span>
                      </div>
                      <p className="text-sm text-gray-900 dark:text-white">
                        {note.content}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'tasks' && (
          <div className="p-4">
            <KanbanBoard
              contactPhone={contactPhone}
              whatsappNumber={whatsappNumber}
              userId={userId}
            />
          </div>
        )}
      </div>
    </div>
  );
}