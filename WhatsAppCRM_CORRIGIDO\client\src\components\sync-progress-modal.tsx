import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { CheckCircle, AlertCircle, Clock, Download, X } from "lucide-react";
import { useQuery } from "@tanstack/react-query";

interface SyncLog {
  id: string;
  operation: string;
  status: string;
  startedAt: string;
  completedAt?: string;
  totalItems: number;
  processedItems: number;
  failedItems: number;
  errorMessage?: string;
  duration?: number;
}

interface SyncProgressModalProps {
  isOpen: boolean;
  onClose: () => void;
  sessionId: string;
  onSyncComplete?: () => void;
}

export function SyncProgressModal({ isOpen, onClose, sessionId, onSyncComplete }: SyncProgressModalProps) {
  const [activeSyncs, setActiveSyncs] = useState<SyncLog[]>([]);
  const [completedSyncs, setCompletedSyncs] = useState<SyncLog[]>([]);

  // Fetch sync logs
  const { data: syncLogs, refetch } = useQuery({
    queryKey: ['sync-logs', sessionId],
    queryFn: async () => {
      const response = await fetch(`/api/sync/logs/${sessionId}`);
      if (!response.ok) throw new Error('Failed to fetch sync logs');
      return response.json() as Promise<SyncLog[]>;
    },
    enabled: isOpen && !!sessionId,
    refetchInterval: 1000, // Refetch every second for real-time updates
  });

  useEffect(() => {
    if (syncLogs) {
      const active = syncLogs.filter(log => log.status === 'started' || log.status === 'progress');
      const completed = syncLogs.filter(log => log.status === 'completed' || log.status === 'failed');
      
      setActiveSyncs(active);
      setCompletedSyncs(completed);
    }
  }, [syncLogs]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'started':
      case 'progress':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'started':
      case 'progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const calculateProgress = (log: SyncLog) => {
    if (log.totalItems === 0) return 0;
    return Math.round((log.processedItems / log.totalItems) * 100);
  };

  const formatDuration = (duration?: number) => {
    if (!duration) return 'N/A';
    const seconds = Math.floor(duration / 1000);
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    return `${minutes}m ${seconds % 60}s`;
  };

  const formatOperation = (operation: string) => {
    switch (operation) {
      case 'sync_messages':
        return 'Sincronizando Mensagens';
      case 'sync_chats':
        return 'Sincronizando Conversas';
      case 'sync_contacts':
        return 'Sincronizando Contatos';
      case 'full_sync':
        return 'Sincronização Completa';
      default:
        return operation;
    }
  };

  const hasActiveSyncs = activeSyncs.length > 0;
  const totalProgress = activeSyncs.length > 0 
    ? Math.round(activeSyncs.reduce((acc, log) => acc + calculateProgress(log), 0) / activeSyncs.length)
    : 100;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Progresso da Sincronização
            </DialogTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Overall Progress */}
          {hasActiveSyncs && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Progresso Geral</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Sincronização em andamento...</span>
                    <span>{totalProgress}%</span>
                  </div>
                  <Progress value={totalProgress} className="h-2" />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Active Syncs */}
          {activeSyncs.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Operações Ativas</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-32">
                  <div className="space-y-3">
                    {activeSyncs.map((log) => (
                      <div key={log.id} className="border rounded-lg p-3 space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(log.status)}
                            <span className="font-medium">{formatOperation(log.operation)}</span>
                            <Badge className={getStatusColor(log.status)}>
                              {log.status === 'started' ? 'Iniciado' : 'Em Progresso'}
                            </Badge>
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {log.processedItems}/{log.totalItems}
                          </span>
                        </div>
                        <div className="space-y-1">
                          <Progress value={calculateProgress(log)} className="h-1" />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>Iniciado: {new Date(log.startedAt).toLocaleTimeString()}</span>
                            <span>{calculateProgress(log)}% concluído</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          )}

          {/* Completed Syncs */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Histórico de Sincronização</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-48">
                {completedSyncs.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    Nenhuma sincronização concluída ainda
                  </div>
                ) : (
                  <div className="space-y-3">
                    {completedSyncs.map((log) => (
                      <div key={log.id} className="border rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(log.status)}
                            <span className="font-medium">{formatOperation(log.operation)}</span>
                            <Badge className={getStatusColor(log.status)}>
                              {log.status === 'completed' ? 'Concluído' : 'Falhou'}
                            </Badge>
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {formatDuration(log.duration)}
                          </span>
                        </div>
                        
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">Processados:</span>
                            <div className="font-medium">{log.processedItems}</div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Total:</span>
                            <div className="font-medium">{log.totalItems}</div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Falhas:</span>
                            <div className="font-medium text-red-600">{log.failedItems}</div>
                          </div>
                        </div>

                        {log.errorMessage && (
                          <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded text-sm">
                            <span className="text-red-600 dark:text-red-400">Erro: </span>
                            {log.errorMessage}
                          </div>
                        )}

                        <div className="text-xs text-muted-foreground mt-2">
                          Concluído: {log.completedAt ? new Date(log.completedAt).toLocaleString() : 'N/A'}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-between">
            <Button variant="outline" onClick={() => refetch()}>
              Atualizar
            </Button>
            <div className="flex gap-2">
              {!hasActiveSyncs && onSyncComplete && (
                <Button onClick={onSyncComplete}>
                  Iniciar Nova Sincronização
                </Button>
              )}
              <Button variant="secondary" onClick={onClose}>
                Fechar
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}