import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface ThemeState {
  theme: 'light' | 'dark';
  setTheme: (theme: 'light' | 'dark') => void;
  toggleTheme: () => void;
}

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      theme: 'light',
      setTheme: (theme) => {
        set({ theme });
        document.documentElement.classList.toggle('dark', theme === 'dark');
      },
      toggleTheme: () => {
        const currentTheme = get().theme;
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        get().setTheme(newTheme);
      },
    }),
    {
      name: 'casa-das-camisetas-theme',
      onRehydrateStorage: () => (state) => {
        if (state) {
          document.documentElement.classList.toggle('dark', state.theme === 'dark');
        }
      },
    }
  )
);

// Initialize theme on first load
if (typeof window !== 'undefined') {
  const savedTheme = localStorage.getItem('casa-das-camisetas-theme');
  if (savedTheme) {
    try {
      const parsed = JSON.parse(savedTheme);
      document.documentElement.classList.toggle('dark', parsed.state?.theme === 'dark');
    } catch (e) {
      // Fallback to light theme
      document.documentElement.classList.remove('dark');
    }
  }
}