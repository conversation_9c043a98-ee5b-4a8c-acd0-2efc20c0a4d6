import { useState } from "react";
import WhatsAppLayout from "@/components/WhatsAppLayout";
import WhatsAppChat from "@/components/WhatsAppChat";
import { useWhatsApp } from "@/hooks/use-whatsapp";
import { useLocation } from "wouter";
import { MessageSquare, QrCode } from "lucide-react";

export default function WhatsAppLayoutPage() {
  const [selectedContact, setSelectedContact] = useState<string>("");
  const [selectedContactName, setSelectedContactName] = useState<string>("");
  const { isConnected } = useWhatsApp();
  const [, setLocation] = useLocation();

  if (!isConnected) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50">
        <div className="text-center max-w-md">
          <QrCode className="w-24 h-24 text-gray-400 mx-auto mb-6" />
          <h2 className="text-2xl font-semibold text-gray-700 mb-4">Conecte seu WhatsApp</h2>
          <p className="text-gray-500 mb-6">
            Para usar o WhatsApp Web, você precisa escanear o código QR com seu telefone.
          </p>
          <button 
            onClick={() => setLocation('/qr-code')}
            className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
          >
            Conectar WhatsApp
          </button>
        </div>
      </div>
    );
  }

  return (
    <WhatsAppLayout
      selectedContact={selectedContact}
      onContactSelect={(contactId: string, contactName: string) => {
        setSelectedContact(contactId);
        setSelectedContactName(contactName);
      }}
    >
      {selectedContact ? (
        <WhatsAppChat 
          contactId={selectedContact} 
          contactName={selectedContactName}
          contactPhone={selectedContact}
        />
      ) : (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <MessageSquare className="w-24 h-24 text-gray-300 mx-auto mb-6" />
            <h2 className="text-2xl font-light text-gray-600 mb-2">WhatsApp Web</h2>
            <p className="text-gray-500 max-w-md">
              Envie e receba mensagens sem manter seu telefone conectado à internet.
            </p>
            <p className="text-sm text-gray-400 mt-4">
              Selecione uma conversa para começar a enviar mensagens.
            </p>
          </div>
        </div>
      )}
    </WhatsAppLayout>
  );
}