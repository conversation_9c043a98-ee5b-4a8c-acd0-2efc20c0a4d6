import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { useQueryClient } from '@tanstack/react-query';

interface WebSocketConfig {
  userId: string;
  whatsappNumber?: string;
  onMessageReceived?: (message: any) => void;
  onMessageSent?: (message: any) => void;
  onMessageDeleted?: (message: any) => void;
  onMessageEdited?: (message: any) => void;
  onStatusUpdate?: (status: any) => void;
  onTypingStart?: (data: any) => void;
  onTypingStop?: (data: any) => void;
  onKanbanUpdate?: (task: any) => void;
  onCRMUpdate?: (contact: any) => void;
  onDesktopNotification?: (notification: any) => void;
}

export function useWebSocketRealtime(config: WebSocketConfig) {
  const socketRef = useRef<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const queryClient = useQueryClient();
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  useEffect(() => {
    const socket = io('/', {
      transports: ['websocket', 'polling'],
      timeout: 10000,
      forceNew: true
    });

    socketRef.current = socket;

    // Handle connection
    socket.on('connect', () => {
      console.log('WebSocket connected:', socket.id);
      setConnected(true);
      setConnectionError(null);
      reconnectAttempts.current = 0;

      // Authenticate with server
      socket.emit('authenticate', {
        userId: config.userId,
        whatsappNumber: config.whatsappNumber
      });
    });

    socket.on('authenticated', (data) => {
      console.log('WebSocket authenticated:', data);
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      console.log('WebSocket disconnected');
      setConnected(false);
    });

    // Handle connection errors
    socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      setConnectionError(error.message);
      setConnected(false);
      
      reconnectAttempts.current++;
      if (reconnectAttempts.current >= maxReconnectAttempts) {
        socket.disconnect();
      }
    });

    // Message events
    socket.on('message:received', (data) => {
      console.log('Message received via WebSocket:', data);
      config.onMessageReceived?.(data);
      
      // Invalidate message cache
      queryClient.invalidateQueries({ 
        queryKey: ['/api/whatsapp/messages', config.userId, data.chatId] 
      });
      
      // Show desktop notification for new messages
      if (data.fromMe === false) {
        showDesktopNotification({
          title: data.senderName || 'Nova mensagem',
          body: data.body || 'Mensagem de mídia',
          icon: '/favicon.ico',
          tag: `message-${data.id}`
        });
      }
    });

    socket.on('message:sent', (data) => {
      console.log('Message sent via WebSocket:', data);
      config.onMessageSent?.(data);
      
      queryClient.invalidateQueries({ 
        queryKey: ['/api/whatsapp/messages', config.userId, data.chatId] 
      });
    });

    socket.on('message:deleted', (data) => {
      console.log('Message deleted via WebSocket:', data);
      config.onMessageDeleted?.(data);
      
      queryClient.invalidateQueries({ 
        queryKey: ['/api/whatsapp/messages', config.userId, data.chatId] 
      });
    });

    socket.on('message:edited', (data) => {
      console.log('Message edited via WebSocket:', data);
      config.onMessageEdited?.(data);
      
      queryClient.invalidateQueries({ 
        queryKey: ['/api/whatsapp/messages', config.userId, data.chatId] 
      });
    });

    // Status events
    socket.on('whatsapp:status:changed', (data) => {
      console.log('WhatsApp status changed:', data);
      config.onStatusUpdate?.(data);
      
      queryClient.invalidateQueries({ 
        queryKey: ['/api/whatsapp/status'] 
      });
    });

    socket.on('status:update', (data) => {
      console.log('User status update:', data);
      config.onStatusUpdate?.(data);
    });

    // Typing indicators
    socket.on('typing:started', (data) => {
      console.log('User started typing:', data);
      config.onTypingStart?.(data);
    });

    socket.on('typing:stopped', (data) => {
      console.log('User stopped typing:', data);
      config.onTypingStop?.(data);
    });

    // Kanban events
    socket.on('kanban:task:updated', (data) => {
      console.log('Kanban task updated:', data);
      config.onKanbanUpdate?.(data);
      
      queryClient.invalidateQueries({ 
        queryKey: ['/api/tasks'] 
      });
    });

    // CRM events
    socket.on('crm:contact:updated', (data) => {
      console.log('CRM contact updated:', data);
      config.onCRMUpdate?.(data);
      
      queryClient.invalidateQueries({ 
        queryKey: ['/api/contacts'] 
      });
    });

    // System notifications
    socket.on('system:notification', (notification) => {
      // Show system notification (toast)
      console.log('System notification:', notification);
    });

    socket.on('desktop:notification', (notification) => {
      console.log('Desktop notification:', notification);
      config.onDesktopNotification?.(notification);
      showDesktopNotification(notification);
    });

    // Ping/pong for connection health
    const pingInterval = setInterval(() => {
      if (socket.connected) {
        socket.emit('ping');
      }
    }, 30000);

    socket.on('pong', (data) => {
      console.log('Pong received:', data);
    });

    return () => {
      clearInterval(pingInterval);
      socket.disconnect();
    };
  }, [config.userId, config.whatsappNumber]);

  // Helper functions
  const sendTypingStart = (chatId: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('typing:start', {
        chatId,
        userId: config.userId
      });
    }
  };

  const sendTypingStop = (chatId: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('typing:stop', {
        chatId,
        userId: config.userId
      });
    }
  };

  const sendMessageSent = (messageData: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('message:send', messageData);
    }
  };

  const sendKanbanUpdate = (taskData: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('kanban:task:update', taskData);
    }
  };

  const sendCRMUpdate = (contactData: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('crm:contact:update', contactData);
    }
  };

  return {
    connected,
    connectionError,
    sendTypingStart,
    sendTypingStop,
    sendMessageSent,
    sendKanbanUpdate,
    sendCRMUpdate,
    socket: socketRef.current
  };
}

// Desktop notification helper
function showDesktopNotification(notification: {
  title: string;
  body: string;
  icon?: string;
  tag?: string;
  requireInteraction?: boolean;
}) {
  // Check if notifications are supported
  if (!('Notification' in window)) {
    console.log('This browser does not support desktop notifications');
    return;
  }

  // Check permission
  if (Notification.permission === 'granted') {
    new Notification(notification.title, {
      body: notification.body,
      icon: notification.icon || '/favicon.ico',
      tag: notification.tag,
      requireInteraction: notification.requireInteraction || false
    });
  } else if (Notification.permission !== 'denied') {
    // Request permission
    Notification.requestPermission().then((permission) => {
      if (permission === 'granted') {
        new Notification(notification.title, {
          body: notification.body,
          icon: notification.icon || '/favicon.ico',
          tag: notification.tag,
          requireInteraction: notification.requireInteraction || false
        });
      }
    });
  }
}

// Hook for requesting notification permissions
export function useDesktopNotifications() {
  const [permission, setPermission] = useState<NotificationPermission>('default');

  useEffect(() => {
    if ('Notification' in window) {
      setPermission(Notification.permission);
    }
  }, []);

  const requestPermission = async () => {
    if ('Notification' in window) {
      const result = await Notification.requestPermission();
      setPermission(result);
      return result;
    }
    return 'denied';
  };

  return {
    permission,
    requestPermission,
    isSupported: 'Notification' in window
  };
}