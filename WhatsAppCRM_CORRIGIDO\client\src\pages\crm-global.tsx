import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { toast } from '@/hooks/use-toast';
import { useWebSocket } from '@/hooks/useWebSocket';
import { useThemeStore } from '@/lib/theme-store';
import { apiRequest } from '@/lib/queryClient';
import { 
  Users, 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Phone, 
  Mail, 
  Building, 
  User, 
  Calendar,
  Star,
  RefreshCw,
  Moon,
  Sun,
  Activity
} from 'lucide-react';

interface CrmClient {
  id: string;
  name: string;
  email?: string;
  phoneNumber: string;
  company?: string;
  position?: string;
  status: 'lead' | 'prospect' | 'client' | 'inactive';
  salesRepresentative: string;
  notes?: string;
  tags?: string[];
  lastContact?: Date;
  value?: number;
  source?: string;
  createdAt: Date;
  updatedAt: Date;
}

const statusOptions = [
  { value: 'lead', label: 'Lead', color: 'bg-blue-500' },
  { value: 'prospect', label: 'Prospect', color: 'bg-yellow-500' },
  { value: 'client', label: 'Cliente', color: 'bg-green-500' },
  { value: 'inactive', label: 'Inativo', color: 'bg-gray-500' },
];

const salesRepresentatives = [
  'Ariane da Silva Ramos',
  'Erica Ferreira da Costa',
  'Gabrielly Araújo Rodrigues',
  'Ellen Ribeiro Lopes',
  'Jennyfer Cristine de Souza Santana'
];

export default function CrmGlobal() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [salesRepFilter, setSalesRepFilter] = useState<string>('');
  const [dateFilter, setDateFilter] = useState<string>('');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingClient, setEditingClient] = useState<CrmClient | null>(null);
  const [formData, setFormData] = useState<{
    name: string;
    email: string;
    phoneNumber: string;
    company: string;
    position: string;
    status: 'lead' | 'prospect' | 'client' | 'inactive';
    salesRepresentative: string;
    notes: string;
    value: string;
    source: string;
  }>({
    name: '',
    email: '',
    phoneNumber: '',
    company: '',
    position: '',
    status: 'lead',
    salesRepresentative: '',
    notes: '',
    value: '',
    source: ''
  });

  const queryClient = useQueryClient();
  const { theme, toggleTheme } = useThemeStore();
  const { connected, emit } = useWebSocket({
    userId: `user_${Date.now()}`,
    autoConnect: true
  });

  // Fetch CRM clients
  const { data: clients = [], isLoading, refetch } = useQuery<CrmClient[]>({
    queryKey: ['/api/crm/clients'],
    refetchInterval: 30000
  });

  // Real-time WebSocket events
  useEffect(() => {
    const handleClientCreated = (event: CustomEvent) => {
      queryClient.invalidateQueries({ queryKey: ['/api/crm/clients'] });
      toast({
        title: "Cliente criado",
        description: `Novo cliente: ${event.detail.client.name}`,
      });
    };

    const handleClientUpdated = (event: CustomEvent) => {
      queryClient.invalidateQueries({ queryKey: ['/api/crm/clients'] });
      toast({
        title: "Cliente atualizado",
        description: `Cliente ${event.detail.client.name} foi atualizado`,
      });
    };

    const handleClientDeleted = (event: CustomEvent) => {
      queryClient.invalidateQueries({ queryKey: ['/api/crm/clients'] });
      toast({
        title: "Cliente removido",
        description: "Cliente foi removido do sistema",
        variant: "destructive"
      });
    };

    window.addEventListener('websocket:crm:client:created', handleClientCreated as EventListener);
    window.addEventListener('websocket:crm:client:updated', handleClientUpdated as EventListener);
    window.addEventListener('websocket:crm:client:deleted', handleClientDeleted as EventListener);

    return () => {
      window.removeEventListener('websocket:crm:client:created', handleClientCreated as EventListener);
      window.removeEventListener('websocket:crm:client:updated', handleClientUpdated as EventListener);
      window.removeEventListener('websocket:crm:client:deleted', handleClientDeleted as EventListener);
    };
  }, [queryClient]);

  // Create client mutation
  const createClientMutation = useMutation({
    mutationFn: (clientData: any) => apiRequest('POST', '/api/crm/clients', {
      ...clientData,
      id: `client_${Date.now()}`,
      value: clientData.value ? parseFloat(clientData.value) : null,
      tags: []
    }),
    onSuccess: async (response) => {
      const data = await response.json();
      queryClient.invalidateQueries({ queryKey: ['/api/crm/clients'] });
      setIsDialogOpen(false);
      resetForm();
      emit('crm:client:create', data);
      toast({
        title: "Cliente criado com sucesso",
        description: `${data.name} foi adicionado ao CRM`,
      });
    },
    onError: () => {
      toast({
        title: "Erro ao criar cliente",
        description: "Tente novamente",
        variant: "destructive"
      });
    }
  });

  // Update client mutation
  const updateClientMutation = useMutation({
    mutationFn: ({ id, ...clientData }: any) => apiRequest('PUT', `/api/crm/clients/${id}`, {
      ...clientData,
      value: clientData.value ? parseFloat(clientData.value) : null
    }),
    onSuccess: async (response) => {
      const data = await response.json();
      queryClient.invalidateQueries({ queryKey: ['/api/crm/clients'] });
      setIsDialogOpen(false);
      setEditingClient(null);
      resetForm();
      emit('crm:client:update', data);
      toast({
        title: "Cliente atualizado",
        description: `${data.name} foi atualizado com sucesso`,
      });
    },
    onError: () => {
      toast({
        title: "Erro ao atualizar cliente",
        description: "Tente novamente",
        variant: "destructive"
      });
    }
  });

  // Delete client mutation
  const deleteClientMutation = useMutation({
    mutationFn: (id: string) => apiRequest('DELETE', `/api/crm/clients/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/crm/clients'] });
      toast({
        title: "Cliente removido",
        description: "Cliente foi removido do sistema",
      });
    },
    onError: () => {
      toast({
        title: "Erro ao remover cliente",
        description: "Tente novamente",
        variant: "destructive"
      });
    }
  });

  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      phoneNumber: '',
      company: '',
      position: '',
      status: 'lead',
      salesRepresentative: '',
      notes: '',
      value: '',
      source: ''
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingClient) {
      updateClientMutation.mutate({ id: editingClient.id, ...formData });
    } else {
      createClientMutation.mutate(formData);
    }
  };

  const handleEdit = (client: CrmClient) => {
    setEditingClient(client);
    setFormData({
      name: client.name,
      email: client.email || '',
      phoneNumber: client.phoneNumber,
      company: client.company || '',
      position: client.position || '',
      status: client.status,
      salesRepresentative: client.salesRepresentative,
      notes: client.notes || '',
      value: client.value?.toString() || '',
      source: client.source || ''
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (id: string) => {
    deleteClientMutation.mutate(id);
  };

  // Date filter helper function
  const getDateRange = (filter: string) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    switch (filter) {
      case 'today':
        return { start: today, end: new Date(today.getTime() + 24 * 60 * 60 * 1000) };
      case 'yesterday':
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
        return { start: yesterday, end: today };
      case 'last7days':
        return { start: new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000), end: new Date(today.getTime() + 24 * 60 * 60 * 1000) };
      case 'last30days':
        return { start: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000), end: new Date(today.getTime() + 24 * 60 * 60 * 1000) };
      case 'thisMonth':
        return { start: new Date(now.getFullYear(), now.getMonth(), 1), end: new Date(now.getFullYear(), now.getMonth() + 1, 1) };
      case 'lastMonth':
        return { start: new Date(now.getFullYear(), now.getMonth() - 1, 1), end: new Date(now.getFullYear(), now.getMonth(), 1) };
      case 'custom':
        if (startDate && endDate) {
          return { start: new Date(startDate), end: new Date(new Date(endDate).getTime() + 24 * 60 * 60 * 1000) };
        }
        return null;
      default:
        return null;
    }
  };

  // Filter clients
  const filteredClients = (clients as CrmClient[]).filter((client: CrmClient) => {
    const matchesSearch = client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.phoneNumber.includes(searchTerm) ||
                         client.company?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !statusFilter || client.status === statusFilter;
    const matchesSalesRep = !salesRepFilter || client.salesRepresentative === salesRepFilter;
    
    // Date filtering
    let matchesDate = true;
    if (dateFilter && dateFilter !== '') {
      const dateRange = getDateRange(dateFilter);
      if (dateRange && client.createdAt) {
        const clientDate = new Date(client.createdAt);
        matchesDate = clientDate >= dateRange.start && clientDate < dateRange.end;
      }
    }
    
    return matchesSearch && matchesStatus && matchesSalesRep && matchesDate;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = statusOptions.find(opt => opt.value === status);
    return (
      <Badge className={`${statusConfig?.color} text-white`}>
        {statusConfig?.label}
      </Badge>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <Users className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Casa das Camisetas CRM
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Sistema Global de Gestão de Clientes
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Activity className={`h-4 w-4 ${connected ? 'text-green-500' : 'text-red-500'}`} />
                <span className={`text-sm ${connected ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                  {connected ? 'Online' : 'Offline'}
                </span>
              </div>
              
              <Button variant="outline" size="sm" onClick={toggleTheme}>
                {theme === 'light' ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
              </Button>
              
              <Button variant="outline" size="sm" onClick={() => refetch()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Atualizar
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white dark:bg-gray-800">
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Clientes</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{(clients as CrmClient[]).length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-white dark:bg-gray-800">
            <CardContent className="p-6">
              <div className="flex items-center">
                <Star className="h-8 w-8 text-green-600 dark:text-green-400" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Clientes Ativos</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {(clients as CrmClient[]).filter((c: CrmClient) => c.status === 'client').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-white dark:bg-gray-800">
            <CardContent className="p-6">
              <div className="flex items-center">
                <Activity className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Prospects</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {(clients as CrmClient[]).filter((c: CrmClient) => c.status === 'prospect').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-white dark:bg-gray-800">
            <CardContent className="p-6">
              <div className="flex items-center">
                <Building className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Leads</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {(clients as CrmClient[]).filter((c: CrmClient) => c.status === 'lead').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Actions */}
        <Card className="mb-6 bg-white dark:bg-gray-800">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="flex flex-col md:flex-row gap-4 flex-1">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Buscar clientes..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Filtrar por status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todos os status</SelectItem>
                    {statusOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Select value={salesRepFilter} onValueChange={setSalesRepFilter}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Vendedor" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todos vendedores</SelectItem>
                    {salesRepresentatives.map(rep => (
                      <SelectItem key={rep} value={rep}>
                        {rep}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Select value={dateFilter} onValueChange={setDateFilter}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Filtrar por data" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todas as datas</SelectItem>
                    <SelectItem value="today">Hoje</SelectItem>
                    <SelectItem value="yesterday">Ontem</SelectItem>
                    <SelectItem value="last7days">Últimos 7 dias</SelectItem>
                    <SelectItem value="last30days">Últimos 30 dias</SelectItem>
                    <SelectItem value="thisMonth">Este mês</SelectItem>
                    <SelectItem value="lastMonth">Mês passado</SelectItem>
                    <SelectItem value="custom">Período personalizado</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {/* Custom date range inputs */}
              {dateFilter === 'custom' && (
                <div className="flex flex-col md:flex-row gap-4 w-full">
                  <div className="flex-1">
                    <Label htmlFor="startDate" className="text-sm text-gray-600 dark:text-gray-400">Data inicial</Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                  <div className="flex-1">
                    <Label htmlFor="endDate" className="text-sm text-gray-600 dark:text-gray-400">Data final</Label>
                    <Input
                      id="endDate"
                      type="date"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                </div>
              )}
              
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button onClick={() => { setEditingClient(null); resetForm(); }}>
                    <Plus className="h-4 w-4 mr-2" />
                    Novo Cliente
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>
                      {editingClient ? 'Editar Cliente' : 'Novo Cliente'}
                    </DialogTitle>
                    <DialogDescription>
                      {editingClient ? 'Atualize as informações do cliente' : 'Adicione um novo cliente ao sistema CRM'}
                    </DialogDescription>
                  </DialogHeader>
                  
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">Nome *</Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                          required
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="phoneNumber">Telefone *</Label>
                        <Input
                          id="phoneNumber"
                          value={formData.phoneNumber}
                          onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                          required
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="email">E-mail</Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="company">Empresa</Label>
                        <Input
                          id="company"
                          value={formData.company}
                          onChange={(e) => setFormData(prev => ({ ...prev, company: e.target.value }))}
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="position">Cargo</Label>
                        <Input
                          id="position"
                          value={formData.position}
                          onChange={(e) => setFormData(prev => ({ ...prev, position: e.target.value }))}
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="status">Status *</Label>
                        <Select value={formData.status} onValueChange={(value: any) => setFormData(prev => ({ ...prev, status: value }))}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {statusOptions.map(option => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <Label htmlFor="salesRepresentative">Vendedor Responsável *</Label>
                        <Select value={formData.salesRepresentative} onValueChange={(value) => setFormData(prev => ({ ...prev, salesRepresentative: value }))}>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o vendedor" />
                          </SelectTrigger>
                          <SelectContent>
                            {salesRepresentatives.map(rep => (
                              <SelectItem key={rep} value={rep}>
                                {rep}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <Label htmlFor="value">Valor Estimado (R$)</Label>
                        <Input
                          id="value"
                          type="number"
                          step="0.01"
                          value={formData.value}
                          onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="source">Fonte</Label>
                        <Input
                          id="source"
                          value={formData.source}
                          onChange={(e) => setFormData(prev => ({ ...prev, source: e.target.value }))}
                          placeholder="WhatsApp, Site, Indicação..."
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="notes">Observações</Label>
                      <Textarea
                        id="notes"
                        value={formData.notes}
                        onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                        rows={3}
                      />
                    </div>
                    
                    <div className="flex justify-end space-x-2 pt-4">
                      <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                        Cancelar
                      </Button>
                      <Button 
                        type="submit" 
                        disabled={createClientMutation.isPending || updateClientMutation.isPending}
                      >
                        {createClientMutation.isPending || updateClientMutation.isPending ? 'Salvando...' : 'Salvar'}
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
            </div>
          </CardContent>
        </Card>

        {/* Clients Table */}
        <Card className="bg-white dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="text-gray-900 dark:text-white">
              Clientes ({filteredClients.length})
            </CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-400">
              Gerencie todos os seus clientes em um só lugar
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
                <span className="ml-2 text-gray-500">Carregando clientes...</span>
              </div>
            ) : filteredClients.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">
                  {searchTerm || statusFilter || salesRepFilter ? 'Nenhum cliente encontrado com os filtros aplicados' : 'Nenhum cliente cadastrado'}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Cliente</TableHead>
                      <TableHead>Contato</TableHead>
                      <TableHead>Empresa</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Vendedor Responsável</TableHead>
                      <TableHead>Valor</TableHead>
                      <TableHead>Criado em</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredClients.map((client: CrmClient) => (
                      <TableRow key={client.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">{client.name}</p>
                            {client.position && (
                              <p className="text-sm text-gray-500 dark:text-gray-400">{client.position}</p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center text-sm">
                              <Phone className="h-3 w-3 mr-1 text-gray-400" />
                              {client.phoneNumber}
                            </div>
                            {client.email && (
                              <div className="flex items-center text-sm">
                                <Mail className="h-3 w-3 mr-1 text-gray-400" />
                                {client.email}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {client.company && (
                            <div className="flex items-center text-sm">
                              <Building className="h-3 w-3 mr-1 text-gray-400" />
                              {client.company}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(client.status)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm">
                            <User className="h-3 w-3 mr-1 text-gray-400" />
                            {client.salesRepresentative}
                          </div>
                        </TableCell>
                        <TableCell>
                          {client.value && (
                            <span className="text-green-600 dark:text-green-400 font-medium">
                              R$ {client.value.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                            </span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center text-sm text-gray-500">
                            <Calendar className="h-3 w-3 mr-1" />
                            {new Date(client.createdAt).toLocaleDateString('pt-BR')}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end space-x-2">
                            <Button variant="ghost" size="sm" onClick={() => handleEdit(client)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <Trash2 className="h-4 w-4 text-red-500" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Tem certeza que deseja excluir o cliente {client.name}? Esta ação não pode ser desfeita.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                  <AlertDialogAction onClick={() => handleDelete(client.id)}>
                                    Excluir
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Casa das Camisetas CRM | Desenvolvido por Kauã - Codestorm 2025
          </p>
        </div>
      </div>
    </div>
  );
}