import { useState, useEffect, useRef, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  X, 
  ZoomIn, 
  ZoomOut, 
  RotateCw, 
  RotateCcw,
  Download, 
  Play, 
  Pause, 
  Volume2, 
  VolumeX,
  SkipBack,
  SkipForward,
  Maximize,
  Minimize,
  FileText,
  Image as ImageIcon,
  Video as VideoIcon,
  Music,
  Eye
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface MediaItem {
  id: string;
  type: 'image' | 'video' | 'audio' | 'document';
  name: string;
  url: string;
  size: number;
  mimetype: string;
  thumbnail?: string;
}

interface MediaViewerProps {
  mediaItems: MediaItem[];
  currentIndex: number;
  isOpen: boolean;
  onClose: () => void;
  onIndexChange: (index: number) => void;
}

export function MediaViewer({ mediaItems, currentIndex, isOpen, onClose, onIndexChange }: MediaViewerProps) {
  const [zoom, setZoom] = useState(100);
  const [rotation, setRotation] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  const { toast } = useToast();
  const mediaRef = useRef<HTMLVideoElement | HTMLAudioElement | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  const currentMedia = mediaItems[currentIndex];

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          if (currentIndex > 0) {
            onIndexChange(currentIndex - 1);
          }
          break;
        case 'ArrowRight':
          if (currentIndex < mediaItems.length - 1) {
            onIndexChange(currentIndex + 1);
          }
          break;
        case ' ':
          e.preventDefault();
          togglePlayPause();
          break;
        case '+':
        case '=':
          handleZoomIn();
          break;
        case '-':
          handleZoomOut();
          break;
        case 'r':
          handleRotate();
          break;
        case 'd':
          handleDownload();
          break;
        case 'f':
          toggleFullscreen();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, currentIndex, mediaItems.length]);

  // Reset states when media changes
  useEffect(() => {
    setZoom(100);
    setRotation(0);
    setIsPlaying(false);
    setCurrentTime(0);
    setDuration(0);
  }, [currentIndex]);

  // Handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 500));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 25));
  };

  const handleRotate = () => {
    setRotation(prev => (prev + 90) % 360);
  };

  const handleRotateCounter = () => {
    setRotation(prev => (prev - 90 + 360) % 360);
  };

  const togglePlayPause = () => {
    if (mediaRef.current) {
      if (isPlaying) {
        mediaRef.current.pause();
      } else {
        mediaRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (mediaRef.current) {
      mediaRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume);
    if (mediaRef.current) {
      mediaRef.current.volume = newVolume;
    }
  };

  const handleSeek = (time: number) => {
    if (mediaRef.current) {
      mediaRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  const handleDownload = async () => {
    if (!currentMedia) return;

    try {
      const response = await fetch(currentMedia.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = currentMedia.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Download Iniciado",
        description: `Baixando ${currentMedia.name}`,
        variant: "default"
      });
    } catch (error) {
      toast({
        title: "Erro no Download",
        description: "Não foi possível baixar o arquivo",
        variant: "destructive"
      });
    }
  };

  const toggleFullscreen = async () => {
    if (!containerRef.current) return;

    try {
      if (isFullscreen) {
        await document.exitFullscreen();
      } else {
        await containerRef.current.requestFullscreen();
      }
    } catch (error) {
      console.error('Fullscreen error:', error);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getMediaIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <ImageIcon className="h-4 w-4" />;
      case 'video':
        return <VideoIcon className="h-4 w-4" />;
      case 'audio':
        return <Music className="h-4 w-4" />;
      case 'document':
        return <FileText className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  if (!isOpen || !currentMedia) return null;

  return (
    <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center">
      <div 
        ref={containerRef}
        className="relative w-full h-full flex flex-col"
      >
        {/* Header */}
        <div className="absolute top-0 left-0 right-0 z-10 bg-black/50 p-4">
          <div className="flex items-center justify-between text-white">
            <div className="flex items-center gap-3">
              {getMediaIcon(currentMedia.type)}
              <div>
                <h3 className="font-medium">{currentMedia.name}</h3>
                <p className="text-sm text-gray-300">
                  {formatFileSize(currentMedia.size)} • {currentIndex + 1} de {mediaItems.length}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {currentMedia.type.toUpperCase()}
              </Badge>
              <Button
                onClick={onClose}
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Media Content */}
        <div className="flex-1 flex items-center justify-center p-4 pt-20 pb-20">
          {currentMedia.type === 'image' && (
            <img
              ref={imageRef}
              src={currentMedia.url}
              alt={currentMedia.name}
              className="max-w-full max-h-full object-contain transition-transform duration-200"
              style={{
                transform: `scale(${zoom / 100}) rotate(${rotation}deg)`
              }}
              onError={() => {
                toast({
                  title: "Erro ao Carregar",
                  description: "Não foi possível carregar a imagem",
                  variant: "destructive"
                });
              }}
            />
          )}

          {currentMedia.type === 'video' && (
            <video
              ref={mediaRef as React.RefObject<HTMLVideoElement>}
              src={currentMedia.url}
              className="max-w-full max-h-full"
              controls
              onLoadedMetadata={(e) => {
                const target = e.target as HTMLVideoElement;
                setDuration(target.duration);
              }}
              onTimeUpdate={(e) => {
                const target = e.target as HTMLVideoElement;
                setCurrentTime(target.currentTime);
              }}
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
              onError={() => {
                toast({
                  title: "Erro ao Carregar",
                  description: "Não foi possível carregar o vídeo",
                  variant: "destructive"
                });
              }}
            />
          )}

          {currentMedia.type === 'audio' && (
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 w-full max-w-md">
              <div className="text-center mb-6">
                <Music className="h-16 w-16 mx-auto text-white mb-4" />
                <h3 className="text-white font-medium">{currentMedia.name}</h3>
              </div>
              
              <audio
                ref={mediaRef as React.RefObject<HTMLAudioElement>}
                src={currentMedia.url}
                onLoadedMetadata={(e) => {
                  const target = e.target as HTMLAudioElement;
                  setDuration(target.duration);
                }}
                onTimeUpdate={(e) => {
                  const target = e.target as HTMLAudioElement;
                  setCurrentTime(target.currentTime);
                }}
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                onError={() => {
                  toast({
                    title: "Erro ao Carregar",
                    description: "Não foi possível carregar o áudio",
                    variant: "destructive"
                  });
                }}
              />
              
              <div className="space-y-4">
                {/* Progress Bar */}
                <div className="w-full bg-gray-600 rounded-full h-2">
                  <div 
                    className="bg-white rounded-full h-2 transition-all duration-100"
                    style={{ width: `${(currentTime / duration) * 100}%` }}
                  />
                </div>
                
                {/* Time Display */}
                <div className="flex justify-between text-sm text-gray-300">
                  <span>{formatTime(currentTime)}</span>
                  <span>{formatTime(duration)}</span>
                </div>
                
                {/* Audio Controls */}
                <div className="flex items-center justify-center gap-4">
                  <Button
                    onClick={() => handleSeek(Math.max(0, currentTime - 10))}
                    variant="ghost"
                    size="sm"
                    className="text-white hover:bg-white/20"
                  >
                    <SkipBack className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    onClick={togglePlayPause}
                    variant="ghost"
                    size="lg"
                    className="text-white hover:bg-white/20"
                  >
                    {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
                  </Button>
                  
                  <Button
                    onClick={() => handleSeek(Math.min(duration, currentTime + 10))}
                    variant="ghost"
                    size="sm"
                    className="text-white hover:bg-white/20"
                  >
                    <SkipForward className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    onClick={toggleMute}
                    variant="ghost"
                    size="sm"
                    className="text-white hover:bg-white/20"
                  >
                    {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
            </div>
          )}

          {currentMedia.type === 'document' && (
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 w-full max-w-md text-center">
              <FileText className="h-16 w-16 mx-auto text-white mb-4" />
              <h3 className="text-white font-medium mb-2">{currentMedia.name}</h3>
              <p className="text-gray-300 text-sm mb-6">
                {formatFileSize(currentMedia.size)}
              </p>
              <div className="space-y-3">
                <Button
                  onClick={handleDownload}
                  className="w-full"
                  variant="secondary"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Baixar Documento
                </Button>
                
                {currentMedia.mimetype === 'application/pdf' && (
                  <Button
                    onClick={() => window.open(currentMedia.url, '_blank')}
                    className="w-full"
                    variant="outline"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Visualizar PDF
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Bottom Controls */}
        <div className="absolute bottom-0 left-0 right-0 bg-black/50 p-4">
          <div className="flex items-center justify-between">
            {/* Navigation */}
            <div className="flex items-center gap-2">
              <Button
                onClick={() => onIndexChange(currentIndex - 1)}
                disabled={currentIndex === 0}
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20"
              >
                ← Anterior
              </Button>
              
              <Button
                onClick={() => onIndexChange(currentIndex + 1)}
                disabled={currentIndex === mediaItems.length - 1}
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20"
              >
                Próximo →
              </Button>
            </div>

            {/* Image/Video Controls */}
            {(currentMedia.type === 'image' || currentMedia.type === 'video') && (
              <div className="flex items-center gap-2">
                <Button
                  onClick={handleZoomOut}
                  variant="ghost"
                  size="sm"
                  className="text-white hover:bg-white/20"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
                
                <span className="text-white text-sm px-2">
                  {zoom}%
                </span>
                
                <Button
                  onClick={handleZoomIn}
                  variant="ghost"
                  size="sm"
                  className="text-white hover:bg-white/20"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
                
                {currentMedia.type === 'image' && (
                  <>
                    <Button
                      onClick={handleRotateCounter}
                      variant="ghost"
                      size="sm"
                      className="text-white hover:bg-white/20"
                    >
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      onClick={handleRotate}
                      variant="ghost"
                      size="sm"
                      className="text-white hover:bg-white/20"
                    >
                      <RotateCw className="h-4 w-4" />
                    </Button>
                  </>
                )}
              </div>
            )}

            {/* Universal Controls */}
            <div className="flex items-center gap-2">
              <Button
                onClick={toggleFullscreen}
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20"
              >
                {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
              </Button>
              
              <Button
                onClick={handleDownload}
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20"
              >
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Keyboard Shortcuts Help */}
        <div className="absolute top-20 right-4 bg-black/70 text-white p-3 rounded-lg text-xs max-w-xs">
          <h4 className="font-medium mb-2">Atalhos do Teclado</h4>
          <div className="space-y-1">
            <div>ESC - Fechar</div>
            <div>← → - Navegar</div>
            <div>Espaço - Play/Pause</div>
            <div>+ - - Zoom</div>
            <div>R - Rotacionar</div>
            <div>D - Download</div>
            <div>F - Fullscreen</div>
          </div>
        </div>
      </div>
    </div>
  );
}