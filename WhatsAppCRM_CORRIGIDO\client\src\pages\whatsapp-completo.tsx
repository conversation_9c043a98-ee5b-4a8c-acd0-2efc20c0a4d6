import { useState, useRef, useEffect } from "react";
import { 
  ArrowLeft, MessageSquare, Users, Smartphone, RefreshCw, Send, Search, 
  Phone, Paperclip, Smile, Check, Check<PERSON>heck, Clock, Moon, Sun,
  Activity, Settings, Menu, Bell, Circle, Plus, Edit, Trash2,
  <PERSON>c, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>
} from "lucide-react";
import { useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import { useWebSocket } from "@/hooks/useWebSocket";
import { useThemeStore } from "@/lib/theme-store";
import { toast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useAudioRecorder } from "@/hooks/useAudioRecorder";


interface WhatsAppMessage {
  id: string;
  body: string;
  fromMe: boolean;
  timestamp: number;
  type: string;
  hasMedia: boolean;
  status?: 'sending' | 'sent' | 'delivered' | 'read';
  author?: string;
  isForwarded?: boolean;
}

interface WhatsAppChat {
  id: string;
  name: string;
  lastMessage?: {
    body: string;
    timestamp: number;
    fromMe: boolean;
  };
  unreadCount: number;
  isGroup: boolean;
  profilePic?: string;
}

export default function WhatsAppCompleto() {
  const [, setLocation] = useLocation();
  const [selectedChat, setSelectedChat] = useState<string>("");
  const [messageInput, setMessageInput] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [searchInChat, setSearchInChat] = useState("");
  const [showChatSearch, setShowChatSearch] = useState(false);
  const audioRecorder = useAudioRecorder();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const queryClient = useQueryClient();
  const { theme, toggleTheme } = useThemeStore();

  // Generate consistent userId from localStorage
  const getCurrentUserId = () => {
    let userId = localStorage.getItem('whatsapp_user_id');
    if (!userId) {
      userId = `user_${Date.now()}`;
      localStorage.setItem('whatsapp_user_id', userId);
    }
    return userId;
  };
  
  const currentUserId = getCurrentUserId();
  
  // WebSocket connection for real-time updates
  const { connected: wsConnected, emit } = useWebSocket({
    userId: currentUserId,
    autoConnect: true
  });

  // WhatsApp connection status with proper error handling
  const { data: whatsappStatus, refetch: refetchStatus } = useQuery({
    queryKey: ['/api/whatsapp/status', currentUserId],
    queryFn: async () => {
      try {
        const response = await fetch(`/api/whatsapp/status?userId=${currentUserId}`);
        if (!response.ok) throw new Error('Status fetch failed');
        const data = await response.json();
        
        // Handle multi-session response format
        if (data.sessions && Array.isArray(data.sessions)) {
          const userSession = data.sessions.find(s => s.userId === currentUserId);
          return userSession ? {
            connected: Boolean(userSession.isReady),
            phoneNumber: userSession.phoneNumber || null,
            status: userSession.isReady ? 'connected' : 'connecting'
          } : {
            connected: false,
            phoneNumber: null,
            status: 'disconnected'
          };
        }
        
        // Handle direct status response
        return {
          connected: Boolean(data.connected),
          phoneNumber: data.phoneNumber || null,
          status: data.status || 'disconnected'
        };
      } catch (error) {
        return {
          connected: false,
          phoneNumber: null,
          status: 'error'
        };
      }
    },
    refetchInterval: 5000
  });

  // WhatsApp chats
  const { data: chatsResponse, refetch: refetchChats } = useQuery({
    queryKey: ['/api/whatsapp/chats', currentUserId],
    queryFn: async () => {
      const response = await fetch(`/api/whatsapp/chats?userId=${currentUserId}`);
      return response.json();
    },
    enabled: whatsappStatus?.connected,
    refetchInterval: 10000,
  });

  // Safe extraction of chats array from response
  const chats = Array.isArray(chatsResponse?.chats) ? chatsResponse.chats : 
                Array.isArray(chatsResponse) ? chatsResponse : [];

  // Messages for selected chat with proper pagination structure
  const { data: messagesData, refetch: refetchMessages, isLoading: messagesLoading } = useQuery({
    queryKey: ['/api/whatsapp/messages', selectedChat],
    queryFn: async () => {
      if (!selectedChat) return { data: [], total: 0, offset: 0 };
      setLoadingMessages(true);
      try {
        const response = await fetch(`/api/whatsapp/messages?chatId=${selectedChat}&userId=${currentUserId}`);
        const result = await response.json();
        setLoadingMessages(false);
        
        // Ensure we return the expected paginated structure with defensive validation
        if (Array.isArray(result)) {
          return { data: result, total: result.length, offset: 0 };
        }
        
        // Handle different response formats safely
        if (result && typeof result === 'object') {
          if (Array.isArray(result.messages)) {
            return { data: result.messages, total: result.totalMessages || result.messages.length, offset: 0 };
          }
          if (Array.isArray(result.data)) {
            return { data: result.data, total: result.total || result.data.length, offset: 0 };
          }
        }
        
        // Fallback to empty array structure
        return { data: [], total: 0, offset: 0 };
      } catch (error) {
        setLoadingMessages(false);
        throw error;
      }
    },
    enabled: !!selectedChat && whatsappStatus?.connected,
    refetchInterval: 3000,
  });

  // Extract messages array from paginated structure with proper fallback
  const messages = Array.isArray(messagesData?.data) ? messagesData.data : 
                   Array.isArray(messagesData) ? messagesData : [];

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: async (messageData: { chatId: string; message: string; userId: string }) => {
      setSendingMessage(true);
      const response = await fetch('/api/whatsapp/send', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(messageData)
      });
      
      if (!response.ok) {
        throw new Error('Falha ao enviar mensagem');
      }
      
      return response.json();
    },
    onSuccess: () => {
      setSendingMessage(false);
      setMessageInput("");
      queryClient.invalidateQueries({ queryKey: ['/api/whatsapp/messages'] });
      queryClient.invalidateQueries({ queryKey: ['/api/whatsapp/chats'] });
      emit('message:sent', { chatId: selectedChat, userId: currentUserId });
      toast({
        title: "Mensagem enviada",
        description: "Sua mensagem foi enviada com sucesso",
      });
    },
    onError: (error: any) => {
      setSendingMessage(false);
      toast({
        title: "Erro ao enviar mensagem",
        description: error.message || "Tente novamente",
        variant: "destructive"
      });
    }
  });

  // Real-time WebSocket events
  useEffect(() => {
    const handleMessageReceived = () => {
      queryClient.invalidateQueries({ queryKey: ['/api/whatsapp/messages'] });
      queryClient.invalidateQueries({ queryKey: ['/api/whatsapp/chats'] });
    };

    const handleMessageSent = () => {
      queryClient.invalidateQueries({ queryKey: ['/api/whatsapp/messages'] });
      queryClient.invalidateQueries({ queryKey: ['/api/whatsapp/chats'] });
    };

    const handleWhatsAppConnected = () => {
      refetchStatus();
      refetchChats();
      toast({
        title: "WhatsApp conectado",
        description: "Sua conta WhatsApp foi conectada com sucesso",
      });
    };

    const handleWhatsAppDisconnected = () => {
      refetchStatus();
      toast({
        title: "WhatsApp desconectado",
        description: "Sua conta WhatsApp foi desconectada",
        variant: "destructive"
      });
    };

    window.addEventListener('websocket:message:received', handleMessageReceived);
    window.addEventListener('websocket:message:sent', handleMessageSent);
    window.addEventListener('websocket:whatsapp:connected', handleWhatsAppConnected);
    window.addEventListener('websocket:whatsapp:disconnected', handleWhatsAppDisconnected);

    return () => {
      window.removeEventListener('websocket:message:received', handleMessageReceived);
      window.removeEventListener('websocket:message:sent', handleMessageSent);
      window.removeEventListener('websocket:whatsapp:connected', handleWhatsAppConnected);
      window.removeEventListener('websocket:whatsapp:disconnected', handleWhatsAppDisconnected);
    };
  }, [queryClient, refetchStatus, refetchChats]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Handle send message
  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!messageInput.trim() || !selectedChat || sendingMessage) return;

    setSendingMessage(true);
    sendMessageMutation.mutate({
      chatId: selectedChat,
      message: messageInput.trim(),
      userId: currentUserId
    }, {
      onSuccess: () => {
        setMessageInput("");
        setSendingMessage(false);
        inputRef.current?.focus();
      },
      onError: () => {
        setSendingMessage(false);
      }
    });
  };

  // Audio recording handlers
  const handleAudioRecord = async () => {
    if (audioRecorder.isRecording) {
      audioRecorder.stopRecording();
    } else {
      await audioRecorder.startRecording();
    }
  };

  const handleSendAudio = async () => {
    if (!audioRecorder.audioBlob || !selectedChat) return;

    try {
      const formData = new FormData();
      formData.append('audio', audioRecorder.audioBlob, 'audio.webm');
      formData.append('chatId', selectedChat);
      formData.append('userId', 'user_1749665821961');

      setSendingMessage(true);
      
      const response = await fetch('/api/whatsapp/send-audio', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('Failed to send audio');
      }

      audioRecorder.clearAudio();
      toast({
        title: "Áudio enviado",
        description: "Mensagem de áudio enviada com sucesso"
      });
    } catch (error) {
      toast({
        title: "Erro ao enviar áudio",
        description: "Não foi possível enviar a mensagem de áudio",
        variant: "destructive"
      });
    } finally {
      setSendingMessage(false);
    }
  };

  // Apply search filter directly to messages array
  const displayMessages = searchInChat 
    ? messages.filter((message: WhatsAppMessage) => 
        message.body?.toLowerCase().includes(searchInChat.toLowerCase())
      )
    : messages;

  // Format time
  const formatTime = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format last message time
  const formatLastMessageTime = (timestamp: number) => {
    const now = new Date();
    const messageDate = new Date(timestamp * 1000);
    const diffDays = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return messageDate.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return 'Ontem';
    } else if (diffDays < 7) {
      return messageDate.toLocaleDateString('pt-BR', { weekday: 'short' });
    } else {
      return messageDate.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
    }
  };

  // Get message status icon
  const getMessageStatusIcon = (message: WhatsAppMessage) => {
    if (!message.fromMe) return null;
    
    switch (message.status) {
      case 'sending':
        return <Clock className="w-3 h-3 text-gray-400" />;
      case 'sent':
        return <Check className="w-3 h-3 text-gray-400" />;
      case 'delivered':
        return <CheckCheck className="w-3 h-3 text-gray-400" />;
      case 'read':
        return <CheckCheck className="w-3 h-3 text-blue-500" />;
      default:
        return <Check className="w-3 h-3 text-gray-400" />;
    }
  };

  // Filter chats based on search
  const filteredChats = chats.filter((chat: WhatsAppChat) =>
    chat.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const selectedChatData = chats.find((chat: WhatsAppChat) => chat.id === selectedChat);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <MessageSquare className="h-8 w-8 text-green-600 dark:text-green-400" />
              <div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  Casa das Camisetas WhatsApp
                </h1>
                <div className="flex items-center space-x-2">
                  <Circle className={`h-2 w-2 ${whatsappStatus?.connected ? 'fill-green-500 text-green-500' : 'fill-red-500 text-red-500'}`} />
                  <span className={`text-xs ${whatsappStatus?.connected ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                    {whatsappStatus?.connected ? 'Conectado' : 'Desconectado'}
                  </span>
                  {whatsappStatus?.phoneNumber && (
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {whatsappStatus.phoneNumber}
                    </span>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Activity className={`h-4 w-4 ${whatsappStatus?.connected ? 'text-green-500' : 'text-red-500'}`} />
                <span className={`text-sm ${whatsappStatus?.connected ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                  {whatsappStatus?.connected ? 'Conectado' : 'Desconectado'}
                </span>
              </div>
              
              <Button variant="outline" size="sm" onClick={toggleTheme}>
                {theme === 'light' ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
              </Button>
              
              <Button variant="outline" size="sm" onClick={() => refetchStatus()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Atualizar
              </Button>
              
              <Button variant="outline" size="sm" onClick={() => setLocation('/')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Menu Principal
              </Button>
              
              <Button 
                variant="destructive" 
                size="sm" 
                onClick={async () => {
                  try {
                    // Disconnect WhatsApp session on server
                    const userId = localStorage.getItem('whatsapp_user_id');
                    if (userId) {
                      await fetch('/api/whatsapp/disconnect', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ userId })
                      });
                    }
                  } catch (error) {
                    console.log('Error disconnecting:', error);
                  }
                  
                  // Clear all localStorage
                  localStorage.removeItem('whatsapp_user_id');
                  localStorage.removeItem('whatsapp_connected');
                  localStorage.removeItem('whatsapp_phone');
                  
                  // Reload to show login screen
                  window.location.reload();
                }}
              >
                Deslogar
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto flex h-[calc(100vh-120px)]">
        {/* Chat List Sidebar */}
        <div className="w-1/3 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
          {/* Search */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Buscar conversas..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          {/* Chats List */}
          <ScrollArea className="flex-1">
            <div className="p-2">
              {!whatsappStatus?.connected ? (
                <div className="text-center py-8">
                  <Smartphone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400 mb-4">
                    WhatsApp desconectado
                  </p>
                  <Button 
                    onClick={() => {
                      // Clear all sessions and redirect to login
                      localStorage.removeItem('whatsapp_user_id');
                      localStorage.removeItem('whatsapp_connected');
                      localStorage.removeItem('whatsapp_phone');
                      window.location.href = '/';
                    }} 
                    variant="outline"
                  >
                    Fazer Login Novamente
                  </Button>
                </div>
              ) : filteredChats.length === 0 ? (
                <div className="text-center py-8">
                  <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">
                    {searchQuery ? 'Nenhuma conversa encontrada' : 'Nenhuma conversa disponível'}
                  </p>
                </div>
              ) : (
                <div className="space-y-1">
                  {filteredChats.map((chat: WhatsAppChat) => (
                    <Card
                      key={chat.id}
                      className={`cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-700 ${
                        selectedChat === chat.id ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700' : ''
                      }`}
                      onClick={() => setSelectedChat(chat.id)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-12 w-12">
                            <AvatarImage src={chat.profilePic} />
                            <AvatarFallback className="bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400">
                              {chat.name.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                {chat.name}
                              </h3>
                              {chat.lastMessage && (
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                  {formatLastMessageTime(chat.lastMessage.timestamp)}
                                </span>
                              )}
                            </div>
                            
                            <div className="flex items-center justify-between mt-1">
                              <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                                {chat.lastMessage ? (
                                  <>
                                    {chat.lastMessage.fromMe && (
                                      <span className="mr-1">Você:</span>
                                    )}
                                    {chat.lastMessage.body}
                                  </>
                                ) : (
                                  'Nenhuma mensagem'
                                )}
                              </p>
                              
                              {chat.unreadCount > 0 && (
                                <Badge className="bg-green-500 text-white text-xs">
                                  {chat.unreadCount}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </ScrollArea>
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col bg-gray-100 dark:bg-gray-900">
          {selectedChat ? (
            <>
              {/* Chat Header */}
              <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={selectedChatData?.profilePic} />
                      <AvatarFallback className="bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400">
                        {selectedChatData?.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                        {selectedChatData?.name}
                      </h2>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {selectedChatData?.isGroup ? 'Grupo' : 'Contato'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm">
                      <Phone className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => setShowChatSearch(!showChatSearch)}
                    >
                      <Search className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                {/* Chat Search Bar */}
                {showChatSearch && (
                  <div className="p-3 border-b border-gray-200 dark:border-gray-700">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        type="text"
                        placeholder="Buscar mensagens..."
                        value={searchInChat}
                        onChange={(e) => setSearchInChat(e.target.value)}
                        className="pl-10 pr-10"
                      />
                      {searchInChat && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                          onClick={() => setSearchInChat("")}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Messages Area */}
              <ScrollArea className="flex-1 p-4" ref={messagesContainerRef}>
                <div className="space-y-4">
                  {loadingMessages && (
                    <div className="flex justify-center py-4">
                      <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span className="text-sm">Carregando mensagens...</span>
                      </div>
                    </div>
                  )}
                  
                  {displayMessages.length === 0 && !loadingMessages ? (
                    <div className="text-center py-8">
                      <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500 dark:text-gray-400">
                        {searchInChat ? 'Nenhuma mensagem encontrada' : 'Nenhuma mensagem nesta conversa'}
                      </p>
                    </div>
                  ) : (
                    displayMessages.map((message: WhatsAppMessage) => (
                      <div
                        key={message.id}
                        className={`flex ${message.fromMe ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg relative ${
                            message.fromMe
                              ? 'bg-green-500 text-white'
                              : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                          }`}
                        >
                          {/* Check if message is deleted */}
                          {message.body === "Esta mensagem foi apagada" || message.body.includes("deleted") ? (
                            <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400 italic">
                              <Ban className="h-3 w-3" />
                              <p className="text-sm">Esta mensagem foi apagada</p>
                            </div>
                          ) : (
                            <p className="text-sm">{message.body}</p>
                          )}
                          
                          {/* Forwarded indicator */}
                          {message.isForwarded && (
                            <div className="flex items-center space-x-1 mb-1 text-xs opacity-70">
                              <ArrowLeft className="h-3 w-3 rotate-180" />
                              <span>Encaminhada</span>
                            </div>
                          )}
                          
                          <div className={`flex items-center justify-end mt-1 space-x-1 ${
                            message.fromMe ? 'text-green-100' : 'text-gray-500 dark:text-gray-400'
                          }`}>
                            <span className="text-xs">
                              {formatTime(message.timestamp)}
                            </span>
                            {getMessageStatusIcon(message)}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>

              {/* Message Input */}
              <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
                {/* Audio Recording Indicator */}
                {audioRecorder.isRecording && (
                  <div className="mb-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                        <span className="text-sm font-medium text-red-600 dark:text-red-400">
                          Gravando... {audioRecorder.formatDuration(audioRecorder.duration)}
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={audioRecorder.cancelRecording}
                        className="text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}

                {/* Audio Preview */}
                {audioRecorder.audioBlob && !audioRecorder.isRecording && (
                  <div className="mb-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Mic className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                          Áudio gravado ({audioRecorder.formatDuration(audioRecorder.duration)})
                        </span>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handleSendAudio}
                          disabled={sendingMessage}
                          className="text-blue-600 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/30"
                        >
                          <Send className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={audioRecorder.clearAudio}
                          className="text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-900/30"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                <form onSubmit={handleSendMessage} className="flex items-center space-x-2">
                  <Button type="button" variant="ghost" size="sm">
                    <Paperclip className="h-4 w-4" />
                  </Button>
                  
                  <div className="flex-1">
                    <Input
                      ref={inputRef}
                      placeholder={audioRecorder.isRecording ? "Gravando áudio..." : "Digite uma mensagem..."}
                      value={messageInput}
                      onChange={(e) => setMessageInput(e.target.value)}
                      disabled={sendingMessage || !whatsappStatus?.connected || audioRecorder.isRecording}
                      className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                    />
                  </div>
                  
                  <Button type="button" variant="ghost" size="sm">
                    <Smile className="h-4 w-4" />
                  </Button>
                  
                  {/* Audio Recording Button */}
                  <Button 
                    type="button"
                    onClick={handleAudioRecord}
                    variant="ghost" 
                    size="sm"
                    className={`${audioRecorder.isRecording ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400' : ''}`}
                  >
                    {audioRecorder.isRecording ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                  </Button>
                  
                  <Button 
                    type="submit" 
                    disabled={!messageInput.trim() || sendingMessage || !whatsappStatus?.connected || audioRecorder.isRecording}
                    className={`bg-green-500 hover:bg-green-600 text-white transition-all duration-200 ${
                      sendingMessage ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    {sendingMessage ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>
                </form>
              </div>
            </>
          ) : (
            /* Welcome Screen */
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <MessageSquare className="h-24 w-24 text-gray-400 mx-auto mb-6" />
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  Casa das Camisetas WhatsApp
                </h2>
                <p className="text-gray-500 dark:text-gray-400 mb-6">
                  Selecione uma conversa para começar a enviar mensagens
                </p>
                {!whatsappStatus?.connected && (
                  <Button onClick={() => setLocation('/qr-code')}>
                    Conectar WhatsApp
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-3">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <p className="text-center text-sm text-gray-500 dark:text-gray-400">
            Casa das Camisetas CRM | Desenvolvido por Kauã - Codestorm 2025
          </p>
        </div>
      </div>
    </div>
  );
}