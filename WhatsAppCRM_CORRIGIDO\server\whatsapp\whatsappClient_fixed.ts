import { makeWASocket, DisconnectReason, ConnectionState, useMultiFileAuthState } from '@whiskeysockets/baileys';
import { Boom } from '@hapi/boom';
import path from 'path';
import fs from 'fs';
import QRCode from 'qrcode';
import { webSocketService } from '../services/websocket';
import { storage } from '../storage';

interface WhatsAppClient {
  sock?: any;
  qr?: string;
  isConnected: boolean;
  userId: number;
  phoneNumber?: string;
}

class WhatsAppClientService {
  private clients: Map<number, WhatsAppClient> = new Map();
  private sessionsDir = process.env.SESSIONS_DIR || path.join(process.cwd(), 'sessions');

  constructor() {
    // Garantir que o diretório de sessões existe
    if (!fs.existsSync(this.sessionsDir)) {
      fs.mkdirSync(this.sessionsDir, { recursive: true });
    }
    
    // Limpar processos Chromium/Puppeteer travados na inicialização
    this.cleanupChromiumProcesses();
  }

  private cleanupChromiumProcesses() {
    try {
      console.log('🧹 Limpando processos Chromium/Puppeteer travados...');
      
      // Matar processos Chromium/Chrome travados
      const { execSync } = require('child_process');
      
      try {
        execSync('pkill -9 -f chromium', { stdio: 'ignore' });
      } catch (e) {
        // Ignorar erro se não houver processos para matar
      }
      
      try {
        execSync('pkill -9 -f chrome', { stdio: 'ignore' });
      } catch (e) {
        // Ignorar erro se não houver processos para matar
      }
      
      // Limpar arquivos de bloqueio órfãos
      const lockFiles = [
        path.join(this.sessionsDir, '**/SingletonLock'),
        path.join(this.sessionsDir, '**/Default/SingletonLock'),
        '/tmp/.org.chromium.Chromium.*'
      ];
      
      lockFiles.forEach(pattern => {
        try {
          execSync(`rm -rf ${pattern}`, { stdio: 'ignore' });
        } catch (e) {
          // Ignorar erro se arquivos não existirem
        }
      });
      
      console.log('✅ Limpeza de processos Chromium concluída');
    } catch (error) {
      console.warn('⚠️ Aviso: Não foi possível limpar todos os processos Chromium:', error);
    }
  }

  async initClient(userId: number): Promise<{ success: boolean; qr?: string; error?: string }> {
    try {
      console.log(`🔐 INICIANDO CONEXÃO WHATSAPP REAL PARA USUÁRIO ${userId}`);
      console.log(`🔐 INICIANDO AUTENTICAÇÃO WHATSAPP REAL PARA USUÁRIO ${userId}`);
      console.log(`🚫 NENHUMA SIMULAÇÃO SERÁ USADA - APENAS DADOS REAIS`);
      
      // Limpar processos antes de iniciar nova sessão
      this.cleanupChromiumProcesses();
      
      const sessionPath = path.join(this.sessionsDir, `session_${userId}`);
      console.log(`📁 Diretório de sessão: ${sessionPath}`);
      
      // Garantir que o diretório da sessão existe
      if (!fs.existsSync(sessionPath)) {
        fs.mkdirSync(sessionPath, { recursive: true });
      }

      // Limpar arquivos de bloqueio específicos da sessão
      const sessionLockFiles = [
        path.join(sessionPath, 'SingletonLock'),
        path.join(sessionPath, 'Default', 'SingletonLock'),
        path.join(sessionPath, '.org.chromium.Chromium.*')
      ];
      
      sessionLockFiles.forEach(lockFile => {
        try {
          if (fs.existsSync(lockFile)) {
            fs.unlinkSync(lockFile);
            console.log(`🗑️ Removido arquivo de bloqueio: ${lockFile}`);
          }
        } catch (e) {
          console.warn(`⚠️ Não foi possível remover ${lockFile}:`, e);
        }
      });

      const { state, saveCreds } = await useMultiFileAuthState(sessionPath);

      const client: WhatsAppClient = {
        isConnected: false,
        userId,
      };

      // Configurações otimizadas do Baileys para evitar problemas com Chromium
      const sock = makeWASocket({
        auth: state,
        printQRInTerminal: false,
        logger: {
          level: 'silent',
          child: () => ({ level: 'silent' } as any),
        } as any,
        browser: ['Casa das Camisetas CRM', 'Chrome', '1.0.0'],
        generateHighQualityLinkPreview: true,
        markOnlineOnConnect: true,
        // Configurações específicas para evitar problemas de lançamento do navegador
        options: {
          // Argumentos do Chromium para evitar problemas de sandbox e recursos
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
          ],
          // Diretório de dados do usuário específico para esta sessão
          userDataDir: path.join(sessionPath, 'chromium_data'),
          // Timeout aumentado para dar tempo ao Chromium inicializar
          timeout: 60000,
          // Desabilitar imagens para economizar recursos
          ignoreDefaultArgs: ['--disable-extensions'],
        }
      });

      client.sock = sock;
      this.clients.set(userId, client);

      // Handlers de conexão
      sock.ev.on('connection.update', async (update: Partial<ConnectionState>) => {
        await this.handleConnectionUpdate(update, client);
      });

      sock.ev.on('creds.update', saveCreds);

      // Handler de mensagens recebidas
      sock.ev.on('messages.upsert', async (messageUpdate) => {
        await this.handleIncomingMessages(userId, messageUpdate);
      });

      // Aguardar QR Code ou conexão com timeout aumentado
      await this.waitForConnection(client, 120000); // 2 minutos

      return { 
        success: true, 
        qr: client.qr
      };

    } catch (error) {
      console.error(`❌ ERRO AO INICIALIZAR CLIENTE WHATSAPP REAL:`, error);
      
      // Tentar limpeza adicional em caso de erro
      this.cleanupChromiumProcesses();
      
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Erro desconhecido' 
      };
    }
  }

  private async handleConnectionUpdate(
    update: Partial<ConnectionState>, 
    client: WhatsAppClient
  ) {
    const { connection, lastDisconnect, qr } = update;

    if (qr) {
      console.log('🔐 QR CODE GERADO PARA AUTENTICAÇÃO REAL DO WHATSAPP');
      console.log('📱 ESCANEIE COM SUA CONTA REAL DO WHATSAPP:');
      console.log('⚠️  Este QR Code conecta sua conta real ao sistema. Nada será simulado.');
      console.log('📋 Por favor, escaneie com o aplicativo do WhatsApp...');
      
      // Gerar QR Code como string para o terminal
      try {
        const qrString = await QRCode.toString(qr, { type: 'terminal' });
        console.log(qrString);
      } catch (qrError) {
        console.warn('⚠️ Não foi possível exibir QR no terminal:', qrError);
      }
      
      // Também gerar como data URL para o frontend
      try {
        const qrDataURL = await QRCode.toDataURL(qr);
        client.qr = qrDataURL;
        
        // Enviar QR Code via WebSocket
        webSocketService.sendToUser(client.userId, 'qr_code', { qr: qrDataURL });
        
        console.log('✅ QR Code enviado para o frontend via WebSocket');
      } catch (qrError) {
        console.error('❌ Erro ao gerar QR Code para frontend:', qrError);
        // Mesmo com erro no QR para frontend, continuar com a string básica
        client.qr = 'QR Code disponível no console do servidor';
      }
    }

    if (connection === 'close') {
      const shouldReconnect = (lastDisconnect?.error as Boom)?.output?.statusCode !== DisconnectReason.loggedOut;
      
      if (shouldReconnect) {
        console.log('🔄 Reconectando WhatsApp...');
        // Limpar processos antes de reconectar
        this.cleanupChromiumProcesses();
        setTimeout(() => {
          this.initClient(client.userId);
        }, 5000); // Delay aumentado para dar tempo à limpeza
      } else {
        console.log('❌ WhatsApp desconectado - requer nova autenticação');
        client.isConnected = false;
        await storage.updateSessionStatus(client.userId, false);
      }
    } else if (connection === 'open') {
      console.log(`✅ WhatsApp conectado com sucesso para usuário ${client.userId}`);
      client.isConnected = true;
      client.phoneNumber = client.sock.user?.id?.replace(/:\d+/, '');
      
      // Salvar status da sessão no banco
      await storage.updateSessionStatus(client.userId, true, client.phoneNumber);
      
      // Notificar via WebSocket
      webSocketService.sendToUser(client.userId, 'whatsapp_connected', {
        phoneNumber: client.phoneNumber,
        isConnected: true
      });
      
      console.log(`📱 Telefone conectado: ${client.phoneNumber}`);
    }
  }

  private async waitForConnection(client: WhatsAppClient, timeout: number = 60000): Promise<void> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const checkConnection = () => {
        if (client.isConnected || client.qr) {
          resolve();
        } else if (Date.now() - startTime > timeout) {
          reject(new Error(`Timeout aguardando conexão/QR code após ${timeout}ms`));
        } else {
          setTimeout(checkConnection, 1000);
        }
      };
      
      checkConnection();
    });
  }

  private async handleIncomingMessages(userId: number, messageUpdate: any) {
    const { messages } = messageUpdate;
    
    for (const message of messages) {
      if (message.key.fromMe) continue; // Ignorar mensagens enviadas por nós
      
      try {
        const phoneNumber = message.key.remoteJid?.replace('@c.us', '');
        if (!phoneNumber) continue;

        // Buscar ou criar lead
        let lead = await storage.getLeadByPhone(phoneNumber);
        if (!lead) {
          lead = await storage.createLead({
            name: message.pushName || `Contato ${phoneNumber}`,
            phone: phoneNumber,
            status: 'new',
            assignedTo: userId,
            source: 'whatsapp',
          });
        }

        // Buscar ou criar chat
        let chat = await storage.getChatByWhatsappId(message.key.remoteJid);
        if (!chat) {
          chat = await storage.createChat({
            leadId: lead.id,
            whatsappChatId: message.key.remoteJid,
          });
        }

        // Salvar mensagem
        const messageText = message.message?.conversation || 
                           message.message?.extendedTextMessage?.text || 
                           '[Mídia]';

        await storage.createMessage({
          chatId: chat.id,
          whatsappMessageId: message.key.id,
          content: messageText,
          fromMe: false,
          timestamp: new Date(message.messageTimestamp * 1000),
        });

        // Atualizar último horário da mensagem no chat
        await storage.updateChatLastMessage(chat.id, new Date(message.messageTimestamp * 1000));

        // Criar atividade
        await storage.createActivity({
          userId,
          leadId: lead.id,
          type: 'message_received',
          description: `Nova mensagem: ${messageText.substring(0, 50)}...`,
        });

        // Notificar via WebSocket
        webSocketService.broadcastToAll('new_message', {
          chatId: chat.id,
          leadId: lead.id,
          message: messageText,
          timestamp: new Date(message.messageTimestamp * 1000),
        });

        console.log(`📨 Nova mensagem de ${phoneNumber}: ${messageText}`);

      } catch (error) {
        console.error('❌ Erro ao processar mensagem recebida:', error);
      }
    }
  }

  async sendMessage(userId: number, chatId: string, content: string): Promise<{ success: boolean; error?: string }> {
    try {
      const client = this.clients.get(userId);
      if (!client || !client.sock || !client.isConnected) {
        return { success: false, error: 'Cliente WhatsApp não conectado' };
      }

      console.log(`📤 Enviando mensagem real para ${chatId}: ${content}`);
      await client.sock.sendMessage(chatId, { text: content });
      
      return { success: true };
    } catch (error) {
      console.error('❌ Erro ao enviar mensagem:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Erro desconhecido' 
      };
    }
  }

  async disconnectClient(userId: number): Promise<void> {
    const client = this.clients.get(userId);
    if (client && client.sock) {
      await client.sock.logout();
      this.clients.delete(userId);
      await storage.updateSessionStatus(userId, false);
      console.log(`🔌 Cliente WhatsApp desconectado para usuário ${userId}`);
      
      // Limpar processos após desconexão
      this.cleanupChromiumProcesses();
    }
  }

  getClientStatus(userId: number): { isConnected: boolean; phoneNumber?: string } {
    const client = this.clients.get(userId);
    return {
      isConnected: client?.isConnected || false,
      phoneNumber: client?.phoneNumber,
    };
  }

  getAllClientsStatus(): Record<number, { isConnected: boolean; phoneNumber?: string }> {
    const status: Record<number, { isConnected: boolean; phoneNumber?: string }> = {};
    
    for (const [userId, client] of this.clients) {
      status[userId] = {
        isConnected: client.isConnected,
        phoneNumber: client.phoneNumber,
      };
    }
    
    return status;
  }
}

export const whatsappClientService = new WhatsAppClientService();

