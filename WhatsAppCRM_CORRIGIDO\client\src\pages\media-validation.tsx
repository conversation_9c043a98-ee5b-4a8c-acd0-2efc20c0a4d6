import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { MediaValidationPanel } from '@/components/media-validation-panel';
import { MessageTestPanel } from '@/components/message-test-panel';
import { Badge } from '@/components/ui/badge';
import { 
  Eye, 
  Send, 
  TestTube,
  CheckCircle2,
  Image as ImageIcon,
  Video as VideoIcon,
  Music,
  FileText
} from 'lucide-react';

export default function MediaValidationPage() {
  const [completedTests, setCompletedTests] = useState({
    imageViewing: false,
    videoPlayback: false,
    audioPlayback: false,
    documentViewing: false,
    zoomControls: false,
    rotationControls: false,
    fullscreenMode: false,
    downloadFunction: false,
    keyboardShortcuts: false
  });

  const validationSteps = [
    {
      id: 'imageViewing',
      title: 'Visualização de Imagens',
      description: 'Carregar e exibir imagens em fullscreen',
      icon: <ImageIcon className="h-5 w-5" />,
      completed: completedTests.imageViewing,
      features: ['JPG', 'PNG', 'GIF', 'WebP']
    },
    {
      id: 'videoPlayback',
      title: 'Reprodução de Vídeos',
      description: 'Play/pause, controles de timeline e volume',
      icon: <VideoIcon className="h-5 w-5" />,
      completed: completedTests.videoPlayback,
      features: ['MP4', 'WebM', 'AVI', 'MOV']
    },
    {
      id: 'audioPlayback',
      title: 'Reprodução de Áudios',
      description: 'Interface customizada para reprodução de áudio',
      icon: <Music className="h-5 w-5" />,
      completed: completedTests.audioPlayback,
      features: ['MP3', 'WAV', 'OGG', 'M4A']
    },
    {
      id: 'documentViewing',
      title: 'Visualização de Documentos',
      description: 'Preview e download de documentos',
      icon: <FileText className="h-5 w-5" />,
      completed: completedTests.documentViewing,
      features: ['PDF', 'DOC', 'TXT', 'Outros']
    },
    {
      id: 'zoomControls',
      title: 'Controles de Zoom',
      description: 'Zoom in/out para imagens e vídeos',
      icon: <Eye className="h-5 w-5" />,
      completed: completedTests.zoomControls,
      features: ['25% - 500%', 'Botões +/-', 'Teclas +/-']
    },
    {
      id: 'rotationControls',
      title: 'Controles de Rotação',
      description: 'Rotação em 90° para imagens',
      icon: <Eye className="h-5 w-5" />,
      completed: completedTests.rotationControls,
      features: ['Horário', 'Anti-horário', 'Tecla R']
    },
    {
      id: 'fullscreenMode',
      title: 'Modo Fullscreen',
      description: 'Visualização em tela cheia',
      icon: <Eye className="h-5 w-5" />,
      completed: completedTests.fullscreenMode,
      features: ['Botão F', 'ESC sair', 'API Fullscreen']
    },
    {
      id: 'downloadFunction',
      title: 'Função Download',
      description: 'Download direto de todos os tipos de mídia',
      icon: <Eye className="h-5 w-5" />,
      completed: completedTests.downloadFunction,
      features: ['Nome original', 'Tecla D', 'Progresso']
    },
    {
      id: 'keyboardShortcuts',
      title: 'Atalhos do Teclado',
      description: 'Navegação completa por teclado',
      icon: <Eye className="h-5 w-5" />,
      completed: completedTests.keyboardShortcuts,
      features: ['ESC', '← →', 'Espaço', '+/-', 'R', 'D', 'F']
    }
  ];

  const completedCount = Object.values(completedTests).filter(Boolean).length;
  const totalTests = validationSteps.length;
  const progressPercentage = (completedCount / totalTests) * 100;

  const markTestCompleted = (testId: string) => {
    setCompletedTests(prev => ({
      ...prev,
      [testId]: true
    }));
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Validação Completa de Visualização de Mídia</h1>
              <p className="text-muted-foreground mt-2">
                Etapa 5 - Teste fullscreen de imagens, vídeos, áudios e documentos com todos os controles
              </p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-primary">{completedCount}/{totalTests}</div>
              <div className="text-sm text-muted-foreground">Validações Concluídas</div>
            </div>
          </div>

          {/* Progress Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TestTube className="h-5 w-5" />
                Progresso da Validação de Mídia
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <div className="w-full bg-muted rounded-full h-3">
                      <div 
                        className="bg-primary h-3 rounded-full transition-all duration-500 ease-out"
                        style={{ width: `${progressPercentage}%` }}
                      />
                    </div>
                  </div>
                  <span className="text-sm font-medium">{progressPercentage.toFixed(0)}%</span>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {validationSteps.map((step) => (
                    <div 
                      key={step.id}
                      className={`flex items-start gap-3 p-4 rounded-lg border ${
                        step.completed 
                          ? 'bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800' 
                          : 'bg-muted/50 border-muted'
                      }`}
                    >
                      <div className={`flex-shrink-0 ${step.completed ? 'text-green-600' : 'text-muted-foreground'}`}>
                        {step.completed ? <CheckCircle2 className="h-5 w-5" /> : step.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm mb-1">{step.title}</div>
                        <div className="text-xs text-muted-foreground mb-2">{step.description}</div>
                        <div className="flex flex-wrap gap-1">
                          {step.features.map((feature, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <Badge variant={step.completed ? "default" : "secondary"} className="text-xs">
                        {step.completed ? "OK" : "Pendente"}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Validation Panels */}
          <Tabs defaultValue="media-viewer" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="media-viewer" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Visualizador de Mídia
              </TabsTrigger>
              <TabsTrigger value="message-tests" className="flex items-center gap-2">
                <Send className="h-4 w-4" />
                Testes de Mensagem
              </TabsTrigger>
            </TabsList>

            <TabsContent value="media-viewer" className="space-y-6">
              <MediaValidationPanel />
            </TabsContent>

            <TabsContent value="message-tests" className="space-y-6">
              <MessageTestPanel />
            </TabsContent>
          </Tabs>

          {/* Validation Instructions */}
          <Card>
            <CardHeader>
              <CardTitle>Instruções de Validação</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-3">Testes de Visualização</h4>
                  <ol className="text-sm space-y-2 list-decimal list-inside">
                    <li>Gere ou carregue mídia de teste</li>
                    <li>Clique em cada item para abrir o visualizador</li>
                    <li>Teste todos os controles disponíveis</li>
                    <li>Verifique os atalhos do teclado</li>
                    <li>Teste o modo fullscreen</li>
                    <li>Valide a função de download</li>
                  </ol>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-3">Atalhos do Teclado</h4>
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span>Fechar visualizador:</span>
                      <Badge variant="outline">ESC</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Navegar mídia:</span>
                      <Badge variant="outline">← →</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Play/Pause:</span>
                      <Badge variant="outline">Espaço</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Zoom:</span>
                      <Badge variant="outline">+ -</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Rotacionar:</span>
                      <Badge variant="outline">R</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Download:</span>
                      <Badge variant="outline">D</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Fullscreen:</span>
                      <Badge variant="outline">F</Badge>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg border border-blue-200 dark:border-blue-800">
                <h4 className="font-semibold mb-2">Recursos Validados</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Imagens:</strong>
                    <ul className="list-disc list-inside ml-2 mt-1">
                      <li>Zoom 25% até 500%</li>
                      <li>Rotação em 90°</li>
                      <li>Visualização fullscreen</li>
                      <li>Download com nome original</li>
                    </ul>
                  </div>
                  <div>
                    <strong>Vídeos:</strong>
                    <ul className="list-disc list-inside ml-2 mt-1">
                      <li>Controles nativos HTML5</li>
                      <li>Timeline interativa</li>
                      <li>Controles de volume</li>
                      <li>Fullscreen nativo</li>
                    </ul>
                  </div>
                  <div>
                    <strong>Áudios:</strong>
                    <ul className="list-disc list-inside ml-2 mt-1">
                      <li>Interface customizada</li>
                      <li>Controles de reprodução</li>
                      <li>Skip ±10 segundos</li>
                      <li>Controle de volume</li>
                    </ul>
                  </div>
                  <div>
                    <strong>Documentos:</strong>
                    <ul className="list-disc list-inside ml-2 mt-1">
                      <li>Preview para PDFs</li>
                      <li>Informações do arquivo</li>
                      <li>Download direto</li>
                      <li>Ações contextuais</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}