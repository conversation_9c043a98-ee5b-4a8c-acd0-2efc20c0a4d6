import { useState, useEffect, useContext, createContext } from 'react';

type SupportedLanguage = 'pt-BR' | 'en-US';

interface Translation {
  // Common
  common: {
    save: string;
    cancel: string;
    delete: string;
    edit: string;
    send: string;
    search: string;
    loading: string;
    error: string;
    success: string;
    confirm: string;
    back: string;
    next: string;
    close: string;
    yes: string;
    no: string;
  };
  
  // WhatsApp Interface
  whatsapp: {
    title: string;
    conversations: string;
    searchConversations: string;
    noMessages: string;
    typing: string;
    online: string;
    offline: string;
    lastSeen: string;
    reply: string;
    forward: string;
    star: string;
    connect: string;
    connected: string;
    disconnected: string;
    audioRecording: string;
    sendAudio: string;
    forwardMessage: string;
    originalMessage: string;
    customMessage: string;
    messageForwarded: string;
  };
  
  // CRM
  crm: {
    title: string;
    clients: string;
    tasks: string;
    notes: string;
    addClient: string;
    clientName: string;
    clientEmail: string;
    clientPhone: string;
    addNote: string;
    internalNotes: string;
  };
  
  // Templates
  templates: {
    title: string;
    messageTemplates: string;
    createTemplate: string;
    templateName: string;
    templateContent: string;
    useTemplate: string;
  };
  
  // Audit
  audit: {
    title: string;
    auditLogs: string;
    timestamp: string;
    action: string;
    details: string;
  };
}

const ptBR: Translation = {
  common: {
    save: "Salvar",
    cancel: "Cancelar",
    delete: "Excluir",
    edit: "Editar",
    send: "Enviar",
    search: "Buscar",
    loading: "Carregando...",
    error: "Erro",
    success: "Sucesso",
    confirm: "Confirmar",
    back: "Voltar",
    next: "Próximo",
    close: "Fechar",
    yes: "Sim",
    no: "Não"
  },
  whatsapp: {
    title: "WhatsApp CRM",
    conversations: "Conversas",
    searchConversations: "Pesquisar conversas...",
    noMessages: "Nenhuma mensagem ainda",
    typing: "digitando...",
    online: "online",
    offline: "offline",
    lastSeen: "visto por último",
    reply: "Responder",
    forward: "Encaminhar",
    star: "Marcar como favorita",
    connect: "Conectar",
    connected: "Conectado",
    disconnected: "Desconectado",
    audioRecording: "Gravando áudio...",
    sendAudio: "Enviar áudio",
    forwardMessage: "Encaminhar mensagem",
    originalMessage: "Mensagem original",
    customMessage: "Mensagem personalizada",
    messageForwarded: "Mensagem encaminhada"
  },
  crm: {
    title: "CRM",
    clients: "Clientes",
    tasks: "Tarefas",
    notes: "Notas",
    addClient: "Adicionar Cliente",
    clientName: "Nome",
    clientEmail: "E-mail",
    clientPhone: "Telefone",
    addNote: "Adicionar Nota",
    internalNotes: "Notas Internas"
  },
  templates: {
    title: "Templates",
    messageTemplates: "Templates de Mensagem",
    createTemplate: "Criar Template",
    templateName: "Nome do Template",
    templateContent: "Conteúdo",
    useTemplate: "Usar Template"
  },
  audit: {
    title: "Auditoria",
    auditLogs: "Logs de Auditoria",
    timestamp: "Data/Hora",
    action: "Ação",
    details: "Detalhes"
  }
};

const enUS: Translation = {
  common: {
    save: "Save",
    cancel: "Cancel",
    delete: "Delete",
    edit: "Edit",
    send: "Send",
    search: "Search",
    loading: "Loading...",
    error: "Error",
    success: "Success",
    confirm: "Confirm",
    back: "Back",
    next: "Next",
    close: "Close",
    yes: "Yes",
    no: "No"
  },
  whatsapp: {
    title: "WhatsApp CRM",
    conversations: "Conversations",
    searchConversations: "Search conversations...",
    noMessages: "No messages yet",
    typing: "typing...",
    online: "online",
    offline: "offline",
    lastSeen: "last seen",
    reply: "Reply",
    forward: "Forward",
    star: "Star",
    connect: "Connect",
    connected: "Connected",
    disconnected: "Disconnected",
    audioRecording: "Recording audio...",
    sendAudio: "Send audio",
    forwardMessage: "Forward message",
    originalMessage: "Original message",
    customMessage: "Custom message",
    messageForwarded: "Message forwarded"
  },
  crm: {
    title: "CRM",
    clients: "Clients",
    tasks: "Tasks",
    notes: "Notes",
    addClient: "Add Client",
    clientName: "Name",
    clientEmail: "Email",
    clientPhone: "Phone",
    addNote: "Add Note",
    internalNotes: "Internal Notes"
  },
  templates: {
    title: "Templates",
    messageTemplates: "Message Templates",
    createTemplate: "Create Template",
    templateName: "Template Name",
    templateContent: "Content",
    useTemplate: "Use Template"
  },
  audit: {
    title: "Audit",
    auditLogs: "Audit Logs",
    timestamp: "Timestamp",
    action: "Action",
    details: "Details"
  }
};

const translations = {
  'pt-BR': ptBR,
  'en-US': enUS
};

interface I18nContextType {
  language: SupportedLanguage;
  setLanguage: (lang: SupportedLanguage) => void;
  t: Translation;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

export function useI18n() {
  const context = useContext(I18nContext);
  if (!context) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
}

export function useI18nLogic() {
  const [language, setLanguageState] = useState<SupportedLanguage>('pt-BR');

  useEffect(() => {
    // Load language from localStorage
    const savedLanguage = localStorage.getItem('language') as SupportedLanguage;
    if (savedLanguage && translations[savedLanguage]) {
      setLanguageState(savedLanguage);
    } else {
      // Detect browser language
      const browserLang = navigator.language;
      if (browserLang.startsWith('pt')) {
        setLanguageState('pt-BR');
      } else {
        setLanguageState('en-US');
      }
    }
  }, []);

  const setLanguage = (lang: SupportedLanguage) => {
    setLanguageState(lang);
    localStorage.setItem('language', lang);
  };

  const t = translations[language];

  return {
    language,
    setLanguage,
    t
  };
}

export { I18nContext, translations };
export type { SupportedLanguage, Translation };