import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock } from "lucide-react";
import { formatDate } from "@/lib/utils";
import type { Task } from "@shared/schema";

interface TaskCardProps {
  task: Task;
  onEdit?: (task: Task) => void;
}

export default function TaskCard({ task, onEdit }: TaskCardProps) {
  const priorityColors = {
    low: "bg-green-100 text-green-800",
    medium: "bg-yellow-100 text-yellow-800", 
    high: "bg-red-100 text-red-800"
  };

  const priorityLabels = {
    low: "Baixa",
    medium: "Média",
    high: "Alta"
  };

  return (
    <Card 
      className="task-card cursor-pointer" 
      onClick={() => onEdit?.(task)}
    >
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-start justify-between">
            <h4 className="font-medium text-slate-800 text-sm leading-relaxed">
              {task.title}
            </h4>
            <Badge 
              variant="secondary" 
              className={`text-xs ${priorityColors[task.priority as keyof typeof priorityColors]}`}
            >
              {priorityLabels[task.priority as keyof typeof priorityLabels]}
            </Badge>
          </div>
          
          {task.description && (
            <p className="text-xs text-slate-600 line-clamp-2">
              {task.description}
            </p>
          )}
          
          {task.dueDate && (
            <div className="flex items-center space-x-2 text-xs text-slate-500">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(task.dueDate)}</span>
            </div>
          )}
          
          <div className="flex items-center justify-between text-xs text-slate-400">
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>{formatDate(task.createdAt!)}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
