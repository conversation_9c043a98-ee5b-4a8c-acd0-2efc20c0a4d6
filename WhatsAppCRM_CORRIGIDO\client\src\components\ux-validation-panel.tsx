import { useState, useRef, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  MessageSquare, 
  ArrowDown, 
  ArrowUp,
  Loader2, 
  Send, 
  RefreshCw,
  CheckCircle2,
  Clock,
  Users,
  Search,
  User
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface MockMessage {
  id: string;
  content: string;
  fromMe: boolean;
  timestamp: Date;
  status: 'sending' | 'sent' | 'delivered' | 'read';
  avatar?: string;
  sender?: string;
}

interface MockChat {
  id: string;
  name: string;
  lastMessage: string;
  timestamp: Date;
  unreadCount: number;
  avatar?: string;
  isOnline: boolean;
}

export function UXValidationPanel() {
  const [messages, setMessages] = useState<MockMessage[]>([]);
  const [chats, setChats] = useState<MockChat[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [isLoadingChats, setIsLoadingChats] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [hasMoreHistory, setHasMoreHistory] = useState(true);
  const [hasMoreChats, setHasMoreChats] = useState(true);
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);
  const [validationTests, setValidationTests] = useState({
    autoScroll: false,
    incrementalHistory: false,
    progressiveChats: false,
    loadingStates: false
  });

  const { toast } = useToast();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const chatsContainerRef = useRef<HTMLDivElement>(null);

  // Generate mock data
  const generateMockMessages = (count: number, isHistory = false): MockMessage[] => {
    const messages: MockMessage[] = [];
    const baseTime = isHistory ? Date.now() - (count * 60000) : Date.now();
    
    for (let i = 0; i < count; i++) {
      messages.push({
        id: `msg_${Date.now()}_${i}`,
        content: isHistory 
          ? `Mensagem histórica ${i + 1} - Esta é uma mensagem mais antiga do chat.`
          : `Nova mensagem ${i + 1} - ${Math.random() > 0.5 ? 'Mensagem de teste' : 'Resposta automática'}`,
        fromMe: Math.random() > 0.5,
        timestamp: new Date(baseTime + (i * (isHistory ? -60000 : 5000))),
        status: ['sent', 'delivered', 'read'][Math.floor(Math.random() * 3)] as any,
        sender: Math.random() > 0.5 ? 'João Silva' : 'Maria Santos'
      });
    }
    
    return messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  };

  const generateMockChats = (count: number): MockChat[] => {
    const names = [
      'João Silva', 'Maria Santos', 'Pedro Oliveira', 'Ana Costa', 'Carlos Lima',
      'Fernanda Rocha', 'Roberto Alves', 'Juliana Campos', 'Marcos Pereira', 'Lucia Fernandes'
    ];
    
    return Array.from({ length: count }, (_, i) => ({
      id: `chat_${i}`,
      name: names[i % names.length] + ` ${i + 1}`,
      lastMessage: `Última mensagem do chat ${i + 1}...`,
      timestamp: new Date(Date.now() - (i * 300000)),
      unreadCount: Math.floor(Math.random() * 5),
      isOnline: Math.random() > 0.3
    }));
  };

  // Auto-scroll to bottom when new message is sent
  const scrollToBottom = useCallback((smooth = true) => {
    if (autoScrollEnabled && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ 
        behavior: smooth ? 'smooth' : 'auto',
        block: 'end'
      });
    }
  }, [autoScrollEnabled]);

  // Load more history (infinite scroll)
  const loadMoreHistory = useCallback(async () => {
    if (isLoadingHistory || !hasMoreHistory) return;
    
    setIsLoadingHistory(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const historyMessages = generateMockMessages(10, true);
    
    setMessages(prev => [...historyMessages, ...prev]);
    
    // Simulate reaching end of history
    if ((messages || []).length > 50) {
      setHasMoreHistory(false);
    }
    
    setIsLoadingHistory(false);
    
    toast({
      title: "Histórico Carregado",
      description: `${historyMessages.length} mensagens antigas carregadas`,
      variant: "default"
    });
  }, [isLoadingHistory, hasMoreHistory, (messages || []).length]);

  // Load more chats (progressive loading)
  const loadMoreChats = useCallback(async () => {
    if (isLoadingChats || !hasMoreChats) return;
    
    setIsLoadingChats(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newChats = generateMockChats(5);
    
    setChats(prev => [...prev, ...newChats]);
    
    // Simulate reaching end of chats
    if (chats.length > 30) {
      setHasMoreChats(false);
    }
    
    setIsLoadingChats(false);
    
    toast({
      title: "Chats Carregados",
      description: `${newChats.length} novos chats carregados`,
      variant: "default"
    });
  }, [isLoadingChats, hasMoreChats, chats.length]);

  // Send message with auto-scroll
  const sendMessage = async () => {
    if (!newMessage.trim()) return;
    
    setIsSending(true);
    
    const message: MockMessage = {
      id: `msg_${Date.now()}`,
      content: newMessage,
      fromMe: true,
      timestamp: new Date(),
      status: 'sending'
    };
    
    setMessages(prev => [...prev, message]);
    setNewMessage('');
    
    // Auto-scroll immediately when sending
    setTimeout(() => scrollToBottom(true), 100);
    
    // Simulate sending delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Update status to sent
    setMessages(prev => 
      prev.map(msg => 
        msg.id === message.id 
          ? { ...msg, status: 'sent' }
          : msg
      )
    );
    
    // Simulate delivery after another delay
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === message.id 
            ? { ...msg, status: 'delivered' }
            : msg
        )
      );
    }, 2000);
    
    setIsSending(false);
    
    if (!validationTests.autoScroll) {
      setValidationTests(prev => ({ ...prev, autoScroll: true }));
      toast({
        title: "Auto-scroll Validado",
        description: "Scroll automático funcionando ao enviar mensagem",
        variant: "default"
      });
    }
  };

  // Handle scroll events for history loading
  const handleMessagesScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop } = e.currentTarget;
    
    // Load more when scrolled to top
    if (scrollTop === 0 && hasMoreHistory && !isLoadingHistory) {
      loadMoreHistory();
      
      if (!validationTests.incrementalHistory) {
        setValidationTests(prev => ({ ...prev, incrementalHistory: true }));
        toast({
          title: "Scroll Incremental Validado",
          description: "Carregamento de histórico por scroll funcionando",
          variant: "default"
        });
      }
    }
  }, [hasMoreHistory, isLoadingHistory, loadMoreHistory, validationTests.incrementalHistory]);

  // Handle scroll events for chat loading
  const handleChatsScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    
    // Load more when scrolled to bottom
    if (scrollTop + clientHeight >= scrollHeight - 10 && hasMoreChats && !isLoadingChats) {
      loadMoreChats();
      
      if (!validationTests.progressiveChats) {
        setValidationTests(prev => ({ ...prev, progressiveChats: true }));
        toast({
          title: "Carregamento Progressivo Validado",
          description: "Carregamento progressivo de chats funcionando",
          variant: "default"
        });
      }
    }
  }, [hasMoreChats, isLoadingChats, loadMoreChats, validationTests.progressiveChats]);

  // Initialize with some data
  useEffect(() => {
    const initialMessages = generateMockMessages(5);
    const initialChats = generateMockChats(8);
    
    setMessages(initialMessages);
    setChats(initialChats);
    setSelectedChat(initialChats[0]?.id);
    
    // Auto-scroll on initial load
    setTimeout(() => scrollToBottom(false), 100);
  }, [scrollToBottom]);

  // Simulate receiving new messages
  const simulateIncomingMessage = () => {
    const incomingMessage: MockMessage = {
      id: `incoming_${Date.now()}`,
      content: `Mensagem recebida: ${Math.random().toString(36).substr(2, 9)}`,
      fromMe: false,
      timestamp: new Date(),
      status: 'delivered',
      sender: 'Contato Externo'
    };
    
    setMessages(prev => [...prev, incomingMessage]);
    
    // Auto-scroll for incoming messages too
    setTimeout(() => scrollToBottom(true), 100);
  };

  // Test loading states
  const testLoadingStates = async () => {
    setIsLoadingHistory(true);
    setIsLoadingChats(true);
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsLoadingHistory(false);
    setIsLoadingChats(false);
    
    setValidationTests(prev => ({ ...prev, loadingStates: true }));
    
    toast({
      title: "Loading States Validados",
      description: "Estados de carregamento funcionando corretamente",
      variant: "default"
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sending':
        return <Clock className="h-3 w-3 text-gray-400" />;
      case 'sent':
        return <CheckCircle2 className="h-3 w-3 text-gray-400" />;
      case 'delivered':
        return <CheckCircle2 className="h-3 w-3 text-blue-500" />;
      case 'read':
        return <CheckCircle2 className="h-3 w-3 text-green-500" />;
      default:
        return null;
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const completedTests = Object.values(validationTests).filter(Boolean).length;
  const totalTests = Object.keys(validationTests).length;

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Validação Completa de UX</h2>
        <Badge variant="outline" className="text-lg px-3 py-1">
          {completedTests}/{totalTests} Testes
        </Badge>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Progresso dos Testes UX</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Progress value={(completedTests / totalTests) * 100} className="w-full" />
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[
                { key: 'autoScroll', label: 'Auto-scroll', desc: 'Scroll automático ao enviar' },
                { key: 'incrementalHistory', label: 'Histórico Incremental', desc: 'Carregamento por scroll' },
                { key: 'progressiveChats', label: 'Chats Progressivos', desc: 'Carregamento progressivo' },
                { key: 'loadingStates', label: 'Loading States', desc: 'Estados de carregamento' }
              ].map((test) => (
                <div 
                  key={test.key}
                  className={`p-3 rounded-lg border ${
                    validationTests[test.key as keyof typeof validationTests]
                      ? 'bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800'
                      : 'bg-muted/50'
                  }`}
                >
                  <div className="flex items-center gap-2 mb-1">
                    {validationTests[test.key as keyof typeof validationTests] ? (
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                    ) : (
                      <Clock className="h-4 w-4 text-gray-400" />
                    )}
                    <span className="font-medium text-sm">{test.label}</span>
                  </div>
                  <p className="text-xs text-muted-foreground">{test.desc}</p>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* UX Test Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Chat List */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Chats</CardTitle>
              <Button
                onClick={loadMoreChats}
                size="sm"
                variant="outline"
                disabled={isLoadingChats}
              >
                {isLoadingChats ? <Loader2 className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
              </Button>
            </div>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar chats..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <ScrollArea 
              ref={chatsContainerRef}
              className="h-96"
              onScrollCapture={handleChatsScroll}
            >
              <div className="space-y-1 p-3">
                {chats
                  .filter(chat => chat.name.toLowerCase().includes(searchTerm.toLowerCase()))
                  .map((chat) => (
                    <div
                      key={chat.id}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        selectedChat === chat.id 
                          ? 'bg-primary/10 border border-primary/20' 
                          : 'hover:bg-muted/50'
                      }`}
                      onClick={() => setSelectedChat(chat.id)}
                    >
                      <div className="flex items-center gap-3">
                        <div className="relative">
                          <div className="w-10 h-10 bg-primary/20 rounded-full flex items-center justify-center">
                            <User className="h-5 w-5" />
                          </div>
                          {chat.isOnline && (
                            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white" />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium text-sm truncate">{chat.name}</h4>
                            <span className="text-xs text-muted-foreground">
                              {formatTime(chat.timestamp)}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground truncate">
                            {chat.lastMessage}
                          </p>
                        </div>
                        {chat.unreadCount > 0 && (
                          <Badge variant="default" className="h-5 min-w-5 text-xs">
                            {chat.unreadCount}
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                
                {isLoadingChats && (
                  <div className="flex items-center justify-center p-4">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                    <span className="ml-2 text-sm text-muted-foreground">
                      Carregando mais chats...
                    </span>
                  </div>
                )}
                
                {!hasMoreChats && chats.length > 0 && (
                  <div className="text-center p-4 text-sm text-muted-foreground">
                    Todos os chats foram carregados
                  </div>
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Messages Area */}
        <Card className="lg:col-span-2">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Mensagens</CardTitle>
              <div className="flex items-center gap-2">
                <Button
                  onClick={() => setAutoScrollEnabled(!autoScrollEnabled)}
                  size="sm"
                  variant={autoScrollEnabled ? "default" : "outline"}
                >
                  <ArrowDown className="h-4 w-4" />
                  Auto-scroll
                </Button>
                <Button
                  onClick={simulateIncomingMessage}
                  size="sm"
                  variant="outline"
                >
                  <MessageSquare className="h-4 w-4" />
                  Simular Mensagem
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <ScrollArea 
              ref={messagesContainerRef}
              className="h-96"
              onScrollCapture={handleMessagesScroll}
            >
              <div className="p-3">
                {isLoadingHistory && (
                  <div className="flex items-center justify-center p-4 mb-4">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                    <span className="ml-2 text-sm text-muted-foreground">
                      Carregando histórico...
                    </span>
                  </div>
                )}
                
                {!hasMoreHistory && (messages || []).length > 0 && (
                  <div className="text-center p-4 mb-4 text-sm text-muted-foreground border-b">
                    Início da conversa
                  </div>
                )}
                
                <div className="space-y-3">
                  {(messages || []).map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.fromMe ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-xs lg:max-w-sm px-4 py-2 rounded-lg ${
                          message.fromMe
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted'
                        }`}
                      >
                        {!message.fromMe && message.sender && (
                          <p className="text-xs font-medium mb-1 opacity-70">
                            {message.sender}
                          </p>
                        )}
                        <p className="text-sm">{message.content}</p>
                        <div className="flex items-center justify-end gap-1 mt-1">
                          <span className="text-xs opacity-70">
                            {formatTime(message.timestamp)}
                          </span>
                          {message.fromMe && getStatusIcon(message.status)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>
            
            {/* Message Input */}
            <div className="border-t p-3">
              <div className="flex gap-2">
                <Textarea
                  placeholder="Digite sua mensagem..."
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      sendMessage();
                    }
                  }}
                  className="min-h-[60px] resize-none"
                />
                <Button
                  onClick={sendMessage}
                  disabled={!newMessage.trim() || isSending}
                  size="lg"
                >
                  {isSending ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Test Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Controles de Teste</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <Button
              onClick={testLoadingStates}
              variant="outline"
            >
              <Loader2 className="h-4 w-4 mr-2" />
              Testar Loading States
            </Button>
            
            <Button
              onClick={() => loadMoreHistory()}
              disabled={isLoadingHistory || !hasMoreHistory}
              variant="outline"
            >
              <ArrowUp className="h-4 w-4 mr-2" />
              Carregar Histórico
            </Button>
            
            <Button
              onClick={() => loadMoreChats()}
              disabled={isLoadingChats || !hasMoreChats}
              variant="outline"
            >
              <Users className="h-4 w-4 mr-2" />
              Carregar Mais Chats
            </Button>
            
            <Button
              onClick={() => scrollToBottom(true)}
              variant="outline"
            >
              <ArrowDown className="h-4 w-4 mr-2" />
              Scroll para Baixo
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}