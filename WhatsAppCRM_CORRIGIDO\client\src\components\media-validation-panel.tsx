import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MediaViewer } from './media-viewer';
import { 
  Image as ImageIcon, 
  Video as VideoIcon, 
  Music, 
  FileText, 
  Eye,
  Upload,
  TestTube,
  CheckCircle2
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface MediaItem {
  id: string;
  type: 'image' | 'video' | 'audio' | 'document';
  name: string;
  url: string;
  size: number;
  mimetype: string;
  thumbnail?: string;
}

interface ValidationTest {
  id: string;
  name: string;
  type: 'image' | 'video' | 'audio' | 'document';
  completed: boolean;
  features: string[];
}

export function MediaValidationPanel() {
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [viewerOpen, setViewerOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [validationTests, setValidationTests] = useState<ValidationTest[]>([
    {
      id: 'image-test',
      name: 'Visualização de Imagens',
      type: 'image',
      completed: false,
      features: ['Zoom In/Out', 'Rotação', 'Fullscreen', 'Download', 'Navegação por teclado']
    },
    {
      id: 'video-test',
      name: 'Reprodução de Vídeos',
      type: 'video',
      completed: false,
      features: ['Play/Pause', 'Controles de volume', 'Fullscreen', 'Seek/Timeline', 'Download']
    },
    {
      id: 'audio-test',
      name: 'Reprodução de Áudios',
      type: 'audio',
      completed: false,
      features: ['Play/Pause', 'Controles customizados', 'Timeline', 'Volume', 'Skip ±10s']
    },
    {
      id: 'document-test',
      name: 'Visualização de Documentos',
      type: 'document',
      completed: false,
      features: ['Preview de PDF', 'Download direto', 'Info do arquivo', 'Ações contextuais']
    }
  ]);

  const { toast } = useToast();

  // Sample media items for testing
  const sampleMediaItems: MediaItem[] = [
    {
      id: 'img-1',
      type: 'image',
      name: 'test-image-1.jpg',
      url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjYwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMDBhODg0Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSI0OHB4IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPklNQUdFTSBURVNURTwvdGV4dD4KICA8Y2lyY2xlIGN4PSIyMDAiIGN5PSIxNTAiIHI9IjUwIiBmaWxsPSIjZmZmIiBvcGFjaXR5PSIwLjciLz4KICA8Y2lyY2xlIGN4PSI2MDAiIGN5PSI0NTAiIHI9IjcwIiBmaWxsPSIjZmZmIiBvcGFjaXR5PSIwLjUiLz4KICA8cmVjdCB4PSIzNTAiIHk9IjIwMCIgd2lkdGg9IjEwMCIgaGVpZ2h0PSIyMDAiIGZpbGw9IiNmZmYiIG9wYWNpdHk9IjAuMyIvPgo8L3N2Zz4=',
      size: 245760,
      mimetype: 'image/jpeg'
    },
    {
      id: 'img-2',
      type: 'image',
      name: 'test-image-2.png',
      url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjgwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZCIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzNiODJmNiIvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiM4YjVjZjYiLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZCkiLz4KICA8dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjM2cHgiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+SU1BR0VNIFBPUlRSQUlUPC90ZXh0PgogIDxwb2x5Z29uIHBvaW50cz0iMzAwLDE1MCA0MDAsMjUwIDIwMCwyNTAiIGZpbGw9IiNmZmYiIG9wYWNpdHk9IjAuNyIvPgogIDxwb2x5Z29uIHBvaW50cz0iMzAwLDU1MCA0MDAsMDUwIDIwMCw2NTAiIGZpbGw9IiNmZmYiIG9wYWNpdHk9IjAuNSIvPgo8L3N2Zz4=',
      size: 180340,
      mimetype: 'image/png'
    },
    {
      id: 'vid-1',
      type: 'video',
      name: 'test-video.mp4',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      size: 5510872,
      mimetype: 'video/mp4'
    },
    {
      id: 'aud-1',
      type: 'audio',
      name: 'test-audio.mp3',
      url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      size: 894732,
      mimetype: 'audio/mp3'
    },
    {
      id: 'doc-1',
      type: 'document',
      name: 'documento-teste.pdf',
      url: 'data:application/pdf;base64,JVBERi0xLjMKJcTl8uXrp/Og0MTGCjQgMCBvYmoKPDwKL0xlbmd0aCA0NTIKL0ZpbHRlciAvRmxhdGVEZWNvZGUKPj4Kc3RyZWFtCnic7Z',
      size: 125840,
      mimetype: 'application/pdf'
    }
  ];

  const createTestImage = (text: string, color: string): string => {
    const canvas = document.createElement('canvas');
    canvas.width = 800;
    canvas.height = 600;
    const ctx = canvas.getContext('2d');
    
    if (ctx) {
      // Background
      ctx.fillStyle = color;
      ctx.fillRect(0, 0, 800, 600);
      
      // Text
      ctx.fillStyle = 'white';
      ctx.font = 'bold 48px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(text, 400, 300);
      
      // Decorative elements
      ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
      ctx.fillRect(100, 100, 600, 20);
      ctx.fillRect(100, 480, 600, 20);
      
      ctx.beginPath();
      ctx.arc(150, 150, 40, 0, 2 * Math.PI);
      ctx.fill();
      
      ctx.beginPath();
      ctx.arc(650, 450, 60, 0, 2 * Math.PI);
      ctx.fill();
    }
    
    return canvas.toDataURL('image/png');
  };

  const generateTestMedia = () => {
    const testItems: MediaItem[] = [
      {
        id: 'generated-img-1',
        type: 'image',
        name: 'imagem-teste-zoom.png',
        url: createTestImage('TESTE DE ZOOM', '#3b82f6'),
        size: 245000,
        mimetype: 'image/png'
      },
      {
        id: 'generated-img-2',
        type: 'image',
        name: 'imagem-teste-rotacao.png',
        url: createTestImage('TESTE ROTAÇÃO', '#ef4444'),
        size: 250000,
        mimetype: 'image/png'
      },
      {
        id: 'generated-img-3',
        type: 'image',
        name: 'imagem-teste-fullscreen.png',
        url: createTestImage('TESTE FULLSCREEN', '#10b981'),
        size: 260000,
        mimetype: 'image/png'
      }
    ];

    setMediaItems([...mediaItems, ...testItems]);
    
    toast({
      title: "Mídia de Teste Gerada",
      description: `${testItems.length} itens de teste adicionados`,
      variant: "default"
    });
  };

  const loadSampleMedia = () => {
    setMediaItems(sampleMediaItems);
    
    toast({
      title: "Mídia de Exemplo Carregada",
      description: `${sampleMediaItems.length} itens carregados para teste`,
      variant: "default"
    });
  };

  const openViewer = (index: number) => {
    setCurrentIndex(index);
    setViewerOpen(true);
  };

  const markTestCompleted = (testId: string) => {
    setValidationTests(prev => 
      prev.map(test => 
        test.id === testId ? { ...test, completed: true } : test
      )
    );
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <ImageIcon className="h-4 w-4" />;
      case 'video':
        return <VideoIcon className="h-4 w-4" />;
      case 'audio':
        return <Music className="h-4 w-4" />;
      case 'document':
        return <FileText className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'image':
        return 'bg-blue-500';
      case 'video':
        return 'bg-purple-500';
      case 'audio':
        return 'bg-green-500';
      case 'document':
        return 'bg-orange-500';
      default:
        return 'bg-gray-500';
    }
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const completedTests = validationTests.filter(test => test.completed).length;
  const totalTests = validationTests.length;

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Validação de Visualização de Mídia</h2>
        <Badge variant="outline" className="text-lg px-3 py-1">
          {completedTests}/{totalTests} Validações
        </Badge>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Progresso dos Testes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="w-full bg-muted rounded-full h-3">
              <div 
                className="bg-primary h-3 rounded-full transition-all duration-500"
                style={{ width: `${(completedTests / totalTests) * 100}%` }}
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {validationTests.map((test) => (
                <div 
                  key={test.id}
                  className={`p-4 rounded-lg border ${
                    test.completed 
                      ? 'bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800' 
                      : 'bg-muted/50'
                  }`}
                >
                  <div className="flex items-center gap-3 mb-3">
                    <div className={`p-2 rounded-full ${getTypeColor(test.type)} text-white`}>
                      {getTypeIcon(test.type)}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">{test.name}</h4>
                      <Badge variant={test.completed ? "default" : "secondary"} className="text-xs">
                        {test.completed ? "Validado" : "Pendente"}
                      </Badge>
                    </div>
                    {test.completed && <CheckCircle2 className="h-5 w-5 text-green-600" />}
                  </div>
                  
                  <div className="text-sm text-muted-foreground">
                    <strong>Recursos testados:</strong>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      {test.features.map((feature, index) => (
                        <li key={index}>{feature}</li>
                      ))}
                    </ul>
                  </div>
                  
                  {!test.completed && (
                    <Button
                      onClick={() => markTestCompleted(test.id)}
                      size="sm"
                      className="mt-3 w-full"
                      variant="outline"
                    >
                      Marcar como Testado
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Media Generation */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Gerar Mídia de Teste</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Gere imagens de teste para validar recursos de zoom, rotação e fullscreen.
            </p>
            <Button onClick={generateTestMedia} className="w-full">
              <Upload className="h-4 w-4 mr-2" />
              Gerar Imagens de Teste
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Carregar Exemplos</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Carregue mídia de exemplo para testar todos os tipos de arquivo.
            </p>
            <Button onClick={loadSampleMedia} variant="outline" className="w-full">
              <TestTube className="h-4 w-4 mr-2" />
              Carregar Mídia de Exemplo
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Media Gallery */}
      {mediaItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Galeria de Mídia para Teste</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {mediaItems.map((item, index) => (
                <div
                  key={item.id}
                  className="relative group cursor-pointer border rounded-lg overflow-hidden hover:shadow-lg transition-all"
                  onClick={() => openViewer(index)}
                >
                  <div className={`aspect-square flex items-center justify-center ${getTypeColor(item.type)} text-white`}>
                    {item.type === 'image' ? (
                      <img 
                        src={item.url} 
                        alt={item.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const fallbackDiv = document.createElement('div');
                          fallbackDiv.className = 'flex items-center justify-center w-full h-full';
                          fallbackDiv.innerHTML = '<div class="flex items-center justify-center w-full h-full text-white">IMG</div>';
                          target.parentElement?.appendChild(fallbackDiv);
                        }}
                      />
                    ) : (
                      <div className="flex flex-col items-center gap-2">
                        {getTypeIcon(item.type)}
                        <span className="text-xs font-medium">
                          {item.type.toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>
                  
                  <div className="p-2">
                    <h4 className="text-sm font-medium truncate">{item.name}</h4>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(item.size)}
                    </p>
                  </div>
                  
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <Button size="sm" variant="secondary">
                      <Eye className="h-4 w-4 mr-2" />
                      Visualizar
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Keyboard Shortcuts Info */}
      <Card>
        <CardHeader>
          <CardTitle>Recursos do Visualizador</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3">Controles de Imagem</h4>
              <ul className="text-sm space-y-2">
                <li>• <strong>Zoom:</strong> Botões +/- ou teclas +/-</li>
                <li>• <strong>Rotação:</strong> Botões de rotação ou tecla R</li>
                <li>• <strong>Fullscreen:</strong> Botão fullscreen ou tecla F</li>
                <li>• <strong>Download:</strong> Botão download ou tecla D</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-3">Controles de Mídia</h4>
              <ul className="text-sm space-y-2">
                <li>• <strong>Play/Pause:</strong> Botão ou barra de espaço</li>
                <li>• <strong>Volume:</strong> Controles deslizantes</li>
                <li>• <strong>Seek:</strong> Clique na timeline</li>
                <li>• <strong>Skip:</strong> Botões ±10 segundos</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-3">Navegação</h4>
              <ul className="text-sm space-y-2">
                <li>• <strong>Anterior/Próximo:</strong> Setas esquerda/direita</li>
                <li>• <strong>Fechar:</strong> ESC ou botão X</li>
                <li>• <strong>Fullscreen:</strong> Duplo clique ou tecla F</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-3">Documentos</h4>
              <ul className="text-sm space-y-2">
                <li>• <strong>PDF:</strong> Visualização inline</li>
                <li>• <strong>Download:</strong> Download direto</li>
                <li>• <strong>Info:</strong> Tamanho e tipo do arquivo</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Media Viewer Component */}
      <MediaViewer
        mediaItems={mediaItems}
        currentIndex={currentIndex}
        isOpen={viewerOpen}
        onClose={() => setViewerOpen(false)}
        onIndexChange={setCurrentIndex}
      />
    </div>
  );
}