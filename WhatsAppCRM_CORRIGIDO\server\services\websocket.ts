import { WebSocketServer, WebSocket } from 'ws';
import { IncomingMessage } from 'http';
import { parse } from 'url';

interface AuthenticatedWebSocket extends WebSocket {
  userId?: number;
  isAlive?: boolean;
}

class WebSocketService {
  private wss?: WebSocketServer;
  private clients: Set<AuthenticatedWebSocket> = new Set();

  initialize(server: any) {
    this.wss = new WebSocketServer({ 
      server,
      path: '/ws'
    });

    this.wss.on('connection', (ws: AuthenticatedWebSocket, request: IncomingMessage) => {
      console.log('New WebSocket connection');
      
      // Parse query parameters for authentication
      const query = parse(request.url || '', true).query;
      const userId = query.userId ? parseInt(query.userId as string) : undefined;
      
      ws.userId = userId;
      ws.isAlive = true;
      this.clients.add(ws);

      // Handle ping/pong for connection health
      ws.on('pong', () => {
        ws.isAlive = true;
      });

      // Handle incoming messages
      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(ws, message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      });

      // Handle connection close
      ws.on('close', () => {
        console.log('WebSocket connection closed');
        this.clients.delete(ws);
      });

      // Handle errors
      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        this.clients.delete(ws);
      });

      // Send initial connection acknowledgment
      this.sendToClient(ws, 'connected', { 
        message: 'WebSocket connected successfully',
        userId: ws.userId 
      });
    });

    // Set up ping interval to check connection health
    setInterval(() => {
      this.wss?.clients.forEach((ws: AuthenticatedWebSocket) => {
        if (ws.isAlive === false) {
          ws.terminate();
          this.clients.delete(ws);
          return;
        }

        ws.isAlive = false;
        ws.ping();
      });
    }, 30000); // Check every 30 seconds

    console.log('WebSocket server initialized on /ws');
  }

  private handleMessage(ws: AuthenticatedWebSocket, message: any) {
    const { type, data } = message;

    switch (type) {
      case 'ping':
        this.sendToClient(ws, 'pong', { timestamp: Date.now() });
        break;

      case 'subscribe':
        // Subscribe to specific channels/topics
        if (data.channel) {
          // Could implement channel-based subscriptions here
          console.log(`Client ${ws.userId} subscribed to ${data.channel}`);
        }
        break;

      case 'unsubscribe':
        // Unsubscribe from channels/topics
        if (data.channel) {
          console.log(`Client ${ws.userId} unsubscribed from ${data.channel}`);
        }
        break;

      default:
        console.log('Unknown WebSocket message type:', type);
    }
  }

  private sendToClient(ws: AuthenticatedWebSocket, type: string, data: any) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({ type, data, timestamp: Date.now() }));
    }
  }

  // Broadcast to all connected clients
  broadcastToAll(type: string, data: any) {
    const message = JSON.stringify({ type, data, timestamp: Date.now() });
    
    this.clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }

  // Send to specific user
  sendToUser(userId: number, type: string, data: any) {
    const message = JSON.stringify({ type, data, timestamp: Date.now() });
    
    this.clients.forEach((client) => {
      if (client.userId === userId && client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }

  // Send to multiple users
  sendToUsers(userIds: number[], type: string, data: any) {
    const message = JSON.stringify({ type, data, timestamp: Date.now() });
    
    this.clients.forEach((client) => {
      if (client.userId && userIds.includes(client.userId) && client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }

  // Get connected clients count
  getConnectedClientsCount(): number {
    return this.clients.size;
  }

  // Get connected users
  getConnectedUsers(): number[] {
    const users: number[] = [];
    this.clients.forEach((client) => {
      if (client.userId) {
        users.push(client.userId);
      }
    });
    return [...new Set(users)]; // Remove duplicates
  }
}

export const webSocketService = new WebSocketService();

// Export convenience functions
export const broadcastToAll = (type: string, data: any) => {
  webSocketService.broadcastToAll(type, data);
};

export const sendToUser = (userId: number, type: string, data: any) => {
  webSocketService.sendToUser(userId, type, data);
};

export const sendToUsers = (userIds: number[], type: string, data: any) => {
  webSocketService.sendToUsers(userIds, type, data);
};
