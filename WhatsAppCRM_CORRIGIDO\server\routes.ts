import type { Express } from "express";
import { createServer } from "http";
import { setupVite, serveStatic, log } from "./vite";
import { webSocketService } from "./services/websocket";
import { multiUserWhatsAppService } from "./services/multiUserWhatsApp";
import { storage } from "./storage";
import { insertLeadSchema, insertChatSchema, insertMessageSchema, insertActivitySchema, insertKanbanTaskSchema } from "../shared/schema";

export async function registerRoutes(app: Express): Promise<any> {
  const server = createServer(app);

  // Initialize WebSocket
  webSocketService.initialize(server);

  // Health check
  app.get("/api/health", (_req, res) => {
    res.json({ status: "healthy", timestamp: new Date().toISOString() });
  });

  // WhatsApp API Routes (only real implemented functions)
  
  // Get all WhatsApp clients status
  app.get("/api/whatsapp/clients", (_req, res) => {
    try {
      const clients = multiUserWhatsAppService.getAllUsersStatus();
      res.json(clients);
    } catch (error) {
      console.error("Error fetching clients:", error);
      res.status(500).json({ error: "Failed to fetch clients" });
    }
  });

  // Get specific client status
  app.get("/api/whatsapp/status/:userId", (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const status = multiUserWhatsAppService.getUserStatus(userId);
      res.json(status);
    } catch (error) {
      console.error("Error fetching status:", error);
      res.status(500).json({ error: "Failed to fetch status" });
    }
  });

  // Initialize WhatsApp session
  app.post("/api/whatsapp/init/:userId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const result = await multiUserWhatsAppService.initializeUserSession(userId);
      res.json(result);
    } catch (error) {
      console.error("Error initializing session:", error);
      res.status(500).json({ error: "Failed to initialize session" });
    }
  });

  // Disconnect WhatsApp session
  app.delete("/api/whatsapp/disconnect/:userId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      await multiUserWhatsAppService.disconnectUser(userId);
      res.json({ success: true });
    } catch (error) {
      console.error("Error disconnecting:", error);
      res.status(500).json({ error: "Failed to disconnect" });
    }
  });

  // Get chats for user
  app.get("/api/whatsapp/chats/:userId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const chats = await multiUserWhatsAppService.getChats(userId);
      res.json(chats);
    } catch (error) {
      console.error("Error fetching chats:", error);
      res.status(500).json({ error: "Failed to fetch chats" });
    }
  });

  // Get messages for chat
  app.get("/api/whatsapp/messages/:userId/:chatId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const chatId = req.params.chatId;
      const limit = parseInt(req.query.limit as string) || 50;
      
      const messages = await multiUserWhatsAppService.getMessages(userId, chatId, limit);
      res.json(messages);
    } catch (error) {
      console.error("Error fetching messages:", error);
      res.status(500).json({ error: "Failed to fetch messages" });
    }
  });

  // Send text message
  app.post("/api/whatsapp/send-message/:userId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const { chatId, message } = req.body;
      
      const result = await multiUserWhatsAppService.sendTextMessage(userId, chatId, message);
      res.json(result);
    } catch (error) {
      console.error("Error sending message:", error);
      res.status(500).json({ error: "Failed to send message" });
    }
  });

  // Mark messages as read
  app.patch("/api/whatsapp/mark-read/:userId/:chatId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const chatId = req.params.chatId;
      
      const result = await multiUserWhatsAppService.markAsRead(userId, chatId);
      res.json(result);
    } catch (error) {
      console.error("Error marking as read:", error);
      res.status(500).json({ error: "Failed to mark as read" });
    }
  });

  // Send typing indicator
  app.post("/api/whatsapp/typing/:userId/:chatId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const chatId = req.params.chatId;
      
      const result = await multiUserWhatsAppService.sendTyping(userId, chatId);
      res.json(result);
    } catch (error) {
      console.error("Error sending typing:", error);
      res.status(500).json({ error: "Failed to send typing indicator" });
    }
  });

  // Get contact info
  app.get("/api/whatsapp/contact/:userId/:contactId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const contactId = req.params.contactId;
      
      const contact = await multiUserWhatsAppService.getContactInfo(userId, contactId);
      res.json(contact);
    } catch (error) {
      console.error("Error fetching contact:", error);
      res.status(500).json({ error: "Failed to fetch contact info" });
    }
  });

  // CRM Routes (Leads)
  
  // Get all leads
  app.get("/api/leads", async (req, res) => {
    try {
      const assignedTo = req.query.assignedTo ? parseInt(req.query.assignedTo as string) : undefined;
      const leads = await storage.getLeads(assignedTo);
      res.json(leads);
    } catch (error) {
      console.error("Error fetching leads:", error);
      res.status(500).json({ error: "Failed to fetch leads" });
    }
  });

  // Get lead by ID
  app.get("/api/leads/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const lead = await storage.getLead(id);
      if (!lead) {
        return res.status(404).json({ error: "Lead not found" });
      }
      res.json(lead);
    } catch (error) {
      console.error("Error fetching lead:", error);
      res.status(500).json({ error: "Failed to fetch lead" });
    }
  });

  // Create new lead
  app.post("/api/leads", async (req, res) => {
    try {
      const validatedData = insertLeadSchema.parse(req.body);
      const lead = await storage.createLead(validatedData);
      res.status(201).json(lead);
    } catch (error) {
      console.error("Error creating lead:", error);
      res.status(500).json({ error: "Failed to create lead" });
    }
  });

  // Update lead
  app.patch("/api/leads/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const validatedData = insertLeadSchema.partial().parse(req.body);
      const lead = await storage.updateLead(id, validatedData);
      if (!lead) {
        return res.status(404).json({ error: "Lead not found" });
      }
      res.json(lead);
    } catch (error) {
      console.error("Error updating lead:", error);
      res.status(500).json({ error: "Failed to update lead" });
    }
  });

  // Delete lead
  app.delete("/api/leads/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteLead(id);
      if (!success) {
        return res.status(404).json({ error: "Lead not found" });
      }
      res.json({ success: true });
    } catch (error) {
      console.error("Error deleting lead:", error);
      res.status(500).json({ error: "Failed to delete lead" });
    }
  });

  // Kanban Routes
  
  // Get all kanban tasks
  app.get("/api/kanban/tasks", async (req, res) => {
    try {
      const tasks = await storage.getKanbanTasks();
      res.json(tasks);
    } catch (error) {
      console.error("Error fetching kanban tasks:", error);
      res.status(500).json({ error: "Failed to fetch tasks" });
    }
  });

  // Get kanban task by ID
  app.get("/api/kanban/tasks/:id", async (req, res) => {
    try {
      const id = req.params.id;
      const task = await storage.getKanbanTask(id);
      if (!task) {
        return res.status(404).json({ error: "Task not found" });
      }
      res.json(task);
    } catch (error) {
      console.error("Error fetching kanban task:", error);
      res.status(500).json({ error: "Failed to fetch task" });
    }
  });

  // Create new kanban task
  app.post("/api/kanban/tasks", async (req, res) => {
    try {
      const taskId = `task_${Date.now()}`;
      const validatedData = insertKanbanTaskSchema.parse(req.body);
      
      const taskData = {
        ...validatedData,
        id: taskId,
        position: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const task = await storage.createKanbanTask(taskData);
      res.status(201).json(task);
    } catch (error) {
      console.error("Error creating kanban task:", error);
      res.status(500).json({ error: "Failed to create task" });
    }
  });

  // Update kanban task
  app.patch("/api/kanban/tasks/:id", async (req, res) => {
    try {
      const id = req.params.id;
      const validatedData = insertKanbanTaskSchema.partial().parse(req.body);
      
      const updatedTask = await storage.updateKanbanTask(id, validatedData);
      
      if (!updatedTask) {
        return res.status(404).json({ error: "Task not found" });
      }
      
      res.json(updatedTask);
    } catch (error) {
      console.error("Error updating kanban task:", error);
      res.status(500).json({ error: "Failed to update task" });
    }
  });

  // Delete kanban task
  app.delete("/api/kanban/tasks/:id", async (req, res) => {
    try {
      const id = req.params.id;
      const success = await storage.deleteKanbanTask(id);
      if (!success) {
        return res.status(404).json({ error: "Task not found" });
      }
      res.json({ success: true });
    } catch (error) {
      console.error("Error deleting kanban task:", error);
      res.status(500).json({ error: "Failed to delete task" });
    }
  });

  // Dashboard statistics
  app.get("/api/dashboard/stats", async (req, res) => {
    try {
      const stats = await storage.getDashboardStats();
      res.json(stats);
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
      res.status(500).json({ error: "Failed to fetch dashboard stats" });
    }
  });

  if (process.env.NODE_ENV === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  return server;
}