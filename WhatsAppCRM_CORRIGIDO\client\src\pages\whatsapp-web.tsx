import { useState, useRef, useEffect, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { 
  MessageSquare, ArrowLeft, Search, MoreVertical, Send, Mic, Smile, 
  Paperclip, Phone, VideoIcon, Archive, Pin, VolumeX, Trash2,
  Users, Star, Download, Reply, Forward, Check, CheckCheck, X,
  ZoomIn, ZoomOut, RotateCw, Maximize2, Play, Pause, Volume2,
  SkipBack, SkipForward, FileText, Image as ImageIcon
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useWebSocket } from "@/hooks/use-websocket";
import { apiRequest } from "@/lib/queryClient";
import { formatTime, formatDate, formatPhone, getInitials } from "@/lib/utils";
import { useLocation } from "wouter";
import { useWhatsApp } from "@/hooks/use-whatsapp";
import { SyncProgressModal } from "@/components/sync-progress-modal";

interface WhatsAppMessage {
  id: string;
  body: string;
  content?: string;
  timestamp: number;
  fromMe: boolean;
  from: string;
  to: string;
  ack?: number; // 0: pending, 1: sent, 2: delivered, 3: read
  type?: string;
  hasMedia?: boolean;
  mediaType?: string;
  mediaUrl?: string;
  mediaName?: string;
  mediaMimeType?: string;
  isStarred?: boolean;
  isForwarded?: boolean;
  mentionedIds?: string[];
  quotedMsgId?: string;
  authorName?: string;
  authorNumber?: string;
  location?: {
    latitude: number;
    longitude: number;
    description?: string;
  };
}

interface WhatsAppChat {
  id: string;
  name: string;
  phone: string;
  lastMessage?: {
    body: string;
    content?: string;
    timestamp: number;
    fromMe: boolean;
  };
  unreadCount: number;
  isOnline: boolean;
  isGroup: boolean;
  isMuted?: boolean;
  isArchived?: boolean;
  isPinned?: boolean;
  profilePicUrl?: string;
  timestamp: number;
  groupMetadata?: {
    participants: number;
    desc: string;
    createdAt: Date | null;
    admins: number;
  };
  // WhatsApp Web features
  isContactSaved?: boolean;
  contactInfo?: {
    name: string;
    pushname: string;
    isMyContact: boolean;
    isUser: boolean;
    isWAContact: boolean;
  };
}

export default function WhatsAppWeb() {
  const [selectedChat, setSelectedChat] = useState<string>("");
  const [messageInput, setMessageInput] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showChatInfo, setShowChatInfo] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showMoreOptions, setShowMoreOptions] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState<{
    current: number;
    total: number;
    percentage: number;
    message: string;
    completed: boolean;
  } | null>(null);
  const [showMediaModal, setShowMediaModal] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<any>(null);
  const [mediaCaption, setMediaCaption] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [globalSearch, setGlobalSearch] = useState("");
  const [showGlobalSearch, setShowGlobalSearch] = useState(false);
  const [pinnedChats, setPinnedChats] = useState<Set<string>>(new Set());
  const [unreadChats, setUnreadChats] = useState<Set<string>>(new Set());
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  
  // Performance optimization states
  const [messagePage, setMessagePage] = useState(1);
  const [messageCache, setMessageCache] = useState<Map<string, WhatsAppMessage[]>>(new Map());
  const [isLoadingOlderMessages, setIsLoadingOlderMessages] = useState(false);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);
  const [lastMessageId, setLastMessageId] = useState<string>("");
  
  // Synchronization states
  const [showSyncModal, setShowSyncModal] = useState(false);
  const [syncStatus, setSyncStatus] = useState<{
    chatId?: string;
    status: 'pending' | 'synced' | 'failed';
    syncedAt?: string;
    messageCount?: number;
  }>({ status: 'pending' });
  
  // Media viewer states
  const [showMediaViewer, setShowMediaViewer] = useState(false);
  const [currentMedia, setCurrentMedia] = useState<{
    type: 'image' | 'video' | 'audio' | 'document';
    url: string;
    name?: string;
    mimeType?: string;
  } | null>(null);
  const [imageZoom, setImageZoom] = useState(1);
  const [imageRotation, setImageRotation] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const audioRecorderRef = useRef<MediaRecorder | null>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { isConnected, phoneNumber } = useWhatsApp();
  const [, setLocation] = useLocation();

  // Get current user ID
  const currentUserId = typeof window !== 'undefined' ? localStorage.getItem('userId') || '' : '';

  // Fetch chats from WhatsApp Web
  const { data: chats = [], isLoading: chatsLoading, refetch: refetchChats } = useQuery({
    queryKey: ["/api/whatsapp/chats", currentUserId],
    queryFn: async () => {
      if (!currentUserId || !isConnected) return [];
      
      // Show initial loading progress
      setLoadingProgress({
        current: 0,
        total: 75, // Estimated based on your data
        percentage: 0,
        message: "Conectando ao WhatsApp...",
        completed: false
      });
      
      const response = await fetch(`/api/whatsapp/chats?userId=${encodeURIComponent(currentUserId)}`);
      if (!response.ok) throw new Error('Failed to fetch chats');
      const result = await response.json();
      
      // Update progress to show initial chats loaded
      if (result.length > 0 && loadingProgress && !loadingProgress.completed) {
        setLoadingProgress({
          current: result.length,
          total: Math.max(result.length, 75),
          percentage: Math.round((result.length / Math.max(result.length, 75)) * 100),
          message: `Carregadas ${result.length} conversas`,
          completed: false
        });
      }
      
      return result;
    },
    enabled: !!currentUserId && isConnected,
    refetchInterval: 15000, // Refresh every 15 seconds to get background updates
  });

  // Fetch messages for selected chat with proper paginated structure handling
  const { data: messagesData, isLoading: messagesLoading } = useQuery({
    queryKey: ["/api/whatsapp/messages", currentUserId, selectedChat],
    queryFn: async () => {
      if (!currentUserId || !selectedChat) return { data: [], total: 0, hasMore: false };
      
      const response = await fetch(`/api/whatsapp/messages?userId=${encodeURIComponent(currentUserId)}&chatId=${encodeURIComponent(selectedChat)}&limit=50&page=1`);
      if (!response.ok) throw new Error('Failed to fetch messages');
      const result = await response.json();
      
      // Normalize response to paginated structure
      if (Array.isArray(result)) {
        const normalized = { data: result, total: result.length, hasMore: result.length === 50 };
        setHasMoreMessages(normalized.hasMore);
        setMessagePage(1);
        return normalized;
      }
      
      const normalized = {
        data: result.data || result.messages || [],
        total: result.total || 0,
        hasMore: result.hasMore || false
      };
      
      setHasMoreMessages(normalized.hasMore);
      setMessagePage(1);
      return normalized;
    },
    enabled: !!currentUserId && !!selectedChat && isConnected,
    staleTime: 30000, // Cache for 30 seconds
  });

  // Extract messages array from paginated structure with proper fallback
  const messages = Array.isArray(messagesData?.data) ? messagesData.data : 
                   Array.isArray(messagesData) ? messagesData : [];

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: async ({ chatId, message }: { chatId: string; message: string }) => {
      const cleanPhone = chatId.replace('@c.us', '').replace('@g.us', '');
      const response = await fetch(`/api/whatsapp/send?userId=${encodeURIComponent(currentUserId)}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ clientPhone: cleanPhone, message })
      });
      if (!response.ok) throw new Error('Failed to send message');
      return response.json();
    },
    onSuccess: () => {
      setMessageInput("");
      setAutoScrollEnabled(true);
      queryClient.invalidateQueries({ queryKey: ["/api/whatsapp/messages"] });
      queryClient.invalidateQueries({ queryKey: ["/api/whatsapp/chats"] });
      // Force scroll to bottom after sending message
      setTimeout(() => scrollToBottom(true), 200);
    },
    onError: (error) => {
      toast({
        title: "Erro ao enviar mensagem",
        description: "Não foi possível enviar a mensagem. Tente novamente.",
        variant: "destructive"
      });
    }
  });

  // Save contact mutation - core WhatsApp Web feature
  const saveContactMutation = useMutation({
    mutationFn: async ({ contactPhone, contactName }: { contactPhone: string; contactName: string }) => {
      const response = await fetch(`/api/whatsapp/contacts/save`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: currentUserId,
          contactPhone,
          contactName
        })
      });
      if (!response.ok) throw new Error('Failed to save contact');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/whatsapp/chats", currentUserId] });
      toast({
        title: "Contato salvo",
        description: "Contato adicionado com sucesso",
      });
      setShowChatInfo(false);
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao salvar contato",
        description: error.message || "Não foi possível salvar o contato",
        variant: "destructive",
      });
    }
  });

  // Mutation to send media
  const sendMediaMutation = useMutation({
    mutationFn: async ({ file, caption, chat }: { file: File; caption: string; chat: string }) => {
      const formData = new FormData();
      formData.append('media', file);
      formData.append('userId', currentUserId);
      formData.append('clientPhone', chat.replace('@c.us', '').replace('@g.us', ''));
      formData.append('caption', caption);

      const response = await fetch('/api/whatsapp/send-media', {
        method: 'POST',
        body: formData
      });
      if (!response.ok) throw new Error('Failed to send media');
      return response.json();
    },
    onSuccess: () => {
      setShowMediaModal(false);
      setSelectedMedia(null);
      setMediaCaption("");
      queryClient.invalidateQueries({ queryKey: ["/api/whatsapp/messages"] });
      queryClient.invalidateQueries({ queryKey: ["/api/whatsapp/chats"] });
      toast({
        title: "Mídia enviada",
        description: "Arquivo enviado com sucesso",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao enviar mídia",
        description: error.message || "Não foi possível enviar o arquivo",
        variant: "destructive",
      });
    }
  });

  // Mutation to delete message
  const deleteMessageMutation = useMutation({
    mutationFn: async ({ messageId }: { messageId: string }) => {
      const response = await fetch(`/api/whatsapp/delete-message`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: currentUserId,
          messageId
        })
      });
      if (!response.ok) throw new Error('Failed to delete message');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/whatsapp/messages"] });
      toast({
        title: "Mensagem excluída",
        description: "A mensagem foi removida",
      });
    }
  });

  // Mutation to forward message
  const forwardMessageMutation = useMutation({
    mutationFn: async ({ messageId, targetChats }: { messageId: string; targetChats: string[] }) => {
      const response = await fetch(`/api/whatsapp/forward-message`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: currentUserId,
          messageId,
          targetChats: targetChats.map(chat => chat.replace('@c.us', '').replace('@g.us', ''))
        })
      });
      if (!response.ok) throw new Error('Failed to forward message');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/whatsapp/messages"] });
      toast({
        title: "Mensagem encaminhada",
        description: "A mensagem foi encaminhada com sucesso",
      });
    }
  });

  // Mutation to pin/unpin chat
  const pinChatMutation = useMutation({
    mutationFn: async ({ chatId, pinned }: { chatId: string; pinned: boolean }) => {
      if (pinned) {
        setPinnedChats(prev => new Set([...prev, chatId]));
      } else {
        setPinnedChats(prev => {
          const newSet = new Set(prev);
          newSet.delete(chatId);
          return newSet;
        });
      }
      return { chatId, pinned };
    },
    onSuccess: ({ chatId, pinned }) => {
      toast({
        title: pinned ? "Conversa fixada" : "Conversa desfixada",
        description: pinned ? "A conversa foi fixada no topo" : "A conversa foi removida dos fixados",
      });
    }
  });

  // Mutation to mark as unread
  const markUnreadMutation = useMutation({
    mutationFn: async ({ chatId }: { chatId: string }) => {
      setUnreadChats(prev => new Set([...prev, chatId]));
      return { chatId };
    },
    onSuccess: () => {
      toast({
        title: "Marcado como não lida",
        description: "A conversa foi marcada como não lida",
      });
    }
  });

  // Real-time progress monitoring with enhanced feedback
  useEffect(() => {
    let progressInterval: NodeJS.Timeout;
    let simulatedProgress = 0;
    
    if (chatsLoading && currentUserId && isConnected) {
      setLoadingProgress({
        current: 0,
        total: 100, // Dynamic - will be updated with real count
        percentage: 0,
        message: "Conectando ao WhatsApp...",
        completed: false
      });

      // Monitor real progress from backend with enhanced simulation
      progressInterval = setInterval(async () => {
        try {
          const response = await fetch(`/api/whatsapp/progress?userId=${encodeURIComponent(currentUserId)}`);
          if (response.ok) {
            const progressData = await response.json();
            
            if (progressData.status === 'loading' || progressData.status === 'completed') {
              setLoadingProgress({
                current: progressData.current,
                total: progressData.total,
                percentage: progressData.percentage,
                message: progressData.message,
                completed: progressData.status === 'completed'
              });
            } else if (progressData.status === 'not_connected') {
              // Show connection establishment progress
              simulatedProgress = Math.min(simulatedProgress + 1, 10);
              setLoadingProgress({
                current: simulatedProgress,
                total: 100,
                percentage: Math.round((simulatedProgress / 100) * 100),
                message: `Conectando ao WhatsApp... aguarde`,
                completed: false
              });
            } else if (progressData.status === 'connecting') {
              // Enhanced connecting state
              simulatedProgress = Math.min(simulatedProgress + 2, 20);
              setLoadingProgress({
                current: simulatedProgress,
                total: 100,
                percentage: Math.round((simulatedProgress / 100) * 100),
                message: `Estabelecendo conexão segura...`,
                completed: false
              });
            }
          }
        } catch (error) {
          // Enhanced fallback progress with realistic increments
          simulatedProgress = Math.min(simulatedProgress + Math.random() * 2 + 1, 90);
          const percentage = Math.round((simulatedProgress / 100) * 100);
          setLoadingProgress({
            current: Math.floor(simulatedProgress),
            total: 100,
            percentage,
            message: `Carregando todas as conversas da sua conta... ${percentage}%`,
            completed: false
          });
        }
      }, 1000);
    }
    
    return () => {
      if (progressInterval) {
        clearInterval(progressInterval);
      }
    };
  }, [chatsLoading, currentUserId, isConnected]);

  // Load older messages function
  const loadOlderMessages = useCallback(async () => {
    if (isLoadingOlderMessages || !hasMoreMessages || !selectedChat) return;
    
    setIsLoadingOlderMessages(true);
    try {
      const nextPage = messagePage + 1;
      const response = await fetch(`/api/whatsapp/messages?userId=${encodeURIComponent(currentUserId)}&chatId=${encodeURIComponent(selectedChat)}&limit=50&page=${nextPage}`);
      if (!response.ok) throw new Error('Failed to fetch older messages');
      
      const responseData = await response.json();
      
      // Handle both old format (array) and new format (object with pagination)
      const olderMessages = Array.isArray(responseData) ? responseData : responseData.messages || [];
      const hasMore = Array.isArray(responseData) ? olderMessages.length === 50 : responseData.hasMore;
      
      setHasMoreMessages(hasMore);
      
      // Update cache with older messages
      const cacheKey = `${selectedChat}-${nextPage}`;
      setMessageCache(prev => new Map(prev.set(cacheKey, olderMessages)));
      setMessagePage(nextPage);
      
      // Prepend older messages to current paginated data
      queryClient.setQueryData(["/api/whatsapp/messages", currentUserId, selectedChat], (oldData: any) => {
        const currentData = oldData?.data || oldData || [];
        const updatedData = [...olderMessages.reverse(), ...currentData];
        
        // Return paginated structure
        return {
          data: updatedData,
          total: updatedData.length,
          hasMore: olderMessages.length === 50
        };
      });
      
    } catch (error) {
      console.error('Error loading older messages:', error);
    } finally {
      setIsLoadingOlderMessages(false);
    }
  }, [currentUserId, selectedChat, messagePage, isLoadingOlderMessages, hasMoreMessages, queryClient, messageCache]);

  // Auto-scroll management with debouncing
  const scrollToBottom = useCallback((force = false) => {
    if (!autoScrollEnabled && !force) return;
    
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
    
    scrollTimeoutRef.current = setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ 
        behavior: 'smooth',
        block: 'end'
      });
    }, 100);
  }, [autoScrollEnabled]);

  // Handle scroll events to detect if user scrolled up
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const container = e.currentTarget;
    const { scrollTop, scrollHeight, clientHeight } = container;
    
    // Check if user is at bottom (within 100px)
    const isAtBottom = scrollHeight - scrollTop - clientHeight < 100;
    setAutoScrollEnabled(isAtBottom);
    
    // Load more messages when scrolled to top
    if (scrollTop < 100 && hasMoreMessages && !isLoadingOlderMessages) {
      loadOlderMessages();
    }
  }, [hasMoreMessages, isLoadingOlderMessages, loadOlderMessages]);

  // Reset state when changing chats
  useEffect(() => {
    if (selectedChat) {
      setMessagePage(1);
      setHasMoreMessages(true);
      setAutoScrollEnabled(true);
      setMessageCache(new Map()); // Clear cache for memory management
    }
  }, [selectedChat]);

  // Auto-scroll on new messages
  useEffect(() => {
    if (messages.length > 0 && autoScrollEnabled) {
      const latestMessage = messages[messages.length - 1];
      if (latestMessage && latestMessage.id !== lastMessageId) {
        setLastMessageId(latestMessage.id);
        scrollToBottom();
      }
    }
  }, [messages, autoScrollEnabled, lastMessageId, scrollToBottom]);

  // Memory cleanup for large message caches
  useEffect(() => {
    const cleanup = () => {
      if (messageCache.size > 20) { // Keep only last 20 pages in memory
        const entries = Array.from(messageCache.entries());
        const toKeep = entries.slice(-15); // Keep last 15 pages
        setMessageCache(new Map(toKeep));
      }
    };
    
    const interval = setInterval(cleanup, 30000); // Cleanup every 30 seconds
    return () => clearInterval(interval);
  }, [messageCache]);

  // Cleanup scroll timeouts on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // Media viewer functions
  const openMediaViewer = useCallback((mediaUrl: string, mediaType: string, mediaName?: string, mimeType?: string) => {
    const type = mediaType?.includes('image') ? 'image' : 
                 mediaType?.includes('video') ? 'video' :
                 mediaType?.includes('audio') ? 'audio' : 'document';
    
    setCurrentMedia({
      type,
      url: mediaUrl,
      name: mediaName,
      mimeType
    });
    setShowMediaViewer(true);
    setImageZoom(1);
    setImageRotation(0);
    setCurrentTime(0);
    setIsPlaying(false);
  }, []);

  const closeMediaViewer = useCallback(() => {
    setShowMediaViewer(false);
    setCurrentMedia(null);
    setImageZoom(1);
    setImageRotation(0);
    setIsPlaying(false);
    if (videoRef.current) {
      videoRef.current.pause();
    }
    if (audioRef.current) {
      audioRef.current.pause();
    }
  }, []);

  const handleZoomIn = useCallback(() => {
    setImageZoom(prev => Math.min(prev + 0.25, 3));
  }, []);

  const handleZoomOut = useCallback(() => {
    setImageZoom(prev => Math.max(prev - 0.25, 0.25));
  }, []);

  const handleRotate = useCallback(() => {
    setImageRotation(prev => (prev + 90) % 360);
  }, []);

  const handlePlayPause = useCallback(() => {
    if (currentMedia?.type === 'video' && videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    } else if (currentMedia?.type === 'audio' && audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  }, [currentMedia, isPlaying]);

  const handleSeek = useCallback((time: number) => {
    if (currentMedia?.type === 'video' && videoRef.current) {
      videoRef.current.currentTime = time;
      setCurrentTime(time);
    } else if (currentMedia?.type === 'audio' && audioRef.current) {
      audioRef.current.currentTime = time;
      setCurrentTime(time);
    }
  }, [currentMedia]);

  const handleVolumeChange = useCallback((newVolume: number) => {
    setVolume(newVolume);
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
    }
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
    }
  }, []);

  const formatDuration = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  // Complete progress when chats finish loading
  useEffect(() => {
    if (!chatsLoading && chats.length > 0 && loadingProgress && !loadingProgress.completed) {
      setLoadingProgress({
        current: chats.length,
        total: chats.length,
        percentage: 100,
        message: `✓ Todas as ${chats.length} conversas carregadas com fotos de perfil`,
        completed: true
      });
      
      // Hide progress bar immediately after completion
      setTimeout(() => setLoadingProgress(null), 1000);
    }
  }, [chatsLoading, chats.length, loadingProgress]);

  // Load more messages function for incremental loading
  const loadMoreMessages = useCallback(async () => {
    if (isLoadingOlderMessages || !hasMoreMessages || !selectedChat) return;
    
    setIsLoadingOlderMessages(true);
    try {
      const nextPage = messagePage + 1;
      setMessagePage(nextPage);
      
      // Trigger query refetch with new page
      await queryClient.invalidateQueries({ 
        queryKey: ["/api/whatsapp/messages", currentUserId, selectedChat, nextPage] 
      });
    } catch (error) {
      console.error('Error loading more messages:', error);
      toast({
        title: "Erro ao carregar mensagens",
        description: "Não foi possível carregar mensagens anteriores.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingOlderMessages(false);
    }
  }, [isLoadingOlderMessages, hasMoreMessages, selectedChat, messagePage, currentUserId, queryClient, toast]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // Focus input when chat is selected
  useEffect(() => {
    if (selectedChat && inputRef.current) {
      inputRef.current.focus();
      // Reset pagination state for new chat
      setMessagePage(1);
      setHasMoreMessages(true);
      setLastMessageId("");
    }
  }, [selectedChat]);

  // Listen for sync progress events via WebSocket
  const { isConnected: wsConnected, lastMessage } = useWebSocket(`ws://${window.location.host}/ws`, {
    onMessage: (message) => {
      if (message.type === 'sync_progress' && message.userId === currentUserId) {
        setShowSyncModal(true);
      }
    }
  });
  
  useEffect(() => {
    if (!lastMessage || !currentUserId) return;
    
    // Listen for sync start events
    if (lastMessage.type === 'sync_progress' && lastMessage.userId === currentUserId) {
      setShowSyncModal(true);
    }
    
    // Listen for connection events that trigger sync
    if (lastMessage.type === 'user_connected' && lastMessage.userId === currentUserId) {
      // Show sync modal when user connects (sync will start automatically)
      setTimeout(() => setShowSyncModal(true), 1000);
    }
  }, [lastMessage, currentUserId]);

  // Audio recording functionality
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      audioRecorderRef.current = mediaRecorder;
      
      const chunks: BlobPart[] = [];
      mediaRecorder.ondataavailable = (event) => chunks.push(event.data);
      
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(chunks, { type: 'audio/wav' });
        const audioFile = new File([audioBlob], `audio_${Date.now()}.wav`, { type: 'audio/wav' });
        
        if (selectedChat) {
          sendMediaMutation.mutate({
            file: audioFile,
            caption: '',
            chat: selectedChat
          });
        }
        
        stream.getTracks().forEach(track => track.stop());
        setIsRecording(false);
        setRecordingTime(0);
      };
      
      mediaRecorder.start();
      setIsRecording(true);
      
      // Start recording timer
      const timer = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
      
      // Auto-stop after 5 minutes
      setTimeout(() => {
        if (audioRecorderRef.current && audioRecorderRef.current.state === 'recording') {
          audioRecorderRef.current.stop();
          clearInterval(timer);
        }
      }, 300000);
      
    } catch (error) {
      toast({
        title: "Erro na gravação",
        description: "Não foi possível acessar o microfone",
        variant: "destructive"
      });
    }
  };

  const stopRecording = () => {
    if (audioRecorderRef.current && audioRecorderRef.current.state === 'recording') {
      audioRecorderRef.current.stop();
    }
  };

  // File handling
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Check file size (max 100MB)
      if (file.size > 100 * 1024 * 1024) {
        toast({
          title: "Arquivo muito grande",
          description: "O arquivo deve ter no máximo 100MB",
          variant: "destructive"
        });
        return;
      }
      
      setSelectedMedia({
        file,
        preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : null,
        type: file.type.split('/')[0],
        name: file.name,
        size: file.size
      });
      setShowMediaModal(true);
    }
  };

  // Global search functionality
  const handleGlobalSearch = (query: string) => {
    setGlobalSearch(query);
    if (query.trim()) {
      setShowGlobalSearch(true);
    } else {
      setShowGlobalSearch(false);
    }
  };

  // Filter chats based on search and other criteria
  const getFilteredChats = () => {
    return chats.filter((chat: WhatsAppChat) => {
      const matchesSearch = !searchQuery || 
        chat.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        chat.phone.includes(searchQuery) ||
        chat.lastMessage?.body?.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesGlobalSearch = !globalSearch || 
        chat.name.toLowerCase().includes(globalSearch.toLowerCase()) ||
        chat.phone.includes(globalSearch) ||
        chat.lastMessage?.body?.toLowerCase().includes(globalSearch.toLowerCase());
      
      return matchesSearch && matchesGlobalSearch;
    }).sort((a: WhatsAppChat, b: WhatsAppChat) => {
      // Sort by pinned status first, then by timestamp
      const aPinned = pinnedChats.has(a.id);
      const bPinned = pinnedChats.has(b.id);
      
      if (aPinned && !bPinned) return -1;
      if (!aPinned && bPinned) return 1;
      
      return b.timestamp - a.timestamp;
    });
  };

  const filteredChats = getFilteredChats();

  // Listen for typing indicators via WebSocket
  useEffect(() => {
    if (!lastMessage) return;
    
    if (lastMessage.type === 'typing_start' && lastMessage.chatId) {
      setTypingUsers(prev => new Set(Array.from(prev).concat(lastMessage.chatId)));
    }
    
    if (lastMessage.type === 'typing_stop' && lastMessage.chatId) {
      setTypingUsers(prev => {
        const newSet = new Set(Array.from(prev));
        newSet.delete(lastMessage.chatId);
        return newSet;
      });
    }
  }, [lastMessage]);

  // Send typing indicator
  const sendTypingIndicator = (isTyping: boolean) => {
    if (selectedChat) {
      // Send typing status via WebSocket or API
      fetch('/api/whatsapp/typing', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: currentUserId,
          chatId: selectedChat,
          isTyping
        })
      }).catch(() => {}); // Silent fail
    }
  };

  // Handle input changes for typing indicator
  const handleInputChange = (value: string) => {
    setMessageInput(value);
    
    if (value.trim() && !isTyping) {
      setIsTyping(true);
      sendTypingIndicator(true);
    } else if (!value.trim() && isTyping) {
      setIsTyping(false);
      sendTypingIndicator(false);
    }
  };

  // Close modals when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      
      // Close emoji picker if clicking outside
      if (showEmojiPicker && !target.closest('.emoji-picker-container')) {
        setShowEmojiPicker(false);
      }
      
      // Close more options if clicking outside
      if (showMoreOptions && !target.closest('.more-options-container')) {
        setShowMoreOptions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showEmojiPicker, showMoreOptions]);

  const handleSendMessage = useCallback(() => {
    if (!messageInput.trim() || !selectedChat || sendMessageMutation.isPending) return;
    
    sendMessageMutation.mutate({
      chatId: selectedChat,
      message: messageInput.trim()
    });
  }, [messageInput, selectedChat, sendMessageMutation]);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Remove duplicate filteredChats - already implemented above with full functionality

  // Get selected chat details
  const selectedChatData = chats.find((chat: WhatsAppChat) => chat.id === selectedChat);

  const formatMessageTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) {
      return date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
    } else if (diffInDays === 1) {
      return 'Ontem';
    } else if (diffInDays < 7) {
      return date.toLocaleDateString('pt-BR', { weekday: 'long' });
    } else {
      return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit', year: 'numeric' });
    }
  };

  const getMessageStatusIcon = (message: WhatsAppMessage) => {
    if (!message.fromMe) return null;
    
    switch (message.ack) {
      case 0: return <Check className="w-4 h-4 text-gray-400" />;
      case 1: return <Check className="w-4 h-4 text-gray-500" />;
      case 2: return <CheckCheck className="w-4 h-4 text-gray-500" />;
      case 3: return <CheckCheck className="w-4 h-4 text-blue-500" />;
      default: return <Check className="w-4 h-4 text-gray-400" />;
    }
  };

  if (!isConnected) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50">
        <div className="text-center">
          <MessageSquare className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">WhatsApp Web</h2>
          <p className="text-gray-500">Conecte seu WhatsApp para começar</p>
          <button 
            onClick={() => setLocation('/qr-code')}
            className="mt-4 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            Conectar WhatsApp
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar - Chat List */}
      <div className="w-1/3 bg-white border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-3">
              <button 
                onClick={() => setLocation('/')}
                className="p-2 text-gray-600 hover:bg-gray-200 rounded-full transition-colors"
                title="Voltar ao menu principal"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center text-white font-semibold">
                {getInitials(phoneNumber || 'WA')}
              </div>
              <div>
                <h3 className="font-semibold text-gray-800">WhatsApp Web</h3>
                <p className="text-sm text-gray-500">{phoneNumber}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button 
                onClick={(e) => {
                  e.preventDefault();
                  refetchChats();
                }}
                className="p-2 text-gray-600 hover:bg-gray-200 rounded-full transition-colors"
                title="Atualizar conversas"
              >
                <Search className="w-5 h-5" />
              </button>
              <button className="p-2 text-gray-600 hover:bg-gray-200 rounded-full transition-colors">
                <MoreVertical className="w-5 h-5" />
              </button>
            </div>
          </div>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Pesquisar ou começar uma nova conversa"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-100 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
            />
          </div>
        </div>

        {/* Loading Progress Bar */}
        {loadingProgress && !loadingProgress.completed && (
          <div className="p-4 bg-green-50 border-b border-green-200">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-green-800">
                {loadingProgress.message}
              </span>
              <span className="text-sm text-green-600">
                {loadingProgress.percentage}%
              </span>
            </div>
            <div className="w-full bg-green-200 rounded-full h-2">
              <div 
                className="bg-green-600 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${loadingProgress.percentage}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Completion notification */}
        {loadingProgress && loadingProgress.completed && (
          <div className="p-3 bg-green-100 border-b border-green-200 text-center relative">
            <span className="text-sm font-medium text-green-800">
              ✓ Todas as {loadingProgress.total} conversas carregadas com fotos de perfil
            </span>
            <button
              onClick={() => setLoadingProgress(null)}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-green-600 hover:text-green-800 text-lg"
              title="Fechar"
            >
              ×
            </button>
          </div>
        )}

        {/* Chat List */}
        <div className="flex-1 overflow-y-auto">
          {chatsLoading ? (
            <div className="p-4 text-center text-gray-500">
              <div className="animate-pulse">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                  </div>
                </div>
                Carregando conversas do WhatsApp...
              </div>
            </div>
          ) : filteredChats.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              {searchQuery ? 'Nenhuma conversa encontrada' : 'Nenhuma conversa ainda'}
            </div>
          ) : (
            filteredChats.map((chat: WhatsAppChat) => (
              <div
                key={chat.id}
                onClick={() => setSelectedChat(chat.id)}
                className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                  selectedChat === chat.id ? 'bg-green-50 border-r-4 border-r-green-500' : ''
                }`}
              >
                <div className="flex items-center space-x-3">
                  {/* Profile Picture */}
                  <div className="relative">
                    {chat.profilePicUrl ? (
                      <img 
                        src={chat.profilePicUrl} 
                        alt={chat.name}
                        className="w-12 h-12 rounded-full object-cover"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                          e.currentTarget.nextElementSibling?.classList.remove('hidden');
                        }}
                      />
                    ) : null}
                    <div className={`w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 font-semibold ${chat.profilePicUrl ? 'hidden' : ''}`}>
                      {chat.isGroup ? <Users className="w-6 h-6" /> : getInitials(chat.name)}
                    </div>
                    {chat.isOnline && !chat.isGroup && (
                      <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                    )}
                  </div>
                  
                  {/* Chat Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-1">
                        <h4 className="font-semibold text-gray-800 truncate">{chat.name}</h4>
                        {chat.isPinned && <Pin className="w-4 h-4 text-gray-500" />}
                        {chat.isMuted && <VolumeX className="w-4 h-4 text-gray-500" />}
                        {chat.isGroup && <Users className="w-4 h-4 text-gray-500" />}
                      </div>
                      <span className="text-xs text-gray-500">
                        {chat.lastMessage && formatMessageTime(chat.lastMessage.timestamp)}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between mt-1">
                      <p className="text-sm text-gray-600 truncate">
                        {chat.lastMessage ? (
                          <>
                            {chat.lastMessage.fromMe && <span className="text-green-600">Você: </span>}
                            {chat.lastMessage.body}
                          </>
                        ) : (
                          <span className="text-gray-400">Nenhuma mensagem</span>
                        )}
                      </p>
                      {chat.unreadCount > 0 && (
                        <div className="bg-green-600 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                          {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {selectedChat ? (
          <>
            {/* Chat Header */}
            <div className="p-4 bg-gray-50 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {/* Profile Picture */}
                  <div className="relative">
                    {selectedChatData?.profilePicUrl ? (
                      <img 
                        src={selectedChatData.profilePicUrl} 
                        alt={selectedChatData.name}
                        className="w-10 h-10 rounded-full object-cover"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                          e.currentTarget.nextElementSibling?.classList.remove('hidden');
                        }}
                      />
                    ) : null}
                    <div className={`w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 font-semibold ${selectedChatData?.profilePicUrl ? 'hidden' : ''}`}>
                      {selectedChatData?.isGroup ? <Users className="w-6 h-6" /> : getInitials(selectedChatData?.name || '')}
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold text-gray-800">{selectedChatData?.name}</h3>
                    <p className="text-sm text-gray-500">
                      {selectedChatData?.isGroup 
                        ? `${selectedChatData.groupMetadata?.participants || 0} participantes`
                        : selectedChatData?.isOnline ? 'online' : `${selectedChatData?.phone}`
                      }
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button 
                    onClick={() => {
                      const phone = selectedChatData?.phone?.replace(/\D/g, '');
                      if (phone) {
                        window.open(`tel:${phone}`, '_self');
                      }
                    }}
                    className="p-2 text-gray-600 hover:bg-gray-200 rounded-full transition-colors"
                    title="Fazer chamada"
                  >
                    <Phone className="w-5 h-5" />
                  </button>
                  <button 
                    onClick={() => {
                      const phone = selectedChatData?.phone?.replace(/\D/g, '');
                      if (phone) {
                        window.open(`https://api.whatsapp.com/send?phone=${phone}&text=Vamos%20fazer%20uma%20videochamada!`, '_blank');
                      }
                    }}
                    className="p-2 text-gray-600 hover:bg-gray-200 rounded-full transition-colors"
                    title="Videochamada"
                  >
                    <VideoIcon className="w-5 h-5" />
                  </button>
                  <button 
                    onClick={() => setShowChatInfo(!showChatInfo)}
                    className="p-2 text-gray-600 hover:bg-gray-200 rounded-full transition-colors"
                    title="Informações do contato"
                  >
                    <MoreVertical className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div 
              ref={messagesContainerRef}
              onScroll={handleScroll}
              className="flex-1 overflow-y-auto p-4 bg-[#e5ddd5] bg-opacity-50"
            >
              {/* Load older messages indicator */}
              {isLoadingOlderMessages && (
                <div className="text-center text-gray-500 py-4">
                  <div className="inline-flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-500"></div>
                    <span className="text-sm">Carregando mensagens anteriores...</span>
                  </div>
                </div>
              )}
              
              {messagesLoading ? (
                <div className="text-center text-gray-500 py-8">
                  <div className="inline-flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500"></div>
                    <span>Carregando mensagens...</span>
                  </div>
                </div>
              ) : messages.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  Nenhuma mensagem ainda. Envie a primeira!
                </div>
              ) : (
                <div className="space-y-2">
                  {messages.map((message: WhatsAppMessage) => (
                    <div
                      key={message.id}
                      className={`flex ${message.fromMe ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                          message.fromMe
                            ? 'bg-green-500 text-white'
                            : 'bg-white text-gray-800'
                        }`}
                      >
                        {/* Group message author */}
                        {selectedChatData?.isGroup && !message.fromMe && (
                          <p className="text-xs font-semibold text-green-600 mb-1">
                            {message.authorName || message.authorNumber}
                          </p>
                        )}
                        
                        {/* Forwarded indicator */}
                        {message.isForwarded && (
                          <p className="text-xs text-gray-500 mb-1 flex items-center">
                            <Forward className="w-3 h-3 mr-1" />
                            Encaminhada
                          </p>
                        )}
                        
                        {/* Media content */}
                        {message.hasMedia && message.mediaUrl && (
                          <div className="mb-2">
                            {message.mediaType === 'image' ? (
                              <div 
                                className="relative group cursor-pointer"
                                onClick={() => openMediaViewer(message.mediaUrl!, message.mediaType!, message.mediaName, message.mediaMimeType)}
                              >
                                <img 
                                  src={message.mediaUrl} 
                                  alt="Imagem"
                                  className="max-w-full rounded-lg transition-transform hover:scale-105"
                                />
                                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all flex items-center justify-center">
                                  <Maximize2 className="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                                </div>
                              </div>
                            ) : message.mediaType === 'sticker' ? (
                              <div 
                                className="relative group cursor-pointer"
                                onClick={() => openMediaViewer(message.mediaUrl!, message.mediaType!, message.mediaName, message.mediaMimeType)}
                              >
                                <img 
                                  src={message.mediaUrl} 
                                  alt="Figurinha"
                                  className="max-w-32 rounded-lg transition-transform hover:scale-105"
                                />
                                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all flex items-center justify-center">
                                  <Maximize2 className="w-4 h-4 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                                </div>
                              </div>
                            ) : message.mediaType?.includes('video') ? (
                              <div 
                                className="relative group cursor-pointer bg-black rounded-lg"
                                onClick={() => openMediaViewer(message.mediaUrl!, message.mediaType!, message.mediaName, message.mediaMimeType)}
                              >
                                <video 
                                  src={message.mediaUrl}
                                  className="max-w-full rounded-lg"
                                  preload="metadata"
                                />
                                <div className="absolute inset-0 bg-black bg-opacity-30 group-hover:bg-opacity-50 rounded-lg transition-all flex items-center justify-center">
                                  <div className="bg-white bg-opacity-90 rounded-full p-3">
                                    <Play className="w-8 h-8 text-gray-800" />
                                  </div>
                                </div>
                              </div>
                            ) : message.mediaType?.includes('audio') ? (
                              <div 
                                className="flex items-center space-x-3 p-3 bg-black bg-opacity-10 rounded-lg cursor-pointer hover:bg-opacity-20 transition-all"
                                onClick={() => openMediaViewer(message.mediaUrl!, message.mediaType!, message.mediaName, message.mediaMimeType)}
                              >
                                <div className="bg-green-500 rounded-full p-2">
                                  <Play className="w-4 h-4 text-white" />
                                </div>
                                <div className="flex-1">
                                  <p className="text-sm font-medium">{message.mediaName || 'Áudio'}</p>
                                  <p className="text-xs text-gray-500">Clique para reproduzir</p>
                                </div>
                                <Volume2 className="w-4 h-4 text-gray-400" />
                              </div>
                            ) : (
                              <div 
                                className="flex items-center space-x-2 p-2 bg-black bg-opacity-10 rounded cursor-pointer hover:bg-opacity-20 transition-all"
                                onClick={() => openMediaViewer(message.mediaUrl!, message.mediaType!, message.mediaName, message.mediaMimeType)}
                              >
                                <FileText className="w-4 h-4" />
                                <span className="text-sm">{message.mediaName || 'Documento'}</span>
                                <Download className="w-4 h-4 ml-auto" />
                              </div>
                            )}
                          </div>
                        )}
                        
                        {/* Message text */}
                        {message.body && (
                          <p className="text-sm">{message.body}</p>
                        )}
                        
                        {/* Location */}
                        {message.location && (
                          <div className="mt-2 p-2 bg-black bg-opacity-10 rounded">
                            <p className="text-xs">📍 Localização</p>
                            {message.location.description && (
                              <p className="text-sm mt-1">{message.location.description}</p>
                            )}
                          </div>
                        )}
                        
                        {/* Message footer */}
                        <div className="flex items-center justify-end space-x-1 mt-1">
                          {message.isStarred && <Star className="w-3 h-3 text-yellow-400" />}
                          <span className="text-xs opacity-70">
                            {formatTime(new Date(message.timestamp))}
                          </span>
                          {getMessageStatusIcon(message)}
                        </div>
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </div>

            {/* Message Input */}
            <div className="p-4 bg-gray-50 border-t border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="relative emoji-picker-container">
                  <button 
                    onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                    className="p-2 text-gray-600 hover:bg-gray-200 rounded-full transition-colors"
                    title="Emojis"
                  >
                    <Smile className="w-5 h-5" />
                  </button>
                  {showEmojiPicker && (
                    <div className="absolute bottom-12 left-0 bg-white border border-gray-200 rounded-lg p-4 shadow-lg z-10 w-80">
                      <div className="grid grid-cols-8 gap-2 text-lg max-h-48 overflow-y-auto">
                        {['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '❤️', '💕', '💖', '💗', '💙', '💚', '💛', '🧡', '💜', '🖤', '🤍', '🤎', '💯', '💢', '💥', '💫', '💦', '💨', '🕳️', '💬', '👁️‍🗨️', '🗨️', '🗯️', '💭', '💤', '👋', '🤚', '🖐️', '✋', '🖖', '👌', '🤌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👍', '👎', '👊', '✊', '🤛', '🤜'].map((emoji) => (
                          <button
                            key={emoji}
                            onClick={() => {
                              setMessageInput(prev => prev + emoji);
                              setShowEmojiPicker(false);
                            }}
                            className="hover:bg-gray-100 p-1 rounded transition-colors"
                            title={emoji}
                          >
                            {emoji}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                <button 
                  onClick={() => fileInputRef.current?.click()}
                  className="p-2 text-gray-600 hover:bg-gray-200 rounded-full transition-colors" 
                  title="Anexar arquivo"
                >
                  <Paperclip className="w-5 h-5" />
                </button>
                
                <div className="flex-1 relative">
                  <input
                    ref={inputRef}
                    type="text"
                    placeholder="Digite uma mensagem"
                    value={messageInput}
                    onChange={(e) => setMessageInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="w-full px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-green-500"
                  />
                </div>
                
                {messageInput.trim() ? (
                  <button 
                    onClick={handleSendMessage}
                    disabled={sendMessageMutation.isPending}
                    className="p-2 bg-green-600 text-white rounded-full hover:bg-green-700 disabled:opacity-50"
                  >
                    <Send className="w-5 h-5" />
                  </button>
                ) : (
                  <button 
                    onClick={isRecording ? stopRecording : startRecording}
                    className={`p-2 rounded-full transition-colors ${
                      isRecording 
                        ? 'bg-red-500 text-white hover:bg-red-600' 
                        : 'text-gray-600 hover:bg-gray-200'
                    }`}
                    title={isRecording ? "Parar gravação" : "Gravar áudio"}
                  >
                    <Mic className="w-5 h-5" />
                  </button>
                )}
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <MessageSquare className="w-24 h-24 text-gray-400 mx-auto mb-6" />
              <h2 className="text-2xl font-semibold text-gray-700 mb-2">WhatsApp Web</h2>
              <p className="text-gray-500 mb-4">
                Envie e receba mensagens sem manter seu telefone conectado à internet.
              </p>
              <p className="text-sm text-gray-400">
                Selecione uma conversa para começar a enviar mensagens.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Chat Info Sidebar */}
      {showChatInfo && selectedChatData && (
        <div className="w-80 bg-white border-l border-gray-200 p-4">
          <div className="text-center mb-6">
            <div className="relative">
              {selectedChatData.profilePicUrl ? (
                <img 
                  src={selectedChatData.profilePicUrl} 
                  alt={selectedChatData.name}
                  className="w-20 h-20 rounded-full mx-auto mb-4 object-cover"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    e.currentTarget.nextElementSibling?.classList.remove('hidden');
                  }}
                />
              ) : null}
              <div className={`w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 font-semibold mx-auto mb-4 ${selectedChatData.profilePicUrl ? 'hidden' : ''}`}>
                {selectedChatData.isGroup ? <Users className="w-10 h-10" /> : getInitials(selectedChatData.name)}
              </div>
            </div>
            <h3 className="text-xl font-semibold text-gray-800">{selectedChatData.name}</h3>
            <p className="text-sm text-gray-500">{selectedChatData.phone}</p>
          </div>
          
          {selectedChatData.isGroup && (
            <div className="mb-6">
              <h4 className="font-semibold text-gray-800 mb-2">Informações do Grupo</h4>
              <p className="text-sm text-gray-600">
                {selectedChatData.groupMetadata?.participants || 0} participantes
              </p>
            </div>
          )}
          
          {/* Save Contact Button - appears for unsaved contacts */}
          {!selectedChatData.isGroup && !selectedChatData.isContactSaved && (
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-2">Contato não salvo</h4>
              <p className="text-sm text-green-700 mb-3">
                {selectedChatData.phone}
              </p>
              <button
                onClick={() => saveContactMutation.mutate({
                  contactPhone: selectedChatData.phone,
                  contactName: selectedChatData.name
                })}
                disabled={saveContactMutation.isPending}
                className="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saveContactMutation.isPending ? 'Salvando...' : 'Salvar Contato'}
              </button>
            </div>
          )}

          <div className="space-y-2">
            <button 
              onClick={() => {
                toast({
                  title: "Conversa arquivada",
                  description: "A conversa foi movida para o arquivo",
                });
                setShowChatInfo(false);
              }}
              className="w-full p-3 text-left hover:bg-gray-50 rounded-lg flex items-center space-x-3 transition-colors"
            >
              <Archive className="w-5 h-5 text-gray-600" />
              <span>Arquivar conversa</span>
            </button>
            <button 
              onClick={() => {
                toast({
                  title: "Notificações silenciadas",
                  description: "Você não receberá notificações desta conversa",
                });
                setShowChatInfo(false);
              }}
              className="w-full p-3 text-left hover:bg-gray-50 rounded-lg flex items-center space-x-3 transition-colors"
            >
              <VolumeX className="w-5 h-5 text-gray-600" />
              <span>Silenciar notificações</span>
            </button>
            <button 
              onClick={() => {
                if (confirm("Tem certeza que deseja excluir esta conversa?")) {
                  toast({
                    title: "Conversa excluída",
                    description: "A conversa foi removida permanentemente",
                    variant: "destructive"
                  });
                  setSelectedChat("");
                  setShowChatInfo(false);
                }
              }}
              className="w-full p-3 text-left hover:bg-gray-50 rounded-lg flex items-center space-x-3 transition-colors"
            >
              <Trash2 className="w-5 h-5 text-red-600" />
              <span className="text-red-600">Excluir conversa</span>
            </button>
          </div>
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Media Modal */}
      {showMediaModal && selectedMedia && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Enviar arquivo</h3>
              <button
                onClick={() => {
                  setShowMediaModal(false);
                  setSelectedMedia(null);
                  setMediaCaption("");
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* Media preview */}
            <div className="mb-4">
              {selectedMedia.type === 'image' && selectedMedia.preview && (
                <img
                  src={selectedMedia.preview}
                  alt="Preview"
                  className="w-full h-48 object-cover rounded-lg"
                />
              )}
              {selectedMedia.type === 'video' && (
                <div className="w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                  <VideoIcon className="w-12 h-12 text-gray-400" />
                  <span className="ml-2 text-gray-600">{selectedMedia.name}</span>
                </div>
              )}
              {selectedMedia.type === 'audio' && (
                <div className="w-full h-24 bg-gray-100 rounded-lg flex items-center justify-center">
                  <Mic className="w-8 h-8 text-gray-400" />
                  <span className="ml-2 text-gray-600">{selectedMedia.name}</span>
                </div>
              )}
              {selectedMedia.type === 'application' && (
                <div className="w-full h-24 bg-gray-100 rounded-lg flex items-center justify-center">
                  <Paperclip className="w-8 h-8 text-gray-400" />
                  <span className="ml-2 text-gray-600">{selectedMedia.name}</span>
                </div>
              )}
            </div>

            {/* File info */}
            <div className="mb-4 text-sm text-gray-600">
              <p>Nome: {selectedMedia.name}</p>
              <p>Tamanho: {(selectedMedia.size / 1024 / 1024).toFixed(2)} MB</p>
            </div>

            {/* Caption input */}
            <div className="mb-4">
              <textarea
                value={mediaCaption}
                onChange={(e) => setMediaCaption(e.target.value)}
                placeholder="Adicione uma legenda..."
                className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-green-500"
                rows={3}
              />
            </div>

            {/* Actions */}
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  setShowMediaModal(false);
                  setSelectedMedia(null);
                  setMediaCaption("");
                }}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancelar
              </button>
              <button
                onClick={() => {
                  if (selectedChat) {
                    sendMediaMutation.mutate({
                      file: selectedMedia.file,
                      caption: mediaCaption,
                      chat: selectedChat
                    });
                  }
                }}
                disabled={sendMediaMutation.isPending}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
              >
                {sendMediaMutation.isPending ? 'Enviando...' : 'Enviar'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Global Search Modal */}
      {showGlobalSearch && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-lg w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Pesquisa Global</h3>
              <button
                onClick={() => {
                  setShowGlobalSearch(false);
                  setGlobalSearch("");
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Pesquisar em todas as conversas..."
                value={globalSearch}
                onChange={(e) => handleGlobalSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                autoFocus
              />
            </div>

            <div className="max-h-64 overflow-y-auto">
              {filteredChats.length > 0 ? (
                filteredChats.map((chat: WhatsAppChat) => (
                  <div
                    key={chat.id}
                    onClick={() => {
                      setSelectedChat(chat.id);
                      setShowGlobalSearch(false);
                      setGlobalSearch("");
                    }}
                    className="p-3 hover:bg-gray-50 rounded-lg cursor-pointer border-b border-gray-100"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                        {getInitials(chat.name)}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">{chat.name}</p>
                        {chat.lastMessage && (
                          <p className="text-sm text-gray-500 truncate">
                            {chat.lastMessage.body}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-center text-gray-500 py-4">Nenhum resultado encontrado</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Recording indicator */}
      {isRecording && (
        <div className="fixed bottom-20 right-6 bg-red-500 text-white px-4 py-2 rounded-full flex items-center space-x-2 shadow-lg z-50">
          <div className="w-3 h-3 bg-red-300 rounded-full animate-pulse"></div>
          <span className="text-sm font-medium">
            Gravando {Math.floor(recordingTime / 60)}:{(recordingTime % 60).toString().padStart(2, '0')}
          </span>
          <button
            onClick={stopRecording}
            className="ml-2 w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Sync Progress Modal */}
      <SyncProgressModal
        isOpen={showSyncModal}
        onClose={() => setShowSyncModal(false)}
        sessionId={currentUserId}
        onSyncComplete={() => {
          setShowSyncModal(false);
          // Refresh chats after sync completion
          refetchChats();
        }}
      />

      {/* Fullscreen Media Viewer */}
      {showMediaViewer && currentMedia && (
        <div className="fixed inset-0 bg-black bg-opacity-95 flex items-center justify-center z-50">
          {/* Header Controls */}
          <div className="absolute top-0 left-0 right-0 bg-black bg-opacity-50 p-4 flex items-center justify-between z-10">
            <div className="text-white">
              <h3 className="font-medium">{currentMedia.name || 'Mídia'}</h3>
              <p className="text-sm text-gray-300">{currentMedia.mimeType}</p>
            </div>
            <button
              onClick={closeMediaViewer}
              className="text-white hover:bg-white hover:bg-opacity-20 p-2 rounded-full transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Media Content */}
          <div className="flex-1 flex items-center justify-center p-16">
            {currentMedia.type === 'image' && (
              <div className="relative max-w-full max-h-full">
                <img
                  ref={imageRef}
                  src={currentMedia.url}
                  alt="Imagem em tela cheia"
                  className="max-w-full max-h-full object-contain transition-transform duration-200"
                  style={{
                    transform: `scale(${imageZoom}) rotate(${imageRotation}deg)`,
                    cursor: imageZoom > 1 ? 'grab' : 'default'
                  }}
                  draggable={false}
                />
              </div>
            )}

            {currentMedia.type === 'video' && (
              <div className="relative max-w-full max-h-full">
                <video
                  ref={videoRef}
                  src={currentMedia.url}
                  className="max-w-full max-h-full"
                  controls
                  autoPlay={false}
                  onLoadedMetadata={() => {
                    if (videoRef.current) {
                      setDuration(videoRef.current.duration);
                    }
                  }}
                  onTimeUpdate={() => {
                    if (videoRef.current) {
                      setCurrentTime(videoRef.current.currentTime);
                    }
                  }}
                  onPlay={() => setIsPlaying(true)}
                  onPause={() => setIsPlaying(false)}
                />
              </div>
            )}

            {currentMedia.type === 'audio' && (
              <div className="bg-gray-800 rounded-lg p-8 min-w-96">
                <div className="text-center mb-6">
                  <div className="bg-green-500 rounded-full p-6 w-24 h-24 mx-auto mb-4 flex items-center justify-center">
                    <Volume2 className="w-12 h-12 text-white" />
                  </div>
                  <h3 className="text-white text-lg font-medium mb-2">
                    {currentMedia.name || 'Áudio'}
                  </h3>
                  <p className="text-gray-400 text-sm">
                    {formatDuration(currentTime)} / {formatDuration(duration)}
                  </p>
                </div>

                <audio
                  ref={audioRef}
                  src={currentMedia.url}
                  className="hidden"
                  onLoadedMetadata={() => {
                    if (audioRef.current) {
                      setDuration(audioRef.current.duration);
                    }
                  }}
                  onTimeUpdate={() => {
                    if (audioRef.current) {
                      setCurrentTime(audioRef.current.currentTime);
                    }
                  }}
                  onPlay={() => setIsPlaying(true)}
                  onPause={() => setIsPlaying(false)}
                  onEnded={() => setIsPlaying(false)}
                />

                {/* Audio Progress Bar */}
                <div className="mb-6">
                  <div 
                    className="bg-gray-600 h-2 rounded-full cursor-pointer"
                    onClick={(e) => {
                      const rect = e.currentTarget.getBoundingClientRect();
                      const percent = (e.clientX - rect.left) / rect.width;
                      const newTime = percent * duration;
                      handleSeek(newTime);
                    }}
                  >
                    <div 
                      className="bg-green-500 h-2 rounded-full transition-all"
                      style={{ width: `${(currentTime / duration) * 100}%` }}
                    />
                  </div>
                </div>

                {/* Audio Controls */}
                <div className="flex items-center justify-center space-x-4">
                  <button
                    onClick={() => handleSeek(Math.max(0, currentTime - 10))}
                    className="text-white hover:bg-gray-700 p-2 rounded-full transition-colors"
                  >
                    <SkipBack className="w-6 h-6" />
                  </button>
                  <button
                    onClick={handlePlayPause}
                    className="bg-green-500 hover:bg-green-600 text-white p-4 rounded-full transition-colors"
                  >
                    {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8" />}
                  </button>
                  <button
                    onClick={() => handleSeek(Math.min(duration, currentTime + 10))}
                    className="text-white hover:bg-gray-700 p-2 rounded-full transition-colors"
                  >
                    <SkipForward className="w-6 h-6" />
                  </button>
                </div>

                {/* Volume Control */}
                <div className="flex items-center justify-center space-x-3 mt-6">
                  <Volume2 className="w-5 h-5 text-gray-400" />
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={volume}
                    onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                    className="flex-1 max-w-32"
                  />
                  <span className="text-gray-400 text-sm w-10">{Math.round(volume * 100)}%</span>
                </div>
              </div>
            )}

            {currentMedia.type === 'document' && (
              <div className="bg-gray-800 rounded-lg p-8 min-w-96 text-center">
                <div className="bg-blue-500 rounded-full p-6 w-24 h-24 mx-auto mb-4 flex items-center justify-center">
                  <FileText className="w-12 h-12 text-white" />
                </div>
                <h3 className="text-white text-lg font-medium mb-2">
                  {currentMedia.name || 'Documento'}
                </h3>
                <p className="text-gray-400 text-sm mb-6">
                  {currentMedia.mimeType}
                </p>
                <a
                  href={currentMedia.url}
                  download={currentMedia.name}
                  className="inline-flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors"
                >
                  <Download className="w-5 h-5" />
                  <span>Baixar Arquivo</span>
                </a>
              </div>
            )}
          </div>

          {/* Image Controls */}
          {currentMedia.type === 'image' && (
            <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 p-4 flex items-center justify-center space-x-4 z-10">
              <button
                onClick={handleZoomOut}
                disabled={imageZoom <= 0.25}
                className="text-white hover:bg-white hover:bg-opacity-20 p-2 rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Diminuir zoom"
              >
                <ZoomOut className="w-5 h-5" />
              </button>
              <span className="text-white text-sm px-3 py-1 bg-black bg-opacity-50 rounded">
                {Math.round(imageZoom * 100)}%
              </span>
              <button
                onClick={handleZoomIn}
                disabled={imageZoom >= 3}
                className="text-white hover:bg-white hover:bg-opacity-20 p-2 rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Aumentar zoom"
              >
                <ZoomIn className="w-5 h-5" />
              </button>
              <div className="w-px h-6 bg-gray-500" />
              <button
                onClick={handleRotate}
                className="text-white hover:bg-white hover:bg-opacity-20 p-2 rounded-full transition-colors"
                title="Girar imagem"
              >
                <RotateCw className="w-5 h-5" />
              </button>
              <div className="w-px h-6 bg-gray-500" />
              <button
                onClick={() => {
                  setImageZoom(1);
                  setImageRotation(0);
                }}
                className="text-white hover:bg-white hover:bg-opacity-20 px-3 py-2 rounded transition-colors text-sm"
                title="Resetar zoom e rotação"
              >
                Resetar
              </button>
            </div>
          )}

          {/* Keyboard Instructions */}
          <div className="absolute bottom-4 left-4 text-gray-400 text-xs">
            <p>ESC para fechar • Setas para navegar</p>
            {currentMedia.type === 'image' && (
              <p>+ / - para zoom • R para girar</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}