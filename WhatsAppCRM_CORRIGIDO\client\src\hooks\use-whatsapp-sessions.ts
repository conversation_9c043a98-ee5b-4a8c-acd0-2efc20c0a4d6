import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "./use-toast";

interface WhatsAppSession {
  id: number;
  phoneNumber: string;
  sessionData?: string;
  isConnected: boolean;
  lastSeen?: string;
  createdAt: string;
}

interface WhatsAppStatus {
  connected: boolean;
  phoneNumber?: string;
  sessionId: string;
  qrCode?: string;
}

interface WhatsAppProgress {
  status: 'loading' | 'connecting' | 'completed' | 'error';
  current: number;
  total: number;
  percentage: number;
  message: string;
}

interface InitializeSessionResponse {
  qrCode?: string;
  alreadyConnected?: boolean;
  phoneNumber?: string;
}

export function useWhatsAppStatus(userId?: string) {
  return useQuery({
    queryKey: ['/api/whatsapp/status', userId],
    queryFn: async () => {
      if (!userId || typeof userId !== 'string') return null;
      
      const response = await fetch(`/api/whatsapp/status?userId=${encodeURIComponent(userId)}`);
      if (!response.ok) {
        console.error(`WhatsApp status fetch failed: ${response.status}`);
        return {
          connected: false,
          phoneNumber: null,
          sessionId: userId,
          qrCode: null
        };
      }
      
      const data = await response.json();
      
      // Defensive validation
      return {
        connected: Boolean(data?.connected),
        phoneNumber: data?.phoneNumber || null,
        sessionId: data?.sessionId || userId,
        qrCode: data?.qrCode || null
      } as WhatsAppStatus;
    },
    enabled: !!userId && typeof userId === 'string',
    refetchInterval: 3000,
    staleTime: 1000,
    retry: 2,
    retryDelay: 1000,
  });
}

export function useWhatsAppSessions() {
  return useQuery({
    queryKey: ['/api/whatsapp/sessions'],
    queryFn: async () => {
      const response = await fetch('/api/whatsapp/sessions');
      if (!response.ok) {
        console.error(`WhatsApp sessions fetch failed: ${response.status}`);
        return [];
      }
      
      const data = await response.json();
      
      // Defensive validation for array
      if (!Array.isArray(data)) {
        console.warn('WhatsApp sessions response is not an array:', data);
        return [];
      }
      
      // Validate each session object
      return data.filter(session => 
        session && 
        typeof session === 'object' && 
        session.id && 
        typeof session.phoneNumber === 'string'
      ).map(session => ({
        id: session.id,
        phoneNumber: session.phoneNumber,
        sessionData: session.sessionData || '',
        isConnected: Boolean(session.isConnected),
        lastSeen: session.lastSeen || null,
        createdAt: session.createdAt || new Date().toISOString()
      })) as WhatsAppSession[];
    },
    refetchInterval: 5000,
    staleTime: 2000,
    retry: 2,
    retryDelay: 1000,
  });
}

export function useWhatsAppProgress(userId?: string) {
  return useQuery({
    queryKey: ['/api/whatsapp/progress', userId],
    queryFn: async () => {
      if (!userId) return null;
      
      const response = await fetch(`/api/whatsapp/progress?userId=${encodeURIComponent(userId)}`);
      if (!response.ok) {
        throw new Error('Falha ao verificar progresso');
      }
      return response.json() as Promise<WhatsAppProgress>;
    },
    enabled: !!userId,
    refetchInterval: 1000,
    staleTime: 500,
  });
}

export function useInitializeWhatsApp() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (userId: string) => {
      const response = await fetch('/api/whatsapp/initialize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Erro desconhecido' }));
        throw new Error(error.message || 'Falha ao inicializar WhatsApp');
      }

      return response.json() as Promise<InitializeSessionResponse>;
    },
    onSuccess: (data, userId) => {
      queryClient.invalidateQueries({ 
        queryKey: ['/api/whatsapp/status', userId],
      });
      
      queryClient.invalidateQueries({ 
        queryKey: ['/api/whatsapp/sessions'],
      });

      if (data.alreadyConnected) {
        toast({
          title: "WhatsApp conectado",
          description: "Sua sessão do WhatsApp já está ativa",
        });
      } else if (data.qrCode) {
        toast({
          title: "QR Code gerado",
          description: "Escaneie o QR Code com seu WhatsApp",
        });
      }
    },
    onError: (error) => {
      toast({
        title: "Erro ao conectar WhatsApp",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useDisconnectWhatsApp() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (userId: string) => {
      const response = await fetch('/api/whatsapp/disconnect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Erro desconhecido' }));
        throw new Error(error.message || 'Falha ao desconectar WhatsApp');
      }

      return response.json();
    },
    onSuccess: (data, userId) => {
      queryClient.invalidateQueries({ 
        queryKey: ['/api/whatsapp/status', userId],
      });
      
      queryClient.invalidateQueries({ 
        queryKey: ['/api/whatsapp/sessions'],
      });

      toast({
        title: "WhatsApp desconectado",
        description: "Sua sessão foi encerrada com sucesso",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao desconectar",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useBlockContact() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      userId,
      contactPhone,
      block
    }: {
      userId: string;
      contactPhone: string;
      block: boolean;
    }) => {
      const response = await fetch('/api/whatsapp/block-contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, contactPhone, block }),
      });

      if (!response.ok) {
        throw new Error(`Falha ao ${block ? 'bloquear' : 'desbloquear'} contato`);
      }

      return response.json();
    },
    onSuccess: (data, { block }) => {
      queryClient.invalidateQueries({ 
        queryKey: ['/api/whatsapp/chats'],
      });

      toast({
        title: block ? "Contato bloqueado" : "Contato desbloqueado",
        description: `Contato ${block ? 'bloqueado' : 'desbloqueado'} com sucesso`,
      });
    },
    onError: (error, { block }) => {
      toast({
        title: `Erro ao ${block ? 'bloquear' : 'desbloquear'} contato`,
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useWhatsAppHealth(whatsappNumber?: string) {
  return useQuery({
    queryKey: ['/api/health', whatsappNumber],
    queryFn: async () => {
      if (!whatsappNumber) return null;
      
      const response = await fetch(`/api/health?whatsappNumber=${encodeURIComponent(whatsappNumber)}`);
      if (!response.ok) {
        throw new Error('Falha ao verificar saúde do WhatsApp');
      }
      return response.json();
    },
    enabled: !!whatsappNumber,
    refetchInterval: 30000, // 30 seconds
    staleTime: 15000,
    retry: 2,
  });
}

export function useRestartWhatsApp() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (userId: string) => {
      // First disconnect
      await fetch('/api/whatsapp/disconnect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      // Then initialize again
      const response = await fetch('/api/whatsapp/initialize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) {
        throw new Error('Falha ao reiniciar WhatsApp');
      }

      return response.json();
    },
    onSuccess: (data, userId) => {
      queryClient.invalidateQueries({ 
        queryKey: ['/api/whatsapp/status', userId],
      });
      
      queryClient.invalidateQueries({ 
        queryKey: ['/api/whatsapp/sessions'],
      });

      toast({
        title: "WhatsApp reiniciado",
        description: "Sessão reiniciada. Escaneie o QR Code novamente",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao reiniciar",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

// Hook composto para gerenciar todo o ciclo de vida da sessão
export function useWhatsAppSession(userId?: string) {
  const statusQuery = useWhatsAppStatus(userId);
  const progressQuery = useWhatsAppProgress(userId);
  const initializeMutation = useInitializeWhatsApp();
  const disconnectMutation = useDisconnectWhatsApp();
  const restartMutation = useRestartWhatsApp();

  const isConnected = statusQuery.data?.connected || false;
  const phoneNumber = statusQuery.data?.phoneNumber;
  const qrCode = statusQuery.data?.qrCode;
  const isLoading = statusQuery.isLoading || initializeMutation.isPending;
  const progress = progressQuery.data;

  const initialize = (userId: string) => initializeMutation.mutate(userId);
  const disconnect = (userId: string) => disconnectMutation.mutate(userId);
  const restart = (userId: string) => restartMutation.mutate(userId);

  return {
    // Estado
    isConnected,
    phoneNumber,
    qrCode,
    isLoading,
    progress,
    
    // Queries
    statusQuery,
    progressQuery,
    
    // Mutations
    initializeMutation,
    disconnectMutation,
    restartMutation,
    
    // Funções de conveniência
    initialize,
    disconnect,
    restart,
  };
}