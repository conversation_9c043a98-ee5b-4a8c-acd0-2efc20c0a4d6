import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';

interface UseWebSocketOptions {
  userId?: string;
  whatsappNumber?: string;
  autoConnect?: boolean;
  onMessage?: (message: any) => void;
}

interface WebSocketState {
  connected: boolean;
  socket: Socket | null;
  error: string | null;
  reconnecting: boolean;
}

export const useWebSocket = (options: UseWebSocketOptions = {}) => {
  const { userId, whatsappNumber, autoConnect = true, onMessage } = options;
  const [state, setState] = useState<WebSocketState>({
    connected: false,
    socket: null,
    error: null,
    reconnecting: false
  });

  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;
  const processedEventsRef = useRef<Set<string>>(new Set()); // Anti-duplication protection

  // Event validation with timestamp-based deduplication
  const validateAndProcessEvent = (eventType: string, data: any): boolean => {
    if (!data || typeof data !== 'object') {
      console.warn(`[WebSocket] Invalid event data for ${eventType}:`, data);
      return false;
    }

    // Generate unique event ID based on type, timestamp, and content
    const eventId = `${eventType}-${data.id || data.messageId || data.clientId || data.taskId || ''}-${data.timestamp || Date.now()}`;
    
    if (processedEventsRef.current.has(eventId)) {
      console.log(`[WebSocket] Duplicate event ignored: ${eventId}`);
      return false;
    }

    processedEventsRef.current.add(eventId);
    
    // Cleanup old events (keep last 1000 to prevent memory leaks)
    if (processedEventsRef.current.size > 1000) {
      const eventsArray = Array.from(processedEventsRef.current);
      const recentEvents = eventsArray.slice(-500);
      processedEventsRef.current = new Set(recentEvents);
    }

    return true;
  };

  const connect = () => {
    if (state.socket?.connected) return;

    // Use the correct WebSocket path configured in routes.ts
    const socket = io('/ws', {
      transports: ['websocket'],
      timeout: 10000,
      reconnection: false, // Handle reconnection manually
      forceNew: true
    });

    socket.on('connect', () => {
      console.log('[WebSocket] Conectado com sucesso');
      setState(prev => ({ ...prev, connected: true, error: null, reconnecting: false }));
      reconnectAttemptsRef.current = 0;

      // Authenticate user if userId is provided
      if (userId) {
        socket.emit('authenticate', { userId, whatsappNumber });
      }
    });

    socket.on('disconnect', (reason) => {
      console.log('[WebSocket] Desconectado:', reason);
      setState(prev => ({ ...prev, connected: false }));

      if (reason === 'io server disconnect') {
        socket.connect();
      }
    });

    socket.on('connect_error', (error) => {
      // Silently handle connection errors to prevent console spam
      setState(prev => ({ ...prev, error: 'Conexão indisponível', reconnecting: false }));
    });

    socket.on('authenticated', (data) => {
      console.log('[WebSocket] Autenticado:', data);
    });

    // CONTROLLED EVENTS with anti-duplication
    socket.on('message.new', (data) => {
      if (validateAndProcessEvent('message.new', data)) {
        window.dispatchEvent(new CustomEvent('websocket:message:received', { detail: data }));
      }
    });

    socket.on('message.updated', (data) => {
      if (validateAndProcessEvent('message.updated', data)) {
        window.dispatchEvent(new CustomEvent('websocket:message:updated', { detail: data }));
      }
    });

    socket.on('chat.new', (data) => {
      if (validateAndProcessEvent('chat.new', data)) {
        window.dispatchEvent(new CustomEvent('websocket:chat:new', { detail: data }));
      }
    });

    socket.on('media.loaded', (data) => {
      if (validateAndProcessEvent('media.loaded', data)) {
        window.dispatchEvent(new CustomEvent('websocket:media:loaded', { detail: data }));
      }
    });

    // CRM events with validation
    socket.on('crm.client.created', (data) => {
      if (validateAndProcessEvent('crm.client.created', data)) {
        window.dispatchEvent(new CustomEvent('websocket:crm:client:created', { detail: data }));
      }
    });

    socket.on('crm.client.updated', (data) => {
      if (validateAndProcessEvent('crm.client.updated', data)) {
        window.dispatchEvent(new CustomEvent('websocket:crm:client:updated', { detail: data }));
      }
    });

    socket.on('crm.client.deleted', (data) => {
      if (validateAndProcessEvent('crm.client.deleted', data)) {
        window.dispatchEvent(new CustomEvent('websocket:crm:client:deleted', { detail: data }));
      }
    });

    // Kanban events with validation
    socket.on('kanban.task.created', (data) => {
      if (validateAndProcessEvent('kanban.task.created', data)) {
        window.dispatchEvent(new CustomEvent('websocket:kanban:task:created', { detail: data }));
      }
    });

    socket.on('kanban.task.updated', (data) => {
      if (validateAndProcessEvent('kanban.task.updated', data)) {
        window.dispatchEvent(new CustomEvent('websocket:kanban:task:updated', { detail: data }));
      }
    });

    socket.on('kanban.task.moved', (data) => {
      if (validateAndProcessEvent('kanban.task.moved', data)) {
        window.dispatchEvent(new CustomEvent('websocket:kanban:task:moved', { detail: data }));
      }
    });

    socket.on('kanban.task.deleted', (data) => {
      if (validateAndProcessEvent('kanban.task.deleted', data)) {
        window.dispatchEvent(new CustomEvent('websocket:kanban:task:deleted', { detail: data }));
      }
    });

    // WhatsApp events
    socket.on('whatsapp:connected', (data) => {
      window.dispatchEvent(new CustomEvent('websocket:whatsapp:connected', { detail: data }));
    });

    socket.on('whatsapp:disconnected', (data) => {
      window.dispatchEvent(new CustomEvent('websocket:whatsapp:disconnected', { detail: data }));
    });

    setState(prev => ({ ...prev, socket }));
  };

  const disconnect = () => {
    if (state.socket) {
      state.socket.disconnect();
      setState(prev => ({ ...prev, socket: null, connected: false }));
    }
  };

  const emit = (event: string, data: any) => {
    if (state.socket?.connected) {
      state.socket.emit(event, data);
    } else {
      console.warn('[WebSocket] Tentativa de emitir evento sem conexão:', event);
    }
  };

  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      disconnect();
    };
  }, [autoConnect, userId, whatsappNumber]);

  return {
    ...state,
    connect,
    disconnect,
    emit
  };
};