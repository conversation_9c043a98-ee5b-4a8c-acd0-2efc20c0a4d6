import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "./use-toast";

interface Task {
  id: number;
  whatsappNumber: string;
  title: string;
  description?: string;
  status: 'todo' | 'in_progress' | 'done';
  priority: 'low' | 'medium' | 'high';
  dueDate?: string;
  clientId?: number;
  createdAt: string;
  updatedAt: string;
}

interface CreateTaskRequest {
  whatsappNumber: string;
  title: string;
  description?: string;
  status?: 'todo' | 'in_progress' | 'done';
  priority?: 'low' | 'medium' | 'high';
  dueDate?: string;
  clientId?: number;
}

interface UpdateTaskRequest {
  title?: string;
  description?: string;
  status?: 'todo' | 'in_progress' | 'done';
  priority?: 'low' | 'medium' | 'high';
  dueDate?: string;
  clientId?: number;
}

export function useTasks(whatsappNumber?: string) {
  return useQuery({
    queryKey: ['/api/tasks', whatsappNumber],
    queryFn: async () => {
      if (!whatsappNumber) return [];
      
      const response = await fetch(`/api/tasks/${encodeURIComponent(whatsappNumber)}`);
      if (!response.ok) {
        throw new Error('Falha ao carregar tarefas');
      }
      return response.json() as Promise<Task[]>;
    },
    enabled: !!whatsappNumber,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false,
  });
}

export function useTask(taskId: number, whatsappNumber?: string) {
  return useQuery({
    queryKey: ['/api/tasks', whatsappNumber, taskId],
    queryFn: async () => {
      if (!whatsappNumber || !taskId) return null;
      
      const response = await fetch(`/api/tasks/${taskId}?whatsappNumber=${encodeURIComponent(whatsappNumber)}`);
      if (!response.ok) {
        if (response.status === 404) return null;
        throw new Error('Falha ao carregar tarefa');
      }
      return response.json() as Promise<Task>;
    },
    enabled: !!whatsappNumber && !!taskId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useTasksByStatus(whatsappNumber?: string, status?: Task['status']) {
  return useQuery({
    queryKey: ['/api/tasks', whatsappNumber, 'status', status],
    queryFn: async () => {
      if (!whatsappNumber) return [];
      
      const tasks = await fetch(`/api/tasks/${encodeURIComponent(whatsappNumber)}`).then(res => res.json()) as Task[];
      return status ? tasks.filter(task => task.status === status) : tasks;
    },
    enabled: !!whatsappNumber,
    staleTime: 1 * 60 * 1000, // 1 minute
    select: (data) => status ? data.filter(task => task.status === status) : data,
  });
}

export function useTasksByPriority(whatsappNumber?: string, priority?: Task['priority']) {
  return useQuery({
    queryKey: ['/api/tasks', whatsappNumber, 'priority', priority],
    queryFn: async () => {
      if (!whatsappNumber) return [];
      
      const tasks = await fetch(`/api/tasks/${encodeURIComponent(whatsappNumber)}`).then(res => res.json()) as Task[];
      return priority ? tasks.filter(task => task.priority === priority) : tasks;
    },
    enabled: !!whatsappNumber,
    staleTime: 2 * 60 * 1000,
    select: (data) => priority ? data.filter(task => task.priority === priority) : data,
  });
}

export function useCreateTask() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (task: CreateTaskRequest) => {
      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(task),
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Erro desconhecido' }));
        throw new Error(error.message || 'Falha ao criar tarefa');
      }

      return response.json() as Promise<Task>;
    },
    onMutate: async (newTask) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['/api/tasks', newTask.whatsappNumber] });

      // Snapshot previous value
      const previousTasks = queryClient.getQueryData(['/api/tasks', newTask.whatsappNumber]);

      // Optimistically update cache
      const optimisticTask: Task = {
        id: Date.now(), // Temporary ID
        ...newTask,
        status: newTask.status || 'todo',
        priority: newTask.priority || 'medium',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      queryClient.setQueryData(
        ['/api/tasks', newTask.whatsappNumber],
        (oldData: Task[] | undefined) => {
          if (!oldData) return [optimisticTask];
          return [...oldData, optimisticTask];
        }
      );

      return { previousTasks, optimisticTask };
    },
    onError: (error, newTask, context) => {
      // Rollback on error
      if (context?.previousTasks) {
        queryClient.setQueryData(['/api/tasks', newTask.whatsappNumber], context.previousTasks);
      }

      toast({
        title: "Erro ao criar tarefa",
        description: error.message,
        variant: "destructive",
      });
    },
    onSuccess: (newTask) => {
      queryClient.invalidateQueries({ 
        queryKey: ['/api/tasks', newTask.whatsappNumber],
      });

      toast({
        title: "Tarefa criada",
        description: "Nova tarefa adicionada com sucesso",
      });
    },
  });
}

export function useUpdateTask() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ 
      id, 
      whatsappNumber, 
      updates 
    }: { 
      id: number; 
      whatsappNumber: string; 
      updates: UpdateTaskRequest 
    }) => {
      const response = await fetch(`/api/tasks/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ...updates, whatsappNumber }),
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Erro desconhecido' }));
        throw new Error(error.message || 'Falha ao atualizar tarefa');
      }

      return response.json() as Promise<Task>;
    },
    onMutate: async ({ id, whatsappNumber, updates }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['/api/tasks', whatsappNumber] });

      // Snapshot previous values
      const previousTasks = queryClient.getQueryData(['/api/tasks', whatsappNumber]);
      const previousTask = queryClient.getQueryData(['/api/tasks', whatsappNumber, id]);

      // Optimistically update cache
      queryClient.setQueryData(
        ['/api/tasks', whatsappNumber],
        (oldData: Task[] | undefined) => {
          if (!oldData) return [];
          return oldData.map(task => 
            task.id === id 
              ? { ...task, ...updates, updatedAt: new Date().toISOString() }
              : task
          );
        }
      );

      // Update specific task cache
      queryClient.setQueryData(
        ['/api/tasks', whatsappNumber, id],
        (oldData: Task | undefined) => {
          if (!oldData) return undefined;
          return { ...oldData, ...updates, updatedAt: new Date().toISOString() };
        }
      );

      return { previousTasks, previousTask };
    },
    onError: (error, { id, whatsappNumber }, context) => {
      // Rollback on error
      if (context?.previousTasks) {
        queryClient.setQueryData(['/api/tasks', whatsappNumber], context.previousTasks);
      }
      if (context?.previousTask) {
        queryClient.setQueryData(['/api/tasks', whatsappNumber, id], context.previousTask);
      }

      toast({
        title: "Erro ao atualizar tarefa",
        description: error.message,
        variant: "destructive",
      });
    },
    onSuccess: (updatedTask) => {
      queryClient.invalidateQueries({ 
        queryKey: ['/api/tasks', updatedTask.whatsappNumber],
      });

      toast({
        title: "Tarefa atualizada",
        description: "Tarefa atualizada com sucesso",
      });
    },
  });
}

export function useDeleteTask() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ 
      id, 
      whatsappNumber 
    }: { 
      id: number; 
      whatsappNumber: string; 
    }) => {
      const response = await fetch(`/api/tasks/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ whatsappNumber }),
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Erro desconhecido' }));
        throw new Error(error.message || 'Falha ao deletar tarefa');
      }

      return { id, whatsappNumber };
    },
    onMutate: async ({ id, whatsappNumber }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['/api/tasks', whatsappNumber] });

      // Snapshot previous value
      const previousTasks = queryClient.getQueryData(['/api/tasks', whatsappNumber]);

      // Optimistically update cache
      queryClient.setQueryData(
        ['/api/tasks', whatsappNumber],
        (oldData: Task[] | undefined) => {
          if (!oldData) return [];
          return oldData.filter(task => task.id !== id);
        }
      );

      return { previousTasks };
    },
    onError: (error, { whatsappNumber }, context) => {
      // Rollback on error
      if (context?.previousTasks) {
        queryClient.setQueryData(['/api/tasks', whatsappNumber], context.previousTasks);
      }

      toast({
        title: "Erro ao deletar tarefa",
        description: error.message,
        variant: "destructive",
      });
    },
    onSuccess: ({ id, whatsappNumber }) => {
      // Remove specific task cache
      queryClient.removeQueries({
        queryKey: ['/api/tasks', whatsappNumber, id],
      });

      queryClient.invalidateQueries({ 
        queryKey: ['/api/tasks', whatsappNumber],
      });

      toast({
        title: "Tarefa removida",
        description: "Tarefa deletada com sucesso",
      });
    },
  });
}

export function useBulkUpdateTasks() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      whatsappNumber,
      updates
    }: {
      whatsappNumber: string;
      updates: Array<{ id: number; updates: UpdateTaskRequest }>;
    }) => {
      const promises = updates.map(({ id, updates: taskUpdates }) =>
        fetch(`/api/tasks/${id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ...taskUpdates, whatsappNumber }),
        }).then(res => res.json())
      );

      return Promise.all(promises) as Promise<Task[]>;
    },
    onSuccess: (updatedTasks, { whatsappNumber }) => {
      queryClient.invalidateQueries({ 
        queryKey: ['/api/tasks', whatsappNumber],
      });

      toast({
        title: "Tarefas atualizadas",
        description: `${updatedTasks.length} tarefa(s) atualizada(s) com sucesso`,
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao atualizar tarefas",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}