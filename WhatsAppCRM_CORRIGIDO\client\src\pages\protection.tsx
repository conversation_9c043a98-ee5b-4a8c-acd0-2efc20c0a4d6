import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Shield, 
  ShieldAlert, 
  AlertTriangle, 
  MessageSquareX,
  Search,
  Play,
  Square,
  Download,
  Eye,
  Clock,
  Users,
  FileText,
  CheckCircle2,
  XCircle,
  Info
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

interface DeletedConversation {
  id: string;
  chatId: string;
  phoneNumber: string;
  contactName: string;
  deletionType: string;
  messageCountBefore: number;
  deletionDetectedAt: string;
  backedUpMessageIds: string[];
  recoveredContent: any[];
  isRecovered: boolean;
  complianceFlags: any;
}

interface ProtectionStats {
  totalDeletions: number;
  chatDeleted: number;
  chatCleared: number;
  messagesMissing: number;
  totalMessagesLost: number;
  criticalAlerts: number;
}

interface RecoveredMessage {
  id: string;
  originalMessageId: string;
  body: string;
  author: string;
  timestamp: string;
  contactName: string;
  phoneNumber: string;
  hasMedia: boolean;
  mediaType?: string;
  backupReason: string;
}

export default function ProtectionPage() {
  const [monitoringStatus, setMonitoringStatus] = useState<"active" | "inactive">("inactive");
  const [intervalMinutes, setIntervalMinutes] = useState(5);
  const [selectedDeletion, setSelectedDeletion] = useState<DeletedConversation | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Query para o relatório de proteção
  const { data: protectionReport, isLoading: reportLoading } = useQuery({
    queryKey: ["/api/protection/report"],
    refetchInterval: 30000, // Atualiza a cada 30 segundos
  });

  // Query para mensagens recuperadas
  const { data: recoveredMessages, isLoading: messagesLoading } = useQuery({
    queryKey: ["/api/protection/recovered-messages"],
    refetchInterval: 60000,
  });

  // Mutation para iniciar monitoramento
  const startMonitoring = useMutation({
    mutationFn: async (intervalMinutes: number) => {
      const response = await fetch("/api/protection/start", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ intervalMinutes }),
      });
      if (!response.ok) throw new Error("Failed to start monitoring");
      return response.json();
    },
    onSuccess: () => {
      setMonitoringStatus("active");
      toast({
        title: "✅ Proteção Ativada",
        description: `Monitoramento iniciado com intervalo de ${intervalMinutes} minutos`,
      });
      queryClient.invalidateQueries({ queryKey: ["/api/protection/report"] });
    },
    onError: () => {
      toast({
        title: "❌ Erro",
        description: "Falha ao iniciar o monitoramento",
        variant: "destructive",
      });
    },
  });

  // Mutation para parar monitoramento
  const stopMonitoring = useMutation({
    mutationFn: async () => {
      const response = await fetch("/api/protection/stop", { method: "POST" });
      if (!response.ok) throw new Error("Failed to stop monitoring");
      return response.json();
    },
    onSuccess: () => {
      setMonitoringStatus("inactive");
      toast({
        title: "🛑 Proteção Desativada",
        description: "Monitoramento parado com sucesso",
      });
    },
    onError: () => {
      toast({
        title: "❌ Erro",
        description: "Falha ao parar o monitoramento",
        variant: "destructive",
      });
    },
  });

  // Mutation para marcar como recuperado
  const markAsRecovered = useMutation({
    mutationFn: async (deletionId: string) => {
      const response = await fetch("/api/protection/mark-recovered", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          deletionId, 
          recoveryDetails: { 
            recoveredBy: "admin", 
            recoveryMethod: "manual", 
            timestamp: new Date() 
          } 
        }),
      });
      if (!response.ok) throw new Error("Failed to mark as recovered");
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "✅ Marcado como Recuperado",
        description: "Deleção marcada como recuperada com sucesso",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/protection/report"] });
    },
  });

  const deletions: DeletedConversation[] = protectionReport?.deletions || [];
  const stats: ProtectionStats = protectionReport?.stats || {
    totalDeletions: 0,
    chatDeleted: 0,
    chatCleared: 0,
    messagesMissing: 0,
    totalMessagesLost: 0,
    criticalAlerts: 0,
  };

  const filteredDeletions = deletions.filter(deletion =>
    deletion.contactName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    deletion.phoneNumber?.includes(searchTerm)
  );

  const getSeverityColor = (deletionType: string) => {
    switch (deletionType) {
      case "chat_deleted": return "destructive";
      case "chat_cleared": return "secondary";
      default: return "outline";
    }
  };

  const getSeverityIcon = (deletionType: string) => {
    switch (deletionType) {
      case "chat_deleted": return <XCircle className="h-4 w-4" />;
      case "chat_cleared": return <AlertTriangle className="h-4 w-4" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="h-8 w-8 text-blue-600" />
            Proteção Empresarial
          </h1>
          <p className="text-muted-foreground">
            Detecção e recuperação de conversas apagadas
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <Badge variant={monitoringStatus === "active" ? "default" : "secondary"} className="px-3 py-1">
            {monitoringStatus === "active" ? "🟢 Ativo" : "🔴 Inativo"}
          </Badge>
        </div>
      </div>

      {/* Controles de Monitoramento */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShieldAlert className="h-5 w-5" />
            Controle de Monitoramento
          </CardTitle>
          <CardDescription>
            Configure o sistema de detecção automática de conversas apagadas
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Label htmlFor="interval">Intervalo (minutos):</Label>
              <Input
                id="interval"
                type="number"
                value={intervalMinutes}
                onChange={(e) => setIntervalMinutes(Number(e.target.value))}
                className="w-20"
                min="1"
                max="60"
              />
            </div>
            
            {monitoringStatus === "inactive" ? (
              <Button 
                onClick={() => startMonitoring.mutate(intervalMinutes)}
                disabled={startMonitoring.isPending}
                className="flex items-center gap-2"
              >
                <Play className="h-4 w-4" />
                Iniciar Proteção
              </Button>
            ) : (
              <Button 
                onClick={() => stopMonitoring.mutate()}
                disabled={stopMonitoring.isPending}
                variant="destructive"
                className="flex items-center gap-2"
              >
                <Square className="h-4 w-4" />
                Parar Proteção
              </Button>
            )}
          </div>
          
          {monitoringStatus === "active" && (
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                Sistema de proteção ativo. Verificando conversas a cada {intervalMinutes} minutos.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <MessageSquareX className="h-4 w-4 text-red-500" />
              <div>
                <p className="text-sm font-medium">Total Deleções</p>
                <p className="text-2xl font-bold">{stats.totalDeletions}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <XCircle className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-sm font-medium">Chats Deletados</p>
                <p className="text-2xl font-bold">{stats.chatDeleted}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-orange-500" />
              <div>
                <p className="text-sm font-medium">Chats Limpos</p>
                <p className="text-2xl font-bold">{stats.chatCleared}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-sm font-medium">Msgs Perdidas</p>
                <p className="text-2xl font-bold">{stats.messagesMissing}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-purple-500" />
              <div>
                <p className="text-sm font-medium">Total Msgs</p>
                <p className="text-2xl font-bold">{stats.totalMessagesLost}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <ShieldAlert className="h-4 w-4 text-red-500" />
              <div>
                <p className="text-sm font-medium">Alertas Críticos</p>
                <p className="text-2xl font-bold">{stats.criticalAlerts}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs principais */}
      <Tabs defaultValue="deletions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="deletions">Conversas Apagadas</TabsTrigger>
          <TabsTrigger value="recovered">Mensagens Recuperadas</TabsTrigger>
        </TabsList>

        {/* Tab de Conversas Apagadas */}
        <TabsContent value="deletions" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Conversas Apagadas Detectadas</CardTitle>
                <div className="flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  <Input
                    placeholder="Buscar por nome ou telefone..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-64"
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {filteredDeletions.map((deletion) => (
                    <Card key={deletion.id} className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <Badge variant={getSeverityColor(deletion.deletionType)}>
                              {getSeverityIcon(deletion.deletionType)}
                              {deletion.deletionType.replace('_', ' ').toUpperCase()}
                            </Badge>
                            <span className="font-medium">{deletion.contactName}</span>
                            <span className="text-sm text-muted-foreground">{deletion.phoneNumber}</span>
                            {deletion.isRecovered && (
                              <Badge variant="outline" className="text-green-600">
                                <CheckCircle2 className="h-3 w-3 mr-1" />
                                Recuperado
                              </Badge>
                            )}
                          </div>
                          
                          <div className="grid grid-cols-3 gap-4 text-sm">
                            <div>
                              <span className="text-muted-foreground">Mensagens Perdidas:</span>
                              <span className="ml-2 font-medium">{deletion.messageCountBefore}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Backup:</span>
                              <span className="ml-2 font-medium">{deletion.backedUpMessageIds?.length || 0} msgs</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Detectado:</span>
                              <span className="ml-2 font-medium">
                                {format(new Date(deletion.deletionDetectedAt), "dd/MM/yyyy HH:mm", { locale: ptBR })}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedDeletion(deletion)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          
                          {!deletion.isRecovered && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => markAsRecovered.mutate(deletion.id)}
                              disabled={markAsRecovered.isPending}
                            >
                              <CheckCircle2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))}
                  
                  {filteredDeletions.length === 0 && !reportLoading && (
                    <div className="text-center py-8 text-muted-foreground">
                      <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Nenhuma conversa apagada detectada</p>
                      <p className="text-sm">Isso é uma boa notícia para a segurança!</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab de Mensagens Recuperadas */}
        <TabsContent value="recovered" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Mensagens Recuperadas</CardTitle>
              <CardDescription>
                Conteúdo das mensagens que foram preservadas antes da deleção
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {recoveredMessages?.recoveredMessages?.map((message: RecoveredMessage) => (
                    <Card key={message.id} className="p-4">
                      <div className="flex items-start gap-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="font-medium">{message.contactName}</span>
                            <span className="text-sm text-muted-foreground">{message.phoneNumber}</span>
                            <Badge variant="outline" className="text-xs">
                              {message.backupReason}
                            </Badge>
                          </div>
                          
                          <div className="bg-muted p-3 rounded-lg mb-2">
                            <p className="text-sm">{message.body}</p>
                            {message.hasMedia && (
                              <Badge variant="secondary" className="mt-2">
                                📎 {message.mediaType}
                              </Badge>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span>De: {message.author}</span>
                            <span>
                              {format(new Date(message.timestamp), "dd/MM/yyyy HH:mm", { locale: ptBR })}
                            </span>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                  
                  {(!recoveredMessages?.recoveredMessages?.length) && !messagesLoading && (
                    <div className="text-center py-8 text-muted-foreground">
                      <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Nenhuma mensagem recuperada encontrada</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modal de detalhes da deleção */}
      {selectedDeletion && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-2xl max-h-[80vh] overflow-auto">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Detalhes da Deleção
                <Button variant="ghost" size="sm" onClick={() => setSelectedDeletion(null)}>
                  ✕
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Label>Contato</Label>
                  <p className="font-medium">{selectedDeletion.contactName}</p>
                </div>
                <div>
                  <Label>Telefone</Label>
                  <p className="font-medium">{selectedDeletion.phoneNumber}</p>
                </div>
                <div>
                  <Label>Tipo de Deleção</Label>
                  <Badge variant={getSeverityColor(selectedDeletion.deletionType)}>
                    {selectedDeletion.deletionType}
                  </Badge>
                </div>
                <div>
                  <Label>Mensagens Perdidas</Label>
                  <p className="font-medium">{selectedDeletion.messageCountBefore}</p>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <Label>Conteúdo Recuperado</Label>
                <ScrollArea className="h-48 mt-2">
                  <div className="space-y-2">
                    {selectedDeletion.recoveredContent?.map((content: any, index: number) => (
                      <div key={index} className="bg-muted p-3 rounded-lg">
                        <p className="text-sm">{content.body}</p>
                        <div className="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
                          <span>De: {content.author}</span>
                          <span>{format(new Date(content.timestamp), "dd/MM/yyyy HH:mm")}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}