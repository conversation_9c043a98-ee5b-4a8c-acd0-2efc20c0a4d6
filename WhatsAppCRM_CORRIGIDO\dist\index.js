// server/index.ts
import express2 from "express";

// server/routes.ts
import { createServer } from "http";

// server/vite.ts
import express from "express";
import fs from "fs";
import path2 from "path";
import { createServer as createViteServer, createLogger } from "vite";

// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";
var vite_config_default = defineConfig({
  plugins: [
    react(),
    runtimeErrorOverlay(),
    ...process.env.NODE_ENV !== "production" && process.env.REPL_ID !== void 0 ? [
      await import("@replit/vite-plugin-cartographer").then(
        (m) => m.cartographer()
      )
    ] : []
  ],
  resolve: {
    alias: {
      "@": path.resolve(import.meta.dirname, "client", "src"),
      "@shared": path.resolve(import.meta.dirname, "shared"),
      "@assets": path.resolve(import.meta.dirname, "attached_assets")
    }
  },
  root: path.resolve(import.meta.dirname, "client"),
  build: {
    outDir: path.resolve(import.meta.dirname, "dist/public"),
    emptyOutDir: true
  },
  server: {
    fs: {
      strict: true,
      deny: ["**/.*"]
    }
  }
});

// server/vite.ts
import { nanoid } from "nanoid";
var viteLogger = createLogger();
function log(message, source = "express") {
  const formattedTime = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
  console.log(`${formattedTime} [${source}] ${message}`);
}
async function setupVite(app2, server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true
  };
  const vite = await createViteServer({
    ...vite_config_default,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      }
    },
    server: serverOptions,
    appType: "custom"
  });
  app2.use(vite.middlewares);
  app2.use("*", async (req, res, next) => {
    const url = req.originalUrl;
    try {
      const clientTemplate = path2.resolve(
        import.meta.dirname,
        "..",
        "client",
        "index.html"
      );
      let template = await fs.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e);
      next(e);
    }
  });
}
function serveStatic(app2) {
  const distPath = path2.resolve(import.meta.dirname, "public");
  if (!fs.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`
    );
  }
  app2.use(express.static(distPath));
  app2.use("*", (_req, res) => {
    res.sendFile(path2.resolve(distPath, "index.html"));
  });
}

// server/services/websocket.ts
import { WebSocketServer, WebSocket } from "ws";
import { parse } from "url";
var WebSocketService = class {
  constructor() {
    this.clients = /* @__PURE__ */ new Set();
  }
  initialize(server) {
    this.wss = new WebSocketServer({
      server,
      path: "/ws"
    });
    this.wss.on("connection", (ws, request) => {
      console.log("New WebSocket connection");
      const query = parse(request.url || "", true).query;
      const userId = query.userId ? parseInt(query.userId) : void 0;
      ws.userId = userId;
      ws.isAlive = true;
      this.clients.add(ws);
      ws.on("pong", () => {
        ws.isAlive = true;
      });
      ws.on("message", (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(ws, message);
        } catch (error) {
          console.error("Error parsing WebSocket message:", error);
        }
      });
      ws.on("close", () => {
        console.log("WebSocket connection closed");
        this.clients.delete(ws);
      });
      ws.on("error", (error) => {
        console.error("WebSocket error:", error);
        this.clients.delete(ws);
      });
      this.sendToClient(ws, "connected", {
        message: "WebSocket connected successfully",
        userId: ws.userId
      });
    });
    setInterval(() => {
      this.wss?.clients.forEach((ws) => {
        if (ws.isAlive === false) {
          ws.terminate();
          this.clients.delete(ws);
          return;
        }
        ws.isAlive = false;
        ws.ping();
      });
    }, 3e4);
    console.log("WebSocket server initialized on /ws");
  }
  handleMessage(ws, message) {
    const { type, data } = message;
    switch (type) {
      case "ping":
        this.sendToClient(ws, "pong", { timestamp: Date.now() });
        break;
      case "subscribe":
        if (data.channel) {
          console.log(`Client ${ws.userId} subscribed to ${data.channel}`);
        }
        break;
      case "unsubscribe":
        if (data.channel) {
          console.log(`Client ${ws.userId} unsubscribed from ${data.channel}`);
        }
        break;
      default:
        console.log("Unknown WebSocket message type:", type);
    }
  }
  sendToClient(ws, type, data) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({ type, data, timestamp: Date.now() }));
    }
  }
  // Broadcast to all connected clients
  broadcastToAll(type, data) {
    const message = JSON.stringify({ type, data, timestamp: Date.now() });
    this.clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }
  // Send to specific user
  sendToUser(userId, type, data) {
    const message = JSON.stringify({ type, data, timestamp: Date.now() });
    this.clients.forEach((client) => {
      if (client.userId === userId && client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }
  // Send to multiple users
  sendToUsers(userIds, type, data) {
    const message = JSON.stringify({ type, data, timestamp: Date.now() });
    this.clients.forEach((client) => {
      if (client.userId && userIds.includes(client.userId) && client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }
  // Get connected clients count
  getConnectedClientsCount() {
    return this.clients.size;
  }
  // Get connected users
  getConnectedUsers() {
    const users2 = [];
    this.clients.forEach((client) => {
      if (client.userId) {
        users2.push(client.userId);
      }
    });
    return [...new Set(users2)];
  }
};
var webSocketService = new WebSocketService();

// server/services/multiUserWhatsApp.ts
import { makeWASocket, DisconnectReason, useMultiFileAuthState } from "@whiskeysockets/baileys";
import path3 from "path";
import fs2 from "fs";
import QRCode from "qrcode";
import P from "pino";

// server/storage.ts
import { drizzle } from "drizzle-orm/node-postgres";
import { Pool } from "pg";
import { eq, desc, and, or, count } from "drizzle-orm";

// shared/schema.ts
import { pgTable, text, serial, integer, boolean, timestamp, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
var users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  email: text("email"),
  fullName: text("full_name"),
  role: text("role").default("salesperson"),
  // admin, manager, salesperson
  department: text("department").default("sales"),
  isActive: boolean("is_active").default(true),
  isOnline: boolean("is_online").default(false),
  lastLoginAt: timestamp("last_login_at"),
  maxConcurrentChats: integer("max_concurrent_chats").default(10),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});
var whatsappSessions = pgTable("whatsapp_sessions", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  sessionId: text("session_id").notNull().unique(),
  isConnected: boolean("is_connected").default(false),
  phoneNumber: text("phone_number"),
  sessionData: jsonb("session_data"),
  lastConnected: timestamp("last_connected"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});
var leads = pgTable("leads", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  phone: text("phone").notNull(),
  email: text("email"),
  status: text("status").notNull().default("new"),
  // new, contact, proposal, closed, lost
  source: text("source").default("whatsapp"),
  value: integer("value"),
  // in cents
  notes: text("notes"),
  assignedTo: integer("assigned_to").references(() => users.id),
  lastContactAt: timestamp("last_contact_at"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});
var chats = pgTable("chats", {
  id: serial("id").primaryKey(),
  leadId: integer("lead_id").references(() => leads.id),
  assignedUserId: integer("assigned_user_id").references(() => users.id),
  whatsappChatId: text("whatsapp_chat_id").notNull().unique(),
  isGroup: boolean("is_group").default(false),
  status: text("status").default("active"),
  // active, archived, transferred
  lastMessageAt: timestamp("last_message_at"),
  unreadCount: integer("unread_count").default(0),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});
var messages = pgTable("messages", {
  id: serial("id").primaryKey(),
  chatId: integer("chat_id").references(() => chats.id),
  whatsappMessageId: text("whatsapp_message_id").notNull(),
  fromMe: boolean("from_me").default(false),
  content: text("content"),
  messageType: text("message_type").default("text"),
  // text, image, audio, video, document
  timestamp: timestamp("timestamp").notNull(),
  isRead: boolean("is_read").default(false),
  metadata: jsonb("metadata"),
  createdAt: timestamp("created_at").defaultNow()
});
var activities = pgTable("activities", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  leadId: integer("lead_id").references(() => leads.id),
  type: text("type").notNull(),
  // message_sent, message_received, status_change, note_added, chat_transferred, call_made
  description: text("description").notNull(),
  metadata: jsonb("metadata"),
  createdAt: timestamp("created_at").defaultNow()
});
var chatTransfers = pgTable("chat_transfers", {
  id: serial("id").primaryKey(),
  chatId: integer("chat_id").references(() => chats.id),
  fromUserId: integer("from_user_id").references(() => users.id),
  toUserId: integer("to_user_id").references(() => users.id),
  reason: text("reason"),
  notes: text("notes"),
  status: text("status").default("pending"),
  // pending, accepted, rejected, completed
  transferredAt: timestamp("transferred_at").defaultNow(),
  acceptedAt: timestamp("accepted_at")
});
var userSessions = pgTable("user_sessions", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  sessionToken: text("session_token").notNull().unique(),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  isActive: boolean("is_active").default(true),
  lastActivity: timestamp("last_activity").defaultNow(),
  createdAt: timestamp("created_at").defaultNow(),
  expiresAt: timestamp("expires_at")
});
var userAvailability = pgTable("user_availability", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  status: text("status").default("available"),
  // available, busy, break, offline
  maxActiveChats: integer("max_active_chats").default(5),
  currentActiveChats: integer("current_active_chats").default(0),
  autoAssign: boolean("auto_assign").default(true),
  lastStatusChange: timestamp("last_status_change").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});
var kanbanTasks = pgTable("kanban_tasks", {
  id: text("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description"),
  status: text("status").notNull().default("todo"),
  // todo, in_progress, review, done
  priority: text("priority").notNull().default("medium"),
  // low, medium, high, urgent
  clientId: text("client_id"),
  assignedTo: text("assigned_to"),
  dueDate: timestamp("due_date"),
  tags: text("tags").array(),
  position: integer("position").notNull().default(0),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});
var insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  email: true,
  fullName: true,
  role: true
});
var insertLeadSchema = createInsertSchema(leads).pick({
  name: true,
  phone: true,
  email: true,
  status: true,
  source: true,
  value: true,
  notes: true,
  assignedTo: true
});
var insertChatSchema = createInsertSchema(chats).pick({
  leadId: true,
  assignedUserId: true,
  whatsappChatId: true,
  isGroup: true,
  status: true
});
var insertMessageSchema = createInsertSchema(messages).pick({
  chatId: true,
  whatsappMessageId: true,
  fromMe: true,
  content: true,
  messageType: true,
  timestamp: true,
  metadata: true
});
var insertActivitySchema = createInsertSchema(activities).pick({
  userId: true,
  leadId: true,
  type: true,
  description: true,
  metadata: true
});
var insertKanbanTaskSchema = createInsertSchema(kanbanTasks).pick({
  title: true,
  description: true,
  status: true,
  priority: true,
  clientId: true,
  assignedTo: true,
  dueDate: true,
  tags: true,
  position: true
});

// server/storage.ts
var pool = new Pool({
  connectionString: process.env.DATABASE_URL
});
var db = drizzle(pool);
var DatabaseStorage = class {
  // User operations
  async getUser(id) {
    const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
    return result[0];
  }
  async getUsers() {
    return await db.select().from(users).orderBy(users.id);
  }
  async getUserByUsername(username) {
    const result = await db.select().from(users).where(eq(users.username, username)).limit(1);
    return result[0];
  }
  async createUser(user) {
    const result = await db.insert(users).values(user).returning();
    return result[0];
  }
  async updateUser(id, user) {
    const result = await db.update(users).set({
      ...user,
      updatedAt: /* @__PURE__ */ new Date()
    }).where(eq(users.id, id)).returning();
    return result[0];
  }
  // WhatsApp session operations
  async getWhatsappSession(userId) {
    const result = await db.select().from(whatsappSessions).where(eq(whatsappSessions.userId, userId)).limit(1);
    return result[0];
  }
  async createOrUpdateWhatsappSession(userId, sessionData) {
    const existing = await this.getWhatsappSession(userId);
    if (existing) {
      const result = await db.update(whatsappSessions).set({
        sessionData,
        updatedAt: /* @__PURE__ */ new Date()
      }).where(eq(whatsappSessions.userId, userId)).returning();
      return result[0];
    } else {
      const result = await db.insert(whatsappSessions).values({
        userId,
        sessionId: `session_${userId}_${Date.now()}`,
        sessionData,
        isConnected: false
      }).returning();
      return result[0];
    }
  }
  async updateSessionStatus(userId, isConnected, phoneNumber) {
    await db.update(whatsappSessions).set({
      isConnected,
      phoneNumber,
      lastConnected: isConnected ? /* @__PURE__ */ new Date() : void 0,
      updatedAt: /* @__PURE__ */ new Date()
    }).where(eq(whatsappSessions.userId, userId));
  }
  // Lead operations
  async getLeads(assignedTo) {
    if (assignedTo) {
      return await db.select().from(leads).where(eq(leads.assignedTo, assignedTo)).orderBy(desc(leads.updatedAt));
    }
    return await db.select().from(leads).orderBy(desc(leads.updatedAt));
  }
  async getLeadsByStatus(status) {
    return await db.select().from(leads).where(eq(leads.status, status)).orderBy(desc(leads.updatedAt));
  }
  async getLead(id) {
    const result = await db.select().from(leads).where(eq(leads.id, id)).limit(1);
    return result[0];
  }
  async getLeadByPhone(phone) {
    const result = await db.select().from(leads).where(eq(leads.phone, phone)).limit(1);
    return result[0];
  }
  async createLead(lead) {
    const result = await db.insert(leads).values(lead).returning();
    return result[0];
  }
  async updateLead(id, lead) {
    const result = await db.update(leads).set({
      ...lead,
      updatedAt: /* @__PURE__ */ new Date()
    }).where(eq(leads.id, id)).returning();
    return result[0];
  }
  async updateLeadStatus(id, status) {
    const result = await db.update(leads).set({
      status,
      updatedAt: /* @__PURE__ */ new Date()
    }).where(eq(leads.id, id)).returning();
    return result[0];
  }
  async deleteLead(id) {
    const result = await db.delete(leads).where(eq(leads.id, id));
    return (result.rowCount ?? 0) > 0;
  }
  // Chat operations
  async getChats(leadId) {
    if (leadId) {
      return await db.select().from(chats).where(eq(chats.leadId, leadId)).orderBy(desc(chats.lastMessageAt));
    }
    return await db.select().from(chats).orderBy(desc(chats.lastMessageAt));
  }
  async getChat(id) {
    const result = await db.select().from(chats).where(eq(chats.id, id)).limit(1);
    return result[0];
  }
  async getChatByWhatsappId(whatsappChatId) {
    const result = await db.select().from(chats).where(eq(chats.whatsappChatId, whatsappChatId)).limit(1);
    return result[0];
  }
  async createChat(chat) {
    const result = await db.insert(chats).values(chat).returning();
    return result[0];
  }
  async updateChat(id, chat) {
    const result = await db.update(chats).set({
      ...chat,
      updatedAt: /* @__PURE__ */ new Date()
    }).where(eq(chats.id, id)).returning();
    return result[0];
  }
  async updateChatLastMessage(id, timestamp2) {
    await db.update(chats).set({
      lastMessageAt: timestamp2,
      updatedAt: /* @__PURE__ */ new Date()
    }).where(eq(chats.id, id));
  }
  // Message operations
  async getMessages(chatId, limit = 50) {
    return await db.select().from(messages).where(eq(messages.chatId, chatId)).orderBy(desc(messages.timestamp)).limit(limit);
  }
  async getMessage(id) {
    const result = await db.select().from(messages).where(eq(messages.id, id)).limit(1);
    return result[0];
  }
  async getMessageByWhatsappId(whatsappMessageId) {
    const result = await db.select().from(messages).where(eq(messages.whatsappMessageId, whatsappMessageId)).limit(1);
    return result[0];
  }
  async createMessage(message) {
    const result = await db.insert(messages).values(message).returning();
    return result[0];
  }
  async markMessagesAsRead(chatId) {
    await db.update(messages).set({
      isRead: true
    }).where(and(eq(messages.chatId, chatId), eq(messages.isRead, false)));
  }
  // Activity operations
  async getActivities(leadId, limit = 20) {
    if (leadId) {
      return await db.select().from(activities).where(eq(activities.leadId, leadId)).orderBy(desc(activities.createdAt)).limit(limit);
    }
    return await db.select().from(activities).orderBy(desc(activities.createdAt)).limit(limit);
  }
  async createActivity(activity) {
    const result = await db.insert(activities).values(activity).returning();
    return result[0];
  }
  // Dashboard statistics
  async getDashboardStats() {
    const [activeChatCount] = await db.select({ count: count() }).from(chats).where(eq(chats.unreadCount, 0));
    const [totalLeadCount] = await db.select({ count: count() }).from(leads);
    const [pendingTaskCount] = await db.select({ count: count() }).from(leads).where(or(eq(leads.status, "new"), eq(leads.status, "contact")));
    const leadsByStatusQuery = await db.select({
      status: leads.status,
      count: count()
    }).from(leads).groupBy(leads.status);
    const leadsByStatus = leadsByStatusQuery.reduce((acc, item) => {
      acc[item.status] = item.count;
      return acc;
    }, {});
    return {
      activeChats: activeChatCount.count,
      totalLeads: totalLeadCount.count,
      pendingTasks: pendingTaskCount.count,
      leadsByStatus
    };
  }
  // Kanban operations
  async getKanbanTasks() {
    try {
      return await db.select().from(kanbanTasks).orderBy(kanbanTasks.position);
    } catch (error) {
      console.error("Error fetching kanban tasks:", error);
      return [];
    }
  }
  async getKanbanTask(id) {
    try {
      const [task] = await db.select().from(kanbanTasks).where(eq(kanbanTasks.id, id));
      return task;
    } catch (error) {
      console.error("Error fetching kanban task:", error);
      return void 0;
    }
  }
  async createKanbanTask(task) {
    try {
      const [newTask] = await db.insert(kanbanTasks).values(task).returning();
      return newTask;
    } catch (error) {
      console.error("Error creating kanban task:", error);
      throw error;
    }
  }
  async updateKanbanTask(id, task) {
    try {
      const [updatedTask] = await db.update(kanbanTasks).set(task).where(eq(kanbanTasks.id, id)).returning();
      return updatedTask;
    } catch (error) {
      console.error("Error updating kanban task:", error);
      return void 0;
    }
  }
  async deleteKanbanTask(id) {
    try {
      const result = await db.delete(kanbanTasks).where(eq(kanbanTasks.id, id));
      return result.rowCount ? result.rowCount > 0 : false;
    } catch (error) {
      console.error("Error deleting kanban task:", error);
      return false;
    }
  }
};
var storage = new DatabaseStorage();

// server/services/multiUserWhatsApp.ts
var MultiUserWhatsAppService = class {
  // Progressive delays
  constructor() {
    this.sessions = /* @__PURE__ */ new Map();
    this.sessionsDir = process.env.SESSIONS_DIR || path3.join(process.cwd(), "sessions");
    this.maxConcurrentUsers = 30;
    this.reconnectDelays = [1e3, 2e3, 5e3, 1e4, 3e4];
    if (!fs2.existsSync(this.sessionsDir)) {
      fs2.mkdirSync(this.sessionsDir, { recursive: true });
    }
    setInterval(() => this.cleanupInactiveSessions(), 5 * 60 * 1e3);
    console.log(`\u{1F3E0} MULTI-USER WHATSAPP SERVICE INITIALIZED`);
    console.log(`\u{1F4F1} Suporte para ${this.maxConcurrentUsers} vendedores simult\xE2neos`);
  }
  async initializeUserSession(userId) {
    try {
      console.log(`\u{1F464} INICIANDO SESS\xC3O WHATSAPP PARA VENDEDOR ${userId}`);
      if (this.sessions.has(userId)) {
        const session2 = this.sessions.get(userId);
        if (session2.isConnected && session2.sock && session2.phoneNumber) {
          console.log(`\u2705 Vendedor ${userId} j\xE1 conectado: ${session2.phoneNumber}`);
          return {
            success: true,
            phoneNumber: session2.phoneNumber
          };
        } else {
          console.log(`\u{1F504} Vendedor ${userId} tem sess\xE3o mas n\xE3o est\xE1 conectado: isConnected=${session2.isConnected}, sock=${!!session2.sock}, phone=${session2.phoneNumber}`);
        }
      }
      const activeUsers = Array.from(this.sessions.values()).filter((s) => s.isConnected).length;
      if (activeUsers >= this.maxConcurrentUsers) {
        return {
          success: false,
          error: `Limite de ${this.maxConcurrentUsers} usu\xE1rios simult\xE2neos atingido`
        };
      }
      const sessionPath = path3.join(this.sessionsDir, `user_${userId}`);
      if (!fs2.existsSync(sessionPath)) {
        fs2.mkdirSync(sessionPath, { recursive: true });
      }
      const session = {
        userId,
        isConnected: false,
        sessionPath,
        connectionAttempts: 0,
        lastActivity: /* @__PURE__ */ new Date(),
        activeChats: /* @__PURE__ */ new Set()
      };
      this.sessions.set(userId, session);
      await this.createWhatsAppConnection(session);
      await new Promise((resolve) => setTimeout(resolve, 2e3));
      return {
        success: true,
        qr: session.qr,
        phoneNumber: session.phoneNumber
      };
    } catch (error) {
      console.error(`\u274C ERRO AO INICIALIZAR SESS\xC3O VENDEDOR ${userId}:`, error);
      if (this.sessions.has(userId)) {
        const session = this.sessions.get(userId);
        console.log(`\u2705 Sess\xE3o criada para vendedor ${userId}, aguardando QR code...`);
        return {
          success: true,
          qr: session.qr || "QR code sendo gerado no console...",
          phoneNumber: session.phoneNumber
        };
      }
      return {
        success: false,
        error: "Falha ao criar sess\xE3o WhatsApp"
      };
    }
  }
  async createWhatsAppConnection(session) {
    try {
      const { state, saveCreds } = await useMultiFileAuthState(session.sessionPath);
      const sock = makeWASocket({
        auth: state,
        logger: P({ level: "silent" }),
        browser: ["Casa das Camisetas CRM", "Chrome", `Vendedor-${session.userId}`],
        generateHighQualityLinkPreview: true,
        markOnlineOnConnect: true,
        keepAliveIntervalMs: 3e4,
        defaultQueryTimeoutMs: 6e4,
        connectTimeoutMs: 6e4,
        qrTimeout: 6e4
      });
      session.sock = sock;
      sock.ev.on("connection.update", (update) => {
        this.handleConnectionUpdate(update, session);
      });
      sock.ev.on("creds.update", saveCreds);
      sock.ev.on("messages.upsert", (messageUpdate) => {
        this.handleIncomingMessages(session, messageUpdate);
      });
      sock.ev.on("chats.update", (chatUpdates) => {
        this.handleChatUpdates(session, chatUpdates);
      });
      console.log(`\u{1F517} Conex\xE3o WhatsApp criada para vendedor ${session.userId}`);
    } catch (error) {
      console.error(`\u274C Erro ao criar conex\xE3o WhatsApp para vendedor ${session.userId}:`, error);
      this.scheduleReconnection(session);
    }
  }
  async handleConnectionUpdate(update, session) {
    const { connection, lastDisconnect, qr } = update;
    if (qr) {
      console.log(`\u{1F4F1} QR CODE GERADO PARA VENDEDOR ${session.userId}`);
      try {
        const qrDataURL = await QRCode.toDataURL(qr);
        session.qr = qrDataURL;
        webSocketService.sendToUser(session.userId, "whatsapp_qr", {
          qr: qrDataURL,
          userId: session.userId
        });
        console.log(`\u{1F4F1} QR Code gerado para vendedor ${session.userId}`);
        const qrPath = path3.join(this.sessionsDir, `qr_${session.userId}.png`);
        await QRCode.toFile(qrPath, qr, { width: 400 });
      } catch (error) {
        console.error(`\u274C Erro ao gerar QR Code para vendedor ${session.userId}:`, error);
      }
    }
    if (connection === "close") {
      console.log(`\u{1F50C} Conex\xE3o fechada para vendedor ${session.userId}`);
      session.isConnected = false;
      const shouldReconnect = lastDisconnect?.error?.output?.statusCode !== DisconnectReason.loggedOut;
      if (shouldReconnect && session.connectionAttempts < 5) {
        console.log(`\u{1F504} Reagendando reconex\xE3o para vendedor ${session.userId}`);
        this.scheduleReconnection(session);
      } else {
        console.log(`\u274C Vendedor ${session.userId} deslogado ou muitas tentativas de reconex\xE3o`);
        await this.updateUserStatus(session.userId, false);
      }
    } else if (connection === "open") {
      console.log(`\u2705 VENDEDOR ${session.userId} CONECTADO AO WHATSAPP`);
      session.isConnected = true;
      session.connectionAttempts = 0;
      session.lastActivity = /* @__PURE__ */ new Date();
      if (session.sock?.user) {
        session.phoneNumber = session.sock.user.id;
        console.log(`\u{1F4DE} Telefone conectado vendedor ${session.userId}: ${session.phoneNumber}`);
        this.sessions.set(session.userId, { ...session });
        await this.updateUserStatus(session.userId, true, session.phoneNumber);
        webSocketService.sendToUser(session.userId, "whatsapp_connected", {
          userId: session.userId,
          phoneNumber: session.phoneNumber,
          connectedAt: /* @__PURE__ */ new Date(),
          isConnected: true
        });
        console.log(`\u{1F517} Sess\xE3o ativa persistida para vendedor ${session.userId}: isConnected=${session.isConnected}`);
      }
      console.log(`\u{1F4DE} Telefone conectado vendedor ${session.userId}: ${session.phoneNumber}`);
    }
  }
  scheduleReconnection(session) {
    const delay = this.reconnectDelays[Math.min(session.connectionAttempts, this.reconnectDelays.length - 1)];
    session.connectionAttempts++;
    console.log(`\u23F3 Reconectando vendedor ${session.userId} em ${delay}ms (tentativa ${session.connectionAttempts})`);
    setTimeout(() => {
      if (this.sessions.has(session.userId)) {
        this.createWhatsAppConnection(session);
      }
    }, delay);
  }
  async handleIncomingMessages(session, messageUpdate) {
    const { messages: messages2 } = messageUpdate;
    for (const message of messages2) {
      if (message.key.fromMe) continue;
      try {
        session.lastActivity = /* @__PURE__ */ new Date();
        const phoneNumber = message.key.remoteJid?.replace("@c.us", "");
        const chatId = message.key.remoteJid;
        if (!phoneNumber || !chatId) continue;
        session.activeChats.add(chatId);
        let lead = await storage.getLeadByPhone(phoneNumber);
        if (!lead) {
          lead = await storage.createLead({
            name: message.pushName || `Cliente ${phoneNumber}`,
            phone: phoneNumber,
            status: "new",
            assignedTo: session.userId,
            source: "whatsapp"
          });
        }
        let chat = await storage.getChatByWhatsappId(chatId);
        if (!chat) {
          chat = await storage.createChat({
            leadId: lead.id,
            assignedUserId: session.userId,
            whatsappChatId: chatId
          });
        }
        const messageContent = this.extractMessageContent(message);
        await storage.createMessage({
          chatId: chat.id,
          whatsappMessageId: message.key.id,
          content: messageContent.text,
          fromMe: false,
          messageType: messageContent.type,
          timestamp: new Date(message.messageTimestamp * 1e3),
          metadata: messageContent.metadata
        });
        await storage.updateChatLastMessage(chat.id, new Date(message.messageTimestamp * 1e3));
        await storage.createActivity({
          userId: session.userId,
          leadId: lead.id,
          type: "message_received",
          description: `Nova mensagem: ${messageContent.text.substring(0, 50)}...`
        });
        webSocketService.broadcastToAll("new_message", {
          chatId: chat.id,
          leadId: lead.id,
          assignedUserId: session.userId,
          message: messageContent.text,
          messageType: messageContent.type,
          timestamp: new Date(message.messageTimestamp * 1e3),
          fromPhone: phoneNumber
        });
        console.log(`\u{1F4E8} Mensagem recebida por vendedor ${session.userId} de ${phoneNumber}: ${messageContent.text.substring(0, 30)}...`);
      } catch (error) {
        console.error(`\u274C Erro ao processar mensagem para vendedor ${session.userId}:`, error);
      }
    }
  }
  extractMessageContent(message) {
    if (message.message?.conversation) {
      return { text: message.message.conversation, type: "text" };
    }
    if (message.message?.extendedTextMessage?.text) {
      return {
        text: message.message.extendedTextMessage.text,
        type: "text",
        metadata: { extended: true }
      };
    }
    if (message.message?.imageMessage) {
      return {
        text: message.message.imageMessage.caption || "[Imagem]",
        type: "image",
        metadata: {
          mimetype: message.message.imageMessage.mimetype,
          fileLength: message.message.imageMessage.fileLength
        }
      };
    }
    if (message.message?.documentMessage) {
      return {
        text: `[Documento: ${message.message.documentMessage.fileName || "arquivo"}]`,
        type: "document",
        metadata: {
          fileName: message.message.documentMessage.fileName,
          mimetype: message.message.documentMessage.mimetype
        }
      };
    }
    return { text: "[M\xEDdia n\xE3o suportada]", type: "unknown" };
  }
  async handleChatUpdates(session, chatUpdates) {
    for (const chatUpdate of chatUpdates) {
      if (chatUpdate.unreadCount !== void 0) {
        try {
          const chat = await storage.getChatByWhatsappId(chatUpdate.id);
          if (chat && chat.assignedUserId === session.userId) {
            console.log(`\u{1F522} Chat ${chatUpdate.id} - mensagens n\xE3o lidas: ${chatUpdate.unreadCount}`);
          }
        } catch (error) {
          console.error("Erro ao atualizar chat:", error);
        }
      }
    }
  }
  async sendMessage(userId, chatId, content, messageType = "text") {
    try {
      const session = this.sessions.get(userId);
      if (!session || !session.sock || !session.isConnected) {
        return { success: false, error: "Vendedor n\xE3o est\xE1 conectado ao WhatsApp" };
      }
      console.log(`\u{1F4E4} Enviando mensagem de vendedor ${userId} para ${chatId}: ${content.substring(0, 30)}...`);
      let messagePayload;
      switch (messageType) {
        case "text":
          messagePayload = { text: content };
          break;
        default:
          messagePayload = { text: content };
      }
      await session.sock.sendMessage(chatId, messagePayload);
      session.lastActivity = /* @__PURE__ */ new Date();
      session.activeChats.add(chatId);
      return { success: true };
    } catch (error) {
      console.error(`\u274C Erro ao enviar mensagem de vendedor ${userId}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Erro desconhecido"
      };
    }
  }
  async disconnectUser(userId) {
    try {
      const session = this.sessions.get(userId);
      if (session) {
        if (session.sock) {
          await session.sock.logout();
        }
        this.sessions.delete(userId);
        await this.updateUserStatus(userId, false);
        console.log(`\u{1F50C} Vendedor ${userId} desconectado do WhatsApp`);
        webSocketService.sendToUser(userId, "whatsapp_disconnected", {
          userId,
          disconnectedAt: /* @__PURE__ */ new Date()
        });
      }
    } catch (error) {
      console.error(`\u274C Erro ao desconectar vendedor ${userId}:`, error);
    }
  }
  getUserStatus(userId) {
    const session = this.sessions.get(userId);
    return {
      isConnected: session?.isConnected || false,
      phoneNumber: session?.phoneNumber,
      activeChats: session?.activeChats.size || 0
    };
  }
  getAllUsersStatus() {
    const status = {};
    for (const [userId, session] of this.sessions) {
      status[userId] = {
        isConnected: session.isConnected,
        phoneNumber: session.phoneNumber,
        activeChats: session.activeChats.size,
        lastActivity: session.lastActivity
      };
    }
    return status;
  }
  getSystemMetrics() {
    const connectedUsers = Array.from(this.sessions.values()).filter((s) => s.isConnected).length;
    const totalSessions = this.sessions.size;
    const totalActiveChats = Array.from(this.sessions.values()).reduce((sum, s) => sum + s.activeChats.size, 0);
    return {
      connectedUsers,
      totalSessions,
      totalActiveChats,
      maxConcurrentUsers: this.maxConcurrentUsers,
      uptime: process.uptime()
    };
  }
  async updateUserStatus(userId, isConnected, phoneNumber) {
    try {
      await storage.updateSessionStatus(userId, isConnected, phoneNumber);
    } catch (error) {
      console.error(`\u274C Erro ao atualizar status do vendedor ${userId}:`, error);
    }
  }
  cleanupInactiveSessions() {
    const now = /* @__PURE__ */ new Date();
    const inactiveThreshold = 30 * 60 * 1e3;
    for (const [userId, session] of this.sessions) {
      if (!session.isConnected && now.getTime() - session.lastActivity.getTime() > inactiveThreshold) {
        console.log(`\u{1F9F9} Limpando sess\xE3o inativa do vendedor ${userId}`);
        this.sessions.delete(userId);
      }
    }
  }
  async transferChat(chatId, fromUserId, toUserId, reason) {
    try {
      const fromSession = this.sessions.get(fromUserId);
      const toSession = this.sessions.get(toUserId);
      if (!fromSession?.isConnected) {
        return { success: false, error: "Vendedor de origem n\xE3o est\xE1 conectado" };
      }
      if (!toSession?.isConnected) {
        return { success: false, error: "Vendedor de destino n\xE3o est\xE1 conectado" };
      }
      const chat = await storage.getChat(chatId);
      if (!chat) {
        return { success: false, error: "Chat n\xE3o encontrado" };
      }
      await storage.updateChat(chatId, { assignedUserId: toUserId });
      await storage.createActivity({
        userId: fromUserId,
        leadId: chat.leadId,
        type: "chat_transferred",
        description: `Chat transferido para vendedor ${toUserId}. Motivo: ${reason || "N\xE3o informado"}`
      });
      webSocketService.sendToUser(fromUserId, "chat_transferred_out", {
        chatId,
        toUserId,
        reason
      });
      webSocketService.sendToUser(toUserId, "chat_transferred_in", {
        chatId,
        fromUserId,
        reason
      });
      console.log(`\u{1F504} Chat ${chatId} transferido de vendedor ${fromUserId} para vendedor ${toUserId}`);
      return { success: true };
    } catch (error) {
      console.error("\u274C Erro ao transferir chat:", error);
      return { success: false, error: error instanceof Error ? error.message : "Erro desconhecido" };
    }
  }
  // Baileys Real WhatsApp Methods - NO MOCK DATA
  async getChats(userId) {
    try {
      const session = this.sessions.get(userId);
      if (!session?.sock || !session.isConnected) {
        console.log(`\u274C Sess\xE3o n\xE3o dispon\xEDvel para vendedor ${userId}: sock=${!!session?.sock}, connected=${session?.isConnected}`);
        return [];
      }
      console.log(`\u{1F50D} Buscando chats reais do Baileys para vendedor ${userId}...`);
      console.log(`\u{1F504} For\xE7ando sincroniza\xE7\xE3o com WhatsApp para vendedor ${userId}...`);
      let chats2 = session.sock.store?.chats;
      if (!chats2 || chats2.size === 0) {
        console.log(`\u{1F4DD} Store vazio, tentando m\xE9todos alternativos...`);
        try {
          console.log(`\u{1F50D} Tentando buscar conversas recentes...`);
          const contactIds = Object.keys(session.sock.store?.contacts || {});
          console.log(`\u{1F465} Encontrados ${contactIds.length} contatos no store`);
          if (contactIds.length > 0) {
            const chatPromises = contactIds.slice(0, 10).map(async (contactId) => {
              try {
                const messages2 = await session.sock.loadMessages(contactId, 1);
                if (messages2 && messages2.length > 0) {
                  return {
                    id: contactId,
                    name: session.sock.store?.contacts?.[contactId]?.name || contactId.split("@")[0],
                    lastMessage: {
                      body: messages2[0].message?.conversation || "Mensagem",
                      timestamp: messages2[0].messageTimestamp * 1e3,
                      fromMe: messages2[0].key?.fromMe || false
                    },
                    unreadCount: 0,
                    isGroup: contactId.endsWith("@g.us"),
                    timestamp: Date.now()
                  };
                }
              } catch (e) {
                return null;
              }
            });
            const chatResults = await Promise.all(chatPromises);
            const validChats = chatResults.filter((chat) => chat !== null);
            console.log(`\u{1F4AC} Criados ${validChats.length} chats a partir dos contatos`);
            if (validChats.length > 0) {
              return validChats;
            }
          }
        } catch (loadError) {
          console.log(`\u26A0\uFE0F Erro ao carregar mensagens:`, loadError.message);
        }
        console.log(`\u{1F527} Criando chat de teste com seu n\xFAmero WhatsApp...`);
        return [{
          id: session.phoneNumber || "<EMAIL>",
          name: "Seu WhatsApp",
          lastMessage: {
            body: "Chat conectado via Baileys",
            timestamp: Date.now(),
            fromMe: true
          },
          unreadCount: 0,
          isGroup: false,
          timestamp: Date.now()
        }];
      }
      const chatArray = Array.from(chats2.values());
      console.log(`\u{1F4CA} Encontrados ${chatArray.length} chats no store do Baileys`);
      return chatArray.map((chat) => ({
        id: chat.id,
        name: chat.name || chat.subject || chat.pushName || chat.id.split("@")[0],
        lastMessage: chat.lastMessage ? {
          body: chat.lastMessage.message?.conversation || chat.lastMessage.message?.extendedTextMessage?.text || "Mensagem de m\xEDdia",
          timestamp: (chat.lastMessage.messageTimestamp || Date.now() / 1e3) * 1e3,
          fromMe: chat.lastMessage.key?.fromMe || false
        } : null,
        unreadCount: chat.unreadCount || 0,
        isGroup: chat.id.endsWith("@g.us"),
        profilePic: null,
        timestamp: (chat.conversationTimestamp || Date.now() / 1e3) * 1e3,
        isPinned: chat.pin || false,
        isMuted: chat.mute > Date.now(),
        isArchived: chat.archive || false
      }));
    } catch (error) {
      console.error(`\u274C Erro ao obter chats reais do vendedor ${userId}:`, error);
      return [];
    }
  }
  async getMessages(userId, chatId, limit = 50) {
    try {
      const session = this.sessions.get(userId);
      if (!session?.sock || !session.isConnected) {
        return [];
      }
      const messages2 = await session.sock.store?.messages?.[chatId] || [];
      return Array.from(messages2.values()).slice(-limit).map((msg) => ({
        id: msg.key.id,
        body: msg.message?.conversation || msg.message?.extendedTextMessage?.text || "",
        fromMe: msg.key.fromMe,
        timestamp: msg.messageTimestamp * 1e3,
        type: this.getMessageType(msg.message),
        hasMedia: this.hasMedia(msg.message),
        status: msg.status || 0
      }));
    } catch (error) {
      console.error(`Erro ao obter mensagens reais:`, error);
      return [];
    }
  }
  getMessageType(message) {
    if (message?.conversation) return "text";
    if (message?.imageMessage) return "image";
    if (message?.videoMessage) return "video";
    if (message?.audioMessage) return "audio";
    if (message?.documentMessage) return "document";
    if (message?.stickerMessage) return "sticker";
    if (message?.locationMessage) return "location";
    return "text";
  }
  hasMedia(message) {
    return !!(message?.imageMessage || message?.videoMessage || message?.audioMessage || message?.documentMessage || message?.stickerMessage);
  }
  async sendTextMessage(userId, chatId, text2) {
    try {
      const session = this.sessions.get(userId);
      if (!session?.sock || !session.isConnected) {
        return { success: false, error: "WhatsApp n\xE3o conectado" };
      }
      await session.sock.sendMessage(chatId, { text: text2 });
      console.log(`Mensagem real enviada via Baileys pelo vendedor ${userId}`);
      return { success: true };
    } catch (error) {
      console.error("Erro ao enviar mensagem real:", error);
      return { success: false, error: "Falha ao enviar mensagem" };
    }
  }
  async sendMediaMessage(userId, chatId, mediaBuffer, mediaType, caption) {
    try {
      const session = this.sessions.get(userId);
      if (!session?.sock || !session.isConnected) {
        return { success: false, error: "WhatsApp n\xE3o conectado" };
      }
      const mediaMessage = { caption };
      switch (mediaType) {
        case "image":
          mediaMessage.image = mediaBuffer;
          break;
        case "video":
          mediaMessage.video = mediaBuffer;
          break;
        case "audio":
          mediaMessage.audio = mediaBuffer;
          mediaMessage.mimetype = "audio/ogg; codecs=opus";
          break;
        case "document":
          mediaMessage.document = mediaBuffer;
          break;
        default:
          return { success: false, error: "Tipo de m\xEDdia n\xE3o suportado" };
      }
      await session.sock.sendMessage(chatId, mediaMessage);
      console.log(`M\xEDdia real enviada via Baileys: ${mediaType}`);
      return { success: true };
    } catch (error) {
      console.error("Erro ao enviar m\xEDdia real:", error);
      return { success: false, error: "Falha ao enviar m\xEDdia" };
    }
  }
  async markAsRead(userId, chatId) {
    try {
      const session = this.sessions.get(userId);
      if (!session?.sock || !session.isConnected) {
        return { success: false, error: "WhatsApp n\xE3o conectado" };
      }
      const messages2 = await session.sock.store?.messages?.[chatId];
      if (messages2 && messages2.size > 0) {
        const lastMessage = Array.from(messages2.values()).pop();
        if (lastMessage) {
          await session.sock.readMessages([lastMessage.key]);
        }
      }
      return { success: true };
    } catch (error) {
      console.error("Erro ao marcar como lida:", error);
      return { success: false, error: "Falha ao marcar como lida" };
    }
  }
  async getContactInfo(userId, contactId) {
    try {
      const session = this.sessions.get(userId);
      if (!session?.sock || !session.isConnected) {
        return null;
      }
      const contact = session.sock.store?.contacts?.[contactId];
      const profilePic = await this.getProfilePicture(userId, contactId);
      return {
        id: contactId,
        name: contact?.name || contact?.pushName || contactId.split("@")[0],
        pushName: contact?.pushName,
        profilePic: profilePic?.url,
        isMyContact: !!contact?.name
      };
    } catch (error) {
      console.error("Erro ao obter info do contato:", error);
      return null;
    }
  }
  async getProfilePicture(userId, contactId) {
    try {
      const session = this.sessions.get(userId);
      if (!session?.sock || !session.isConnected) {
        return null;
      }
      const url = await session.sock.profilePictureUrl(contactId, "image");
      return { url };
    } catch (error) {
      return null;
    }
  }
  async sendTyping(userId, chatId) {
    try {
      const session = this.sessions.get(userId);
      if (!session?.sock || !session.isConnected) {
        return { success: false, error: "WhatsApp n\xE3o conectado" };
      }
      await session.sock.sendPresenceUpdate("composing", chatId);
      return { success: true };
    } catch (error) {
      console.error("Erro ao enviar indicador de digita\xE7\xE3o:", error);
      return { success: false, error: "Falha ao enviar indicador" };
    }
  }
  async getGroupInfo(userId, groupId) {
    try {
      const session = this.sessions.get(userId);
      if (!session?.sock || !session.isConnected) {
        return null;
      }
      const groupMetadata = await session.sock.groupMetadata(groupId);
      return {
        id: groupId,
        name: groupMetadata.subject,
        description: groupMetadata.desc,
        participants: groupMetadata.participants.map((p) => ({
          id: p.id,
          isAdmin: p.admin === "admin" || p.admin === "superadmin",
          isSuperAdmin: p.admin === "superadmin"
        })),
        createdAt: new Date(groupMetadata.creation * 1e3),
        owner: groupMetadata.owner
      };
    } catch (error) {
      console.error("Erro ao obter info do grupo:", error);
      return null;
    }
  }
};
var multiUserWhatsAppService = new MultiUserWhatsAppService();

// server/routes.ts
async function registerRoutes(app2) {
  const server = createServer(app2);
  webSocketService.initialize(server);
  app2.get("/api/health", (_req, res) => {
    res.json({ status: "healthy", timestamp: (/* @__PURE__ */ new Date()).toISOString() });
  });
  app2.get("/api/whatsapp/clients", (_req, res) => {
    try {
      const clients = multiUserWhatsAppService.getAllUsersStatus();
      res.json(clients);
    } catch (error) {
      console.error("Error fetching clients:", error);
      res.status(500).json({ error: "Failed to fetch clients" });
    }
  });
  app2.get("/api/whatsapp/status/:userId", (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const status = multiUserWhatsAppService.getUserStatus(userId);
      res.json(status);
    } catch (error) {
      console.error("Error fetching status:", error);
      res.status(500).json({ error: "Failed to fetch status" });
    }
  });
  app2.post("/api/whatsapp/init/:userId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const result = await multiUserWhatsAppService.initializeUserSession(userId);
      res.json(result);
    } catch (error) {
      console.error("Error initializing session:", error);
      res.status(500).json({ error: "Failed to initialize session" });
    }
  });
  app2.delete("/api/whatsapp/disconnect/:userId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      await multiUserWhatsAppService.disconnectUser(userId);
      res.json({ success: true });
    } catch (error) {
      console.error("Error disconnecting:", error);
      res.status(500).json({ error: "Failed to disconnect" });
    }
  });
  app2.get("/api/whatsapp/chats/:userId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const chats2 = await multiUserWhatsAppService.getChats(userId);
      res.json(chats2);
    } catch (error) {
      console.error("Error fetching chats:", error);
      res.status(500).json({ error: "Failed to fetch chats" });
    }
  });
  app2.get("/api/whatsapp/messages/:userId/:chatId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const chatId = req.params.chatId;
      const limit = parseInt(req.query.limit) || 50;
      const messages2 = await multiUserWhatsAppService.getMessages(userId, chatId, limit);
      res.json(messages2);
    } catch (error) {
      console.error("Error fetching messages:", error);
      res.status(500).json({ error: "Failed to fetch messages" });
    }
  });
  app2.post("/api/whatsapp/send-message/:userId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const { chatId, message } = req.body;
      const result = await multiUserWhatsAppService.sendTextMessage(userId, chatId, message);
      res.json(result);
    } catch (error) {
      console.error("Error sending message:", error);
      res.status(500).json({ error: "Failed to send message" });
    }
  });
  app2.patch("/api/whatsapp/mark-read/:userId/:chatId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const chatId = req.params.chatId;
      const result = await multiUserWhatsAppService.markAsRead(userId, chatId);
      res.json(result);
    } catch (error) {
      console.error("Error marking as read:", error);
      res.status(500).json({ error: "Failed to mark as read" });
    }
  });
  app2.post("/api/whatsapp/typing/:userId/:chatId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const chatId = req.params.chatId;
      const result = await multiUserWhatsAppService.sendTyping(userId, chatId);
      res.json(result);
    } catch (error) {
      console.error("Error sending typing:", error);
      res.status(500).json({ error: "Failed to send typing indicator" });
    }
  });
  app2.get("/api/whatsapp/contact/:userId/:contactId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const contactId = req.params.contactId;
      const contact = await multiUserWhatsAppService.getContactInfo(userId, contactId);
      res.json(contact);
    } catch (error) {
      console.error("Error fetching contact:", error);
      res.status(500).json({ error: "Failed to fetch contact info" });
    }
  });
  app2.get("/api/leads", async (req, res) => {
    try {
      const assignedTo = req.query.assignedTo ? parseInt(req.query.assignedTo) : void 0;
      const leads2 = await storage.getLeads(assignedTo);
      res.json(leads2);
    } catch (error) {
      console.error("Error fetching leads:", error);
      res.status(500).json({ error: "Failed to fetch leads" });
    }
  });
  app2.get("/api/leads/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const lead = await storage.getLead(id);
      if (!lead) {
        return res.status(404).json({ error: "Lead not found" });
      }
      res.json(lead);
    } catch (error) {
      console.error("Error fetching lead:", error);
      res.status(500).json({ error: "Failed to fetch lead" });
    }
  });
  app2.post("/api/leads", async (req, res) => {
    try {
      const validatedData = insertLeadSchema.parse(req.body);
      const lead = await storage.createLead(validatedData);
      res.status(201).json(lead);
    } catch (error) {
      console.error("Error creating lead:", error);
      res.status(500).json({ error: "Failed to create lead" });
    }
  });
  app2.patch("/api/leads/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const validatedData = insertLeadSchema.partial().parse(req.body);
      const lead = await storage.updateLead(id, validatedData);
      if (!lead) {
        return res.status(404).json({ error: "Lead not found" });
      }
      res.json(lead);
    } catch (error) {
      console.error("Error updating lead:", error);
      res.status(500).json({ error: "Failed to update lead" });
    }
  });
  app2.delete("/api/leads/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteLead(id);
      if (!success) {
        return res.status(404).json({ error: "Lead not found" });
      }
      res.json({ success: true });
    } catch (error) {
      console.error("Error deleting lead:", error);
      res.status(500).json({ error: "Failed to delete lead" });
    }
  });
  app2.get("/api/kanban/tasks", async (req, res) => {
    try {
      const tasks = await storage.getKanbanTasks();
      res.json(tasks);
    } catch (error) {
      console.error("Error fetching kanban tasks:", error);
      res.status(500).json({ error: "Failed to fetch tasks" });
    }
  });
  app2.get("/api/kanban/tasks/:id", async (req, res) => {
    try {
      const id = req.params.id;
      const task = await storage.getKanbanTask(id);
      if (!task) {
        return res.status(404).json({ error: "Task not found" });
      }
      res.json(task);
    } catch (error) {
      console.error("Error fetching kanban task:", error);
      res.status(500).json({ error: "Failed to fetch task" });
    }
  });
  app2.post("/api/kanban/tasks", async (req, res) => {
    try {
      const taskId = `task_${Date.now()}`;
      const validatedData = insertKanbanTaskSchema.parse(req.body);
      const taskData = {
        ...validatedData,
        id: taskId,
        position: 0,
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      };
      const task = await storage.createKanbanTask(taskData);
      res.status(201).json(task);
    } catch (error) {
      console.error("Error creating kanban task:", error);
      res.status(500).json({ error: "Failed to create task" });
    }
  });
  app2.patch("/api/kanban/tasks/:id", async (req, res) => {
    try {
      const id = req.params.id;
      const validatedData = insertKanbanTaskSchema.partial().parse(req.body);
      const updatedTask = await storage.updateKanbanTask(id, validatedData);
      if (!updatedTask) {
        return res.status(404).json({ error: "Task not found" });
      }
      res.json(updatedTask);
    } catch (error) {
      console.error("Error updating kanban task:", error);
      res.status(500).json({ error: "Failed to update task" });
    }
  });
  app2.delete("/api/kanban/tasks/:id", async (req, res) => {
    try {
      const id = req.params.id;
      const success = await storage.deleteKanbanTask(id);
      if (!success) {
        return res.status(404).json({ error: "Task not found" });
      }
      res.json({ success: true });
    } catch (error) {
      console.error("Error deleting kanban task:", error);
      res.status(500).json({ error: "Failed to delete task" });
    }
  });
  app2.get("/api/dashboard/stats", async (req, res) => {
    try {
      const stats = await storage.getDashboardStats();
      res.json(stats);
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
      res.status(500).json({ error: "Failed to fetch dashboard stats" });
    }
  });
  if (process.env.NODE_ENV === "development") {
    await setupVite(app2, server);
  } else {
    serveStatic(app2);
  }
  return server;
}

// server/index.ts
var app = express2();
app.use(express2.json());
app.use(express2.urlencoded({ extended: false }));
app.use((req, res, next) => {
  const start = Date.now();
  const path4 = req.path;
  let capturedJsonResponse = void 0;
  const originalResJson = res.json;
  res.json = function(bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };
  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path4.startsWith("/api")) {
      let logLine = `${req.method} ${path4} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }
      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "\u2026";
      }
      log(logLine);
    }
  });
  next();
});
(async () => {
  const server = await registerRoutes(app);
  app.use((err, _req, res, _next) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    res.status(status).json({ message });
    throw err;
  });
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }
  const port = 5e3;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true
  }, () => {
    log(`serving on port ${port}`);
  });
})();
