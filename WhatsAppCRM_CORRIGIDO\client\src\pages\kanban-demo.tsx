import { useState } from "react";
import KanbanBoard from "@/components/KanbanBoard";
import { ArrowLeft, Users, Calendar, Flag } from "lucide-react";
import { useLocation } from "wouter";

// Mock contact data for demonstration
const mockContacts = [
  {
    id: "5511999887766",
    name: "<PERSON>",
    phone: "5511999887766"
  },
  {
    id: "5511888776655", 
    name: "<PERSON>",
    phone: "5511888776655"
  },
  {
    id: "5511777665544",
    name: "Ana Costa", 
    phone: "5511777665544"
  }
];

export default function KanbanDemo() {
  const [selectedContact, setSelectedContact] = useState(mockContacts[0]);
  const [, setLocation] = useLocation();

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setLocation('/')}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </button>
          <div className="flex-1">
            <h1 className="text-xl font-semibold text-gray-900">
              Kanban Board - Gerenciamento de Tarefas
            </h1>
            <p className="text-sm text-gray-500">
              Arraste e solte tarefas entre as colunas: A Fazer, Em Andamento e Concluído
            </p>
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Contact Selector Sidebar */}
        <div className="w-80 bg-white border-r border-gray-200">
          <div className="p-4 border-b border-gray-200">
            <h3 className="font-medium text-gray-900 mb-3">Selecione um Contato</h3>
            <p className="text-sm text-gray-500">
              Escolha um contato para gerenciar suas tarefas
            </p>
          </div>
          
          <div className="overflow-y-auto">
            {mockContacts.map((contact) => (
              <button
                key={contact.id}
                onClick={() => setSelectedContact(contact)}
                className={`w-full p-4 text-left hover:bg-gray-50 border-b border-gray-100 transition-colors ${
                  selectedContact.id === contact.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {contact.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </span>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 truncate">{contact.name}</h4>
                    <p className="text-sm text-gray-500 truncate">{contact.phone}</p>
                  </div>
                </div>
              </button>
            ))}
          </div>

          {/* Feature Highlights */}
          <div className="p-4 border-t border-gray-200 mt-auto">
            <h4 className="font-medium text-gray-900 mb-3">Funcionalidades</h4>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4 text-blue-500" />
                <span>Tarefas por contato</span>
              </div>
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-green-500" />
                <span>Datas de vencimento</span>
              </div>
              <div className="flex items-center space-x-2">
                <Flag className="w-4 h-4 text-red-500" />
                <span>Níveis de prioridade</span>
              </div>
            </div>
          </div>
        </div>

        {/* Kanban Board Area */}
        <div className="flex-1">
          <KanbanBoard 
            contactPhone={selectedContact.phone}
            contactName={selectedContact.name}
          />
        </div>
      </div>

      {/* Demo Information Footer */}
      <div className="bg-blue-50 border-t border-blue-200 px-6 py-3">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          <p className="text-sm text-blue-700">
            <strong>Demo Kanban:</strong> Arraste as tarefas entre as colunas para alterar o status. 
            Use React Beautiful DnD para uma experiência fluida de drag-and-drop. 
            Crie novas tarefas com diferentes prioridades e datas de vencimento.
          </p>
        </div>
      </div>
    </div>
  );
}