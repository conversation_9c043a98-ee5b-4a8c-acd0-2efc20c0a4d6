import { useState, useEffect } from "react";
import { Search, X, Clock, Star, MessageCircle } from "lucide-react";
import { useQuery } from "@tanstack/react-query";

interface SearchMessagesProps {
  isOpen: boolean;
  onClose: () => void;
  whatsappNumber: string;
  onSelectMessage: (chatId: string, messageId: string) => void;
}

export default function SearchMessages({ 
  isOpen, 
  onClose, 
  whatsappNumber, 
  onSelectMessage 
}: SearchMessagesProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const performSearch = async (query: string) => {
    if (!query.trim() || query.length < 2) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const response = await fetch(`/api/search/messages?q=${encodeURIComponent(query)}&whatsappNumber=${whatsappNumber}`);
      if (response.ok) {
        const results = await response.json();
        setSearchResults(results);
      }
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setIsSearching(false);
    }
  };

  useEffect(() => {
    const debounced = setTimeout(() => {
      performSearch(searchQuery);
    }, 500);

    return () => clearTimeout(debounced);
  }, [searchQuery, whatsappNumber]);

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('pt-BR');
  };

  const highlightText = (text: string, query: string) => {
    if (!query.trim()) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? 
        <mark key={index} className="bg-yellow-200 px-1 rounded">{part}</mark> : 
        part
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-3xl max-h-[80vh] mx-4 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <Search className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold">Buscar Mensagens</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Search Input */}
        <div className="p-6 border-b">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Digite para buscar mensagens..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 text-lg"
              autoFocus
            />
            {isSearching && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
              </div>
            )}
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Digite pelo menos 2 caracteres para buscar
          </p>
        </div>

        {/* Search Results */}
        <div className="flex-1 overflow-y-auto p-6">
          {!searchQuery.trim() ? (
            <div className="text-center py-12 text-gray-500">
              <Search className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <p className="text-lg">Busque por mensagens, contatos ou palavras-chave</p>
              <p className="text-sm">Digite no campo acima para começar</p>
            </div>
          ) : searchQuery.length < 2 ? (
            <div className="text-center py-12 text-gray-500">
              <p>Digite pelo menos 2 caracteres para buscar</p>
            </div>
          ) : isSearching ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Buscando mensagens...</p>
            </div>
          ) : searchResults.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <MessageCircle className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <p className="text-lg">Nenhuma mensagem encontrada</p>
              <p className="text-sm">Tente usar termos diferentes ou verifique a ortografia</p>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="text-sm text-gray-600 mb-4">
                {searchResults.length} resultado{searchResults.length !== 1 ? 's' : ''} encontrado{searchResults.length !== 1 ? 's' : ''}
              </div>
              
              {searchResults.map((result: any) => (
                <div
                  key={result.id}
                  onClick={() => {
                    onSelectMessage(result.clientPhone, result.id);
                    onClose();
                  }}
                  className="border border-gray-200 rounded-lg p-4 hover:shadow-md hover:border-blue-300 cursor-pointer transition-all"
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 text-sm font-medium">
                          {result.contactName ? result.contactName[0] : result.clientPhone.slice(-2)}
                        </span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {result.contactName || result.clientPhone}
                        </div>
                        <div className="text-xs text-gray-500 flex items-center space-x-2">
                          <Clock className="w-3 h-3" />
                          <span>{formatDate(result.timestamp)}</span>
                          {result.starred && <Star className="w-3 h-3 text-yellow-500 fill-current" />}
                          {result.fromMe && <span className="text-green-600">Você</span>}
                        </div>
                      </div>
                    </div>
                    <div className="text-xs text-gray-400">
                      {result.mediaType !== 'text' && (
                        <span className="bg-gray-100 px-2 py-1 rounded">
                          {result.mediaType === 'image' ? '📷' :
                           result.mediaType === 'video' ? '🎥' :
                           result.mediaType === 'audio' ? '🎵' : '📄'}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-gray-700 text-sm">
                    {result.content ? (
                      <span>
                        {highlightText(
                          result.content.length > 150 
                            ? result.content.substring(0, 150) + '...' 
                            : result.content, 
                          searchQuery
                        )}
                      </span>
                    ) : (
                      <span className="italic text-gray-500">
                        {result.mediaType === 'image' ? 'Imagem' :
                         result.mediaType === 'video' ? 'Vídeo' :
                         result.mediaType === 'audio' ? 'Áudio' :
                         result.mediaType === 'document' ? 'Documento' : 'Mídia'}
                      </span>
                    )}
                  </div>

                  {result.replyToContent && (
                    <div className="mt-2 pl-3 border-l-2 border-gray-300 text-xs text-gray-500">
                      <div className="font-medium">Respondendo:</div>
                      <div>{result.replyToContent.substring(0, 80)}...</div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}