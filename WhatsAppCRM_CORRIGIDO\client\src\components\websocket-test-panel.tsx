import { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Wifi, 
  WifiOff, 
  Send, 
  TestTube, 
  Activity, 
  CheckCircle, 
  XCircle, 
  Clock,
  RotateCcw
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ConnectionMetrics {
  totalConnections: number;
  activeConnections: number;
  reconnections: number;
  failedConnections: number;
  averageLatency: number;
  lastReset: Date;
}

interface ConnectionStatus {
  userId: string;
  isStable: boolean;
  connectionAttempts: number;
  lastActivity: Date;
  metrics: ConnectionMetrics;
  pendingMessages: Array<{
    id: string;
    status: string;
    attempts: number;
    chatId: string;
  }>;
}

interface TestResult {
  id: string;
  type: 'connection' | 'message' | 'latency';
  success: boolean;
  latency?: number;
  message: string;
  timestamp: Date;
}

export function WebSocketTestPanel() {
  const [socket, setSocket] = useState<any>(null);
  const [connected, setConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [testMessage, setTestMessage] = useState('Teste de mensagem WebSocket');
  const [latencyHistory, setLatencyHistory] = useState<number[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [autoReconnect, setAutoReconnect] = useState(true);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  
  const { toast } = useToast();
  const socketRef = useRef<any>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const addTestResult = (result: Omit<TestResult, 'id' | 'timestamp'>) => {
    const newResult: TestResult = {
      ...result,
      id: Date.now().toString(),
      timestamp: new Date()
    };
    setTestResults(prev => [newResult, ...prev.slice(0, 49)]); // Keep last 50 results
  };

  const connectWebSocket = () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
    }

    console.log('[WebSocket Test] Iniciando conexão...');
    
    // Import socket.io-client dynamically
    import('socket.io-client').then(({ io }) => {
      const newSocket = io('/', {
        transports: ['websocket', 'polling'],
        timeout: 10000,
        autoConnect: true
      });

      newSocket.on('connect', () => {
        console.log('[WebSocket Test] Conectado:', newSocket.id);
        setConnected(true);
        setReconnectAttempts(0);
        
        // Authenticate user
        newSocket.emit('authenticate', {
          userId: 'test-user-' + Date.now(),
          whatsappNumber: '+5511999999999'
        });

        addTestResult({
          type: 'connection',
          success: true,
          message: `Conexão estabelecida - ID: ${newSocket.id}`
        });

        // Request connection status
        newSocket.emit('status:request');
      });

      newSocket.on('disconnect', (reason) => {
        console.log('[WebSocket Test] Desconectado:', reason);
        setConnected(false);
        
        addTestResult({
          type: 'connection',
          success: false,
          message: `Desconectado: ${reason}`
        });

        if (autoReconnect && reason !== 'io client disconnect') {
          handleReconnect();
        }
      });

      newSocket.on('connect_error', (error) => {
        console.error('[WebSocket Test] Erro de conexão:', error);
        addTestResult({
          type: 'connection',
          success: false,
          message: `Erro de conexão: ${error.message}`
        });

        if (autoReconnect) {
          handleReconnect();
        }
      });

      // Connection status response
      newSocket.on('connection:status', (status: ConnectionStatus) => {
        console.log('[WebSocket Test] Status da conexão:', status);
        setConnectionStatus(status);
      });

      // Connection test responses
      newSocket.on('connection:test:result', (result: { success: boolean; latency: number; message: string }) => {
        console.log('[WebSocket Test] Resultado do teste:', result);
        
        if (result.success && result.latency >= 0) {
          setLatencyHistory(prev => [result.latency, ...prev.slice(0, 9)]); // Keep last 10 latency measurements
        }

        addTestResult({
          type: 'latency',
          success: result.success,
          latency: result.latency,
          message: result.message
        });
      });

      // Message status responses
      newSocket.on('message:status', (data: { messageId: string; status: string; attempts: number }) => {
        console.log('[WebSocket Test] Status da mensagem:', data);
        
        addTestResult({
          type: 'message',
          success: data.status === 'sent',
          message: `Mensagem ${data.messageId}: ${data.status} (tentativas: ${data.attempts})`
        });
      });

      newSocket.on('message:failed', (data: { messageId: string; attempts: number; error: string }) => {
        console.log('[WebSocket Test] Mensagem falhou:', data);
        
        addTestResult({
          type: 'message',
          success: false,
          message: `Mensagem ${data.messageId} falhou: ${data.error}`
        });
      });

      // Ping/Pong for latency testing
      newSocket.on('pong', (data: { timestamp: number }) => {
        const latency = Date.now() - data.timestamp;
        console.log('[WebSocket Test] Pong recebido, latência:', latency + 'ms');
      });

      socketRef.current = newSocket;
      setSocket(newSocket);
    });
  };

  const handleReconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000); // Exponential backoff, max 30s
    setReconnectAttempts(prev => prev + 1);

    console.log(`[WebSocket Test] Tentando reconectar em ${delay}ms (tentativa ${reconnectAttempts + 1})`);

    reconnectTimeoutRef.current = setTimeout(() => {
      connectWebSocket();
    }, delay);
  };

  const disconnectWebSocket = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
      setSocket(null);
      setConnected(false);
      setConnectionStatus(null);
    }
  };

  const testConnection = () => {
    if (!socket || !connected) {
      toast({
        title: "Erro",
        description: "WebSocket não está conectado",
        variant: "destructive"
      });
      return;
    }

    console.log('[WebSocket Test] Testando conexão...');
    socket.emit('connection:test');
    
    addTestResult({
      type: 'connection',
      success: true,
      message: 'Teste de conexão iniciado'
    });
  };

  const testLatency = () => {
    if (!socket || !connected) {
      toast({
        title: "Erro",
        description: "WebSocket não está conectado",
        variant: "destructive"
      });
      return;
    }

    console.log('[WebSocket Test] Testando latência...');
    socket.emit('ping');
    
    addTestResult({
      type: 'latency',
      success: true,
      message: 'Teste de latência iniciado'
    });
  };

  const sendTestMessage = () => {
    if (!socket || !connected) {
      toast({
        title: "Erro",
        description: "WebSocket não está conectado",
        variant: "destructive"
      });
      return;
    }

    const messageData = {
      chatId: 'test-chat',
      body: testMessage,
      type: 'text',
      timestamp: new Date()
    };

    console.log('[WebSocket Test] Enviando mensagem teste:', messageData);
    socket.emit('message:send', messageData);
    
    addTestResult({
      type: 'message',
      success: true,
      message: `Mensagem enviada: "${testMessage}"`
    });
  };

  const runComprehensiveTest = async () => {
    if (!socket || !connected) {
      toast({
        title: "Erro",
        description: "WebSocket não está conectado",
        variant: "destructive"
      });
      return;
    }

    setIsRunningTests(true);
    
    try {
      // Test 1: Connection stability
      testConnection();
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Test 2: Latency measurement
      for (let i = 0; i < 5; i++) {
        testLatency();
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Test 3: Message sending
      setTestMessage('Teste automatizado de mensagem');
      sendTestMessage();
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Test 4: Status request
      socket.emit('status:request');
      
      toast({
        title: "Sucesso",
        description: "Testes abrangentes concluídos",
        variant: "default"
      });
      
    } catch (error) {
      console.error('[WebSocket Test] Erro nos testes:', error);
      toast({
        title: "Erro",
        description: "Erro durante os testes",
        variant: "destructive"
      });
    } finally {
      setIsRunningTests(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
    setLatencyHistory([]);
  };

  const getStatusColor = (success: boolean) => {
    return success ? 'text-green-500' : 'text-red-500';
  };

  const getStatusIcon = (success: boolean) => {
    return success ? <CheckCircle className="h-4 w-4" /> : <XCircle className="h-4 w-4" />;
  };

  const averageLatency = latencyHistory.length > 0 
    ? Math.round(latencyHistory.reduce((a, b) => a + b, 0) / latencyHistory.length)
    : 0;

  useEffect(() => {
    connectWebSocket();
    
    return () => {
      disconnectWebSocket();
    };
  }, []);

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Teste de Validação WebSocket</h2>
        <div className="flex items-center gap-2">
          {connected ? (
            <Badge variant="default" className="bg-green-500">
              <Wifi className="h-3 w-3 mr-1" />
              Conectado
            </Badge>
          ) : (
            <Badge variant="destructive">
              <WifiOff className="h-3 w-3 mr-1" />
              Desconectado
            </Badge>
          )}
          {reconnectAttempts > 0 && (
            <Badge variant="outline">
              <RotateCcw className="h-3 w-3 mr-1" />
              Tentativas: {reconnectAttempts}
            </Badge>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Connection Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Controles de Conexão</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Button 
                onClick={connectWebSocket}
                disabled={connected}
                className="flex-1"
              >
                <Wifi className="h-4 w-4 mr-2" />
                Conectar
              </Button>
              <Button 
                onClick={disconnectWebSocket}
                disabled={!connected}
                variant="outline"
                className="flex-1"
              >
                <WifiOff className="h-4 w-4 mr-2" />
                Desconectar
              </Button>
            </div>
            
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="autoReconnect"
                checked={autoReconnect}
                onChange={(e) => setAutoReconnect(e.target.checked)}
                className="rounded"
              />
              <label htmlFor="autoReconnect" className="text-sm">
                Reconexão automática
              </label>
            </div>
          </CardContent>
        </Card>

        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Testes de Validação</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={testConnection}
              disabled={!connected}
              className="w-full"
              variant="outline"
            >
              <TestTube className="h-4 w-4 mr-2" />
              Teste de Conexão
            </Button>
            
            <Button 
              onClick={testLatency}
              disabled={!connected}
              className="w-full"
              variant="outline"
            >
              <Activity className="h-4 w-4 mr-2" />
              Teste de Latência
            </Button>
            
            <Button 
              onClick={runComprehensiveTest}
              disabled={!connected || isRunningTests}
              className="w-full"
            >
              {isRunningTests ? (
                <Clock className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <TestTube className="h-4 w-4 mr-2" />
              )}
              Teste Abrangente
            </Button>
          </CardContent>
        </Card>

        {/* Metrics Display */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Métricas de Performance</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-muted-foreground">Latência Média</div>
                <div className="font-mono text-lg">{averageLatency}ms</div>
              </div>
              <div>
                <div className="text-muted-foreground">Histórico</div>
                <div className="font-mono text-lg">{latencyHistory.length}/10</div>
              </div>
              {connectionStatus && (
                <>
                  <div>
                    <div className="text-muted-foreground">Conexões Ativas</div>
                    <div className="font-mono text-lg">{connectionStatus.metrics.activeConnections}</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Reconexões</div>
                    <div className="font-mono text-lg">{connectionStatus.metrics.reconnections}</div>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Message Testing */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Teste de Mensagens</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Input
              placeholder="Mensagem de teste"
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={sendTestMessage}
              disabled={!connected || !testMessage.trim()}
            >
              <Send className="h-4 w-4 mr-2" />
              Enviar Teste
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-lg">Resultados dos Testes</CardTitle>
          <Button onClick={clearResults} variant="outline" size="sm">
            Limpar
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                Nenhum teste executado ainda
              </div>
            ) : (
              testResults.map((result) => (
                <div 
                  key={result.id}
                  className="flex items-center gap-3 p-3 border rounded-lg"
                >
                  <div className={getStatusColor(result.success)}>
                    {getStatusIcon(result.success)}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{result.message}</div>
                    <div className="text-sm text-muted-foreground">
                      {result.type} • {result.timestamp.toLocaleTimeString()}
                      {result.latency && ` • ${result.latency}ms`}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}