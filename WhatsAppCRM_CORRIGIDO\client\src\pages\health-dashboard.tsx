import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Activity, 
  MessageCircle, 
  Users, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Wifi,
  Server,
  Database,
  TrendingUp,
  TrendingDown
} from "lucide-react";
import { useWhatsApp } from "@/hooks/use-whatsapp";
import { useWebSocket } from "@/hooks/use-websocket";
import { formatTime, formatDate } from "@/lib/utils";

interface HealthMetrics {
  whatsapp: {
    connected: boolean;
    phoneNumber: string | null;
    uptime: number;
    lastMessage: Date | null;
    totalContacts: number;
    activeChats: number;
    messagesSentToday: number;
    messagesReceivedToday: number;
  };
  system: {
    serverUptime: number;
    memoryUsage: number;
    cpuUsage: number;
    databaseConnections: number;
    responseTime: number;
  };
  communication: {
    totalMessages: number;
    messagesLast24h: number;
    averageResponseTime: number;
    failedMessages: number;
    successRate: number;
  };
}

export default function HealthDashboard() {
  const { isConnected, phoneNumber } = useWhatsApp();

  // Fetch health metrics
  const { data: healthMetrics, isLoading, refetch } = useQuery({
    queryKey: ["/api/health", phoneNumber],
    queryFn: async () => {
      const response = await fetch(`/api/health${phoneNumber ? `?whatsappNumber=${phoneNumber}` : ''}`);
      if (!response.ok) throw new Error('Failed to fetch health metrics');
      return response.json();
    },
    enabled: !!phoneNumber,
    refetchInterval: 3000, // Refresh every 3 seconds for real-time monitoring
  });

  // Real-time updates handled by polling for better stability
  // WebSocket disabled to prevent conflicts with Vite dev server

  const getStatusColor = (status: boolean) => {
    return status ? "text-green-600" : "text-red-600";
  };

  const getStatusIcon = (status: boolean) => {
    return status ? CheckCircle : XCircle;
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  if (isLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center space-x-2">
          <Activity className="w-6 h-6 text-green-600" />
          <h1 className="text-2xl font-bold">Dashboard de Saúde da Comunicação</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Activity className="w-6 h-6 text-green-600" />
          <h1 className="text-2xl font-bold">Dashboard de Saúde da Comunicação</h1>
        </div>
        <Badge variant={isConnected ? "default" : "destructive"} className="text-sm">
          {isConnected ? "Sistema Online" : "Sistema Offline"}
        </Badge>
      </div>

      {/* WhatsApp Status Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status WhatsApp</CardTitle>
            <Wifi className={`w-4 h-4 ${getStatusColor(healthMetrics?.whatsapp?.connected || false)}`} />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {React.createElement(
                getStatusIcon(healthMetrics?.whatsapp?.connected || false),
                { className: `w-6 h-6 ${getStatusColor(healthMetrics?.whatsapp?.connected || false)}` }
              )}
              <span className="text-lg font-semibold">
                {healthMetrics?.whatsapp?.connected ? "Conectado" : "Desconectado"}
              </span>
            </div>
            {healthMetrics?.whatsapp?.phoneNumber && (
              <p className="text-xs text-muted-foreground mt-1">
                {healthMetrics.whatsapp.phoneNumber}
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contatos Ativos</CardTitle>
            <Users className="w-4 h-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{healthMetrics?.whatsapp?.totalContacts || 0}</div>
            <p className="text-xs text-muted-foreground">
              {healthMetrics?.whatsapp?.activeChats || 0} conversas ativas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Mensagens Hoje</CardTitle>
            <MessageCircle className="w-4 h-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(healthMetrics?.whatsapp?.messagesSentToday || 0) + (healthMetrics?.whatsapp?.messagesReceivedToday || 0)}
            </div>
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>↑ {healthMetrics?.whatsapp?.messagesSentToday || 0} enviadas</span>
              <span>↓ {healthMetrics?.whatsapp?.messagesReceivedToday || 0} recebidas</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tempo Online</CardTitle>
            <Clock className="w-4 h-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatUptime(healthMetrics?.whatsapp?.uptime || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Última mensagem: {healthMetrics?.whatsapp?.lastMessage 
                ? formatTime(healthMetrics.whatsapp.lastMessage)
                : "Nunca"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* System Health Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Server className="w-5 h-5" />
              <span>Saúde do Sistema</span>
            </CardTitle>
            <CardDescription>Métricas de performance do servidor</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex justify-between text-sm">
                <span>Uso de Memória</span>
                <span>{healthMetrics?.system?.memoryUsage || 0}%</span>
              </div>
              <Progress value={healthMetrics?.system?.memoryUsage || 0} className="mt-1" />
            </div>
            <div>
              <div className="flex justify-between text-sm">
                <span>Uso de CPU</span>
                <span>{healthMetrics?.system?.cpuUsage || 0}%</span>
              </div>
              <Progress value={healthMetrics?.system?.cpuUsage || 0} className="mt-1" />
            </div>
            <div className="pt-2 border-t">
              <div className="flex justify-between text-sm">
                <span>Tempo de Resposta</span>
                <span>{healthMetrics?.system?.responseTime || 0}ms</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="w-5 h-5" />
              <span>Base de Dados</span>
            </CardTitle>
            <CardDescription>Status da conexão com a base de dados</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Conexões Ativas</span>
              <Badge variant="outline">{healthMetrics?.system?.databaseConnections || 0}</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Status</span>
              <div className="flex items-center space-x-1">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm">Operacional</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Tempo do Servidor</span>
              <span className="text-sm">{formatUptime(healthMetrics?.system?.serverUptime || 0)}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5" />
              <span>Comunicação</span>
            </CardTitle>
            <CardDescription>Estatísticas de mensagens</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Total de Mensagens</span>
              <span className="font-semibold">{healthMetrics?.communication?.totalMessages || 0}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Últimas 24h</span>
              <span className="font-semibold">{healthMetrics?.communication?.messagesLast24h || 0}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Taxa de Sucesso</span>
              <Badge variant={
                (healthMetrics?.communication?.successRate || 0) >= 95 ? "default" : 
                (healthMetrics?.communication?.successRate || 0) >= 85 ? "secondary" : "destructive"
              }>
                {healthMetrics?.communication?.successRate || 0}%
              </Badge>
            </div>
            {(healthMetrics?.communication?.failedMessages || 0) > 0 && (
              <div className="flex items-center justify-between text-red-600">
                <span className="text-sm">Mensagens Falhadas</span>
                <span className="font-semibold">{healthMetrics.communication.failedMessages}</span>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Alerts Section with Critical System Monitoring */}
      {(!isConnected || 
        (healthMetrics?.communication?.failedMessages || 0) > 0 ||
        (healthMetrics?.system?.memoryUsage || 0) > 90 ||
        (healthMetrics?.communication?.successRate || 100) < 85 ||
        (healthMetrics?.system?.responseTime || 0) > 2000) && (
        <Card className="border-red-200 bg-red-50 animate-pulse">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-red-700">
              <AlertTriangle className="w-5 h-5" />
              <span>Alertas do Sistema</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {!isConnected && (
              <div className="flex items-center justify-between p-3 bg-red-100 rounded-lg">
                <div className="flex items-center space-x-2 text-red-700">
                  <XCircle className="w-4 h-4" />
                  <span className="text-sm font-medium">WhatsApp Desconectado</span>
                </div>
                <Badge variant="destructive" className="text-xs">CRÍTICO</Badge>
              </div>
            )}
            
            {(healthMetrics?.system?.memoryUsage || 0) > 90 && (
              <div className="flex items-center justify-between p-3 bg-orange-100 rounded-lg">
                <div className="flex items-center space-x-2 text-orange-700">
                  <AlertTriangle className="w-4 h-4" />
                  <span className="text-sm font-medium">
                    Memória Crítica: {healthMetrics.system.memoryUsage}%
                  </span>
                </div>
                <Badge variant="destructive" className="text-xs">ALTO</Badge>
              </div>
            )}

            {(healthMetrics?.communication?.successRate || 100) < 85 && (
              <div className="flex items-center justify-between p-3 bg-yellow-100 rounded-lg">
                <div className="flex items-center space-x-2 text-yellow-700">
                  <TrendingDown className="w-4 h-4" />
                  <span className="text-sm font-medium">
                    Taxa de Sucesso Baixa: {healthMetrics.communication.successRate}%
                  </span>
                </div>
                <Badge variant="secondary" className="text-xs">MÉDIO</Badge>
              </div>
            )}

            {(healthMetrics?.system?.responseTime || 0) > 2000 && (
              <div className="flex items-center justify-between p-3 bg-yellow-100 rounded-lg">
                <div className="flex items-center space-x-2 text-yellow-700">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm font-medium">
                    Resposta Lenta: {healthMetrics.system.responseTime}ms
                  </span>
                </div>
                <Badge variant="secondary" className="text-xs">PERFORMANCE</Badge>
              </div>
            )}

            {(healthMetrics?.communication?.failedMessages || 0) > 0 && (
              <div className="flex items-center justify-between p-3 bg-red-100 rounded-lg">
                <div className="flex items-center space-x-2 text-red-700">
                  <XCircle className="w-4 h-4" />
                  <span className="text-sm font-medium">
                    {healthMetrics.communication.failedMessages} mensagens falharam hoje
                  </span>
                </div>
                <Badge variant="destructive" className="text-xs">FALHA</Badge>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* System Status Summary */}
      <Card className="border-green-200 bg-green-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-green-700">
            <CheckCircle className="w-5 h-5" />
            <span>Status do Sistema</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <div className="font-semibold text-green-600">
                {healthMetrics?.whatsapp?.connected ? '✓' : '✗'} WhatsApp
              </div>
              <div className="text-xs text-muted-foreground">
                {healthMetrics?.whatsapp?.connected ? 'Conectado' : 'Desconectado'}
              </div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-green-600">
                {(healthMetrics?.system?.memoryUsage || 0) < 90 ? '✓' : '⚠'} Memória
              </div>
              <div className="text-xs text-muted-foreground">
                {healthMetrics?.system?.memoryUsage || 0}% usado
              </div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-green-600">
                {(healthMetrics?.communication?.successRate || 100) >= 95 ? '✓' : '⚠'} Taxa
              </div>
              <div className="text-xs text-muted-foreground">
                {healthMetrics?.communication?.successRate || 100}% sucesso
              </div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-green-600">
                {(healthMetrics?.system?.responseTime || 0) < 1000 ? '✓' : '⚠'} Resposta
              </div>
              <div className="text-xs text-muted-foreground">
                {healthMetrics?.system?.responseTime || 0}ms
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}