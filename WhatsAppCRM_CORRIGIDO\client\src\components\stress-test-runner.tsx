import { useState, useEffect, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  CheckCircle, XCircle, AlertTriangle, Clock, 
  MemoryStick, Activity, TrendingUp 
} from "lucide-react";

interface StressTestResult {
  testName: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  duration: number;
  result?: any;
  error?: string;
}

interface PerformanceMetrics {
  avgResponseTime: number;
  maxResponseTime: number;
  minResponseTime: number;
  totalRequests: number;
  failedRequests: number;
  memoryUsage: number;
  peakMemory: number;
}

export default function StressTestRunner() {
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string>("");
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState<StressTestResult[]>([]);
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    avgResponseTime: 0,
    maxResponseTime: 0,
    minResponseTime: 0,
    totalRequests: 0,
    failedRequests: 0,
    memoryUsage: 0,
    peakMemory: 0
  });

  const tests = [
    {
      name: "Carregamento Inicial (50 mensagens)",
      fn: () => testInitialLoad()
    },
    {
      name: "Paginação Incremental (10 lotes de 100)",
      fn: () => testIncrementalPagination()
    },
    {
      name: "Detecção de Duplicatas",
      fn: () => testDuplicateDetection()
    },
    {
      name: "Ordenação de Mensagens",
      fn: () => testMessageOrdering()
    },
    {
      name: "Limite de Memória (1000 mensagens)",
      fn: () => testMemoryLimits()
    },
    {
      name: "Performance de Scroll Infinito",
      fn: () => testScrollPerformance()
    },
    {
      name: "Recuperação de Erros",
      fn: () => testErrorRecovery()
    },
    {
      name: "Stress Test Completo (10.000 mensagens)",
      fn: () => testFullStressLoad()
    }
  ];

  const testInitialLoad = async (): Promise<any> => {
    const startTime = performance.now();
    const response = await fetch('/api/stress-test/messages?chatId=stress_test_chat&limit=50&offset=0');
    const endTime = performance.now();
    
    if (!response.ok) throw new Error(`HTTP ${response.status}`);
    
    const data = await response.json();
    const loadTime = endTime - startTime;
    
    // Validate response structure
    if (!data.data || !Array.isArray(data.data) || data.data.length !== 50) {
      throw new Error('Invalid response structure or message count');
    }
    
    return { loadTime, messageCount: data.data.length, hasMore: data.pagination.hasMore };
  };

  const testIncrementalPagination = async (): Promise<any> => {
    const results = [];
    const batchSize = 100;
    let totalMessages = 0;
    
    for (let i = 0; i < 10; i++) {
      const offset = i * batchSize;
      const startTime = performance.now();
      
      const response = await fetch(`/api/stress-test/messages?chatId=stress_test_chat&limit=${batchSize}&offset=${offset}`);
      const endTime = performance.now();
      
      if (!response.ok) throw new Error(`Batch ${i + 1} failed: HTTP ${response.status}`);
      
      const data = await response.json();
      const loadTime = endTime - startTime;
      
      results.push({ batch: i + 1, loadTime, messageCount: data.data.length });
      totalMessages += data.data.length;
      
      // Small delay to simulate real usage
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    const avgLoadTime = results.reduce((sum, r) => sum + r.loadTime, 0) / results.length;
    return { totalMessages, avgLoadTime, batches: results.length };
  };

  const testDuplicateDetection = async (): Promise<any> => {
    // Request same offset multiple times to verify consistency
    const requests = [];
    const offset = 500;
    const limit = 20;
    
    for (let i = 0; i < 5; i++) {
      const response = await fetch(`/api/stress-test/messages?chatId=stress_test_chat&limit=${limit}&offset=${offset}`);
      if (!response.ok) throw new Error(`Request ${i + 1} failed`);
      
      const data = await response.json();
      requests.push(data.data.map((msg: any) => msg.id));
    }
    
    // Check if all requests return identical IDs
    const firstRequest = requests[0];
    const allIdentical = requests.every(req => 
      req.length === firstRequest.length && 
      req.every((id: string, idx: number) => id === firstRequest[idx])
    );
    
    if (!allIdentical) {
      throw new Error('Duplicate detection failed: inconsistent message IDs');
    }
    
    return { consistency: 'perfect', duplicates: 0 };
  };

  const testMessageOrdering = async (): Promise<any> => {
    // Test descending order
    const descResponse = await fetch('/api/stress-test/messages?chatId=stress_test_chat&limit=100&offset=0&order=desc');
    if (!descResponse.ok) throw new Error('Descending order test failed');
    
    const descData = await descResponse.json();
    const descMessages = descData.data;
    
    // Verify descending timestamps
    for (let i = 1; i < descMessages.length; i++) {
      if (descMessages[i].timestamp > descMessages[i-1].timestamp) {
        throw new Error('Descending order violation detected');
      }
    }
    
    // Test ascending order
    const ascResponse = await fetch('/api/stress-test/messages?chatId=stress_test_chat&limit=100&offset=0&order=asc');
    if (!ascResponse.ok) throw new Error('Ascending order test failed');
    
    const ascData = await ascResponse.json();
    const ascMessages = ascData.data;
    
    // Verify ascending timestamps
    for (let i = 1; i < ascMessages.length; i++) {
      if (ascMessages[i].timestamp < ascMessages[i-1].timestamp) {
        throw new Error('Ascending order violation detected');
      }
    }
    
    return { descending: 'correct', ascending: 'correct' };
  };

  const testMemoryLimits = async (): Promise<any> => {
    const messages = [];
    const batchSize = 100;
    const maxBatches = 10; // 1000 messages total
    
    let initialMemory = 0;
    if ('memory' in performance) {
      initialMemory = (performance as any).memory.usedJSHeapSize;
    }
    
    for (let i = 0; i < maxBatches; i++) {
      const response = await fetch(`/api/stress-test/messages?chatId=stress_test_chat&limit=${batchSize}&offset=${i * batchSize}`);
      if (!response.ok) throw new Error(`Memory test batch ${i + 1} failed`);
      
      const data = await response.json();
      messages.push(...data.data);
      
      // Check memory growth
      if ('memory' in performance) {
        const currentMemory = (performance as any).memory.usedJSHeapSize;
        const memoryGrowth = currentMemory - initialMemory;
        
        // Alert if memory growth exceeds 50MB
        if (memoryGrowth > 50 * 1024 * 1024) {
          throw new Error(`Memory usage exceeded limit: ${Math.round(memoryGrowth / 1024 / 1024)}MB`);
        }
      }
    }
    
    const finalMemory = 'memory' in performance ? (performance as any).memory.usedJSHeapSize : 0;
    const memoryGrowth = finalMemory - initialMemory;
    
    return { 
      totalMessages: messages.length, 
      memoryGrowth: Math.round(memoryGrowth / 1024), // KB
      status: 'within_limits'
    };
  };

  const testScrollPerformance = async (): Promise<any> => {
    const performanceResults = [];
    const scrollPositions = [0, 1000, 2000, 3000, 4000, 5000];
    
    for (const offset of scrollPositions) {
      const startTime = performance.now();
      
      const response = await fetch(`/api/stress-test/messages?chatId=stress_test_chat&limit=50&offset=${offset}`);
      if (!response.ok) throw new Error(`Scroll test at offset ${offset} failed`);
      
      const data = await response.json();
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      
      performanceResults.push({ offset, loadTime, messageCount: data.data.length });
      
      // Simulate scroll delay
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    const avgLoadTime = performanceResults.reduce((sum, r) => sum + r.loadTime, 0) / performanceResults.length;
    const maxLoadTime = Math.max(...performanceResults.map(r => r.loadTime));
    
    // Performance should remain consistent across scroll positions
    if (maxLoadTime > avgLoadTime * 3) {
      throw new Error('Scroll performance degradation detected');
    }
    
    return { avgLoadTime, maxLoadTime, consistency: 'good' };
  };

  const testErrorRecovery = async (): Promise<any> => {
    let successfulRecoveries = 0;
    const totalAttempts = 3;
    
    for (let i = 0; i < totalAttempts; i++) {
      try {
        // Test invalid parameters
        const response = await fetch('/api/stress-test/messages?chatId=invalid_chat&limit=50&offset=0');
        
        if (response.status === 404 || response.status === 400) {
          // Expected error - test recovery
          const retryResponse = await fetch('/api/stress-test/messages?chatId=stress_test_chat&limit=50&offset=0');
          if (retryResponse.ok) {
            successfulRecoveries++;
          }
        }
      } catch (error) {
        // Network errors should be recoverable
        const retryResponse = await fetch('/api/stress-test/messages?chatId=stress_test_chat&limit=50&offset=0');
        if (retryResponse.ok) {
          successfulRecoveries++;
        }
      }
    }
    
    return { successfulRecoveries, totalAttempts, recoveryRate: (successfulRecoveries / totalAttempts) * 100 };
  };

  const testFullStressLoad = async (): Promise<any> => {
    const totalMessages = 10000;
    const batchSize = 200;
    const totalBatches = Math.ceil(totalMessages / batchSize);
    const results = [];
    
    let totalLoadTime = 0;
    let maxLoadTime = 0;
    let minLoadTime = Infinity;
    let failedRequests = 0;
    
    for (let i = 0; i < Math.min(totalBatches, 20); i++) { // Limit to 20 batches for testing
      const offset = i * batchSize;
      const startTime = performance.now();
      
      try {
        const response = await fetch(`/api/stress-test/messages?chatId=stress_test_chat&limit=${batchSize}&offset=${offset}`);
        const endTime = performance.now();
        const loadTime = endTime - startTime;
        
        if (!response.ok) {
          failedRequests++;
          continue;
        }
        
        const data = await response.json();
        
        totalLoadTime += loadTime;
        maxLoadTime = Math.max(maxLoadTime, loadTime);
        minLoadTime = Math.min(minLoadTime, loadTime);
        
        results.push({
          batch: i + 1,
          loadTime,
          messageCount: data.data.length,
          offset
        });
        
        // Update progress
        setProgress((i + 1) / Math.min(totalBatches, 20) * 100);
        
      } catch (error) {
        failedRequests++;
      }
      
      // Small delay to prevent overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    const avgLoadTime = totalLoadTime / results.length;
    const successRate = ((results.length / Math.min(totalBatches, 20)) * 100);
    
    return {
      totalBatches: results.length,
      avgLoadTime,
      maxLoadTime,
      minLoadTime,
      failedRequests,
      successRate,
      totalMessagesLoaded: results.reduce((sum, r) => sum + r.messageCount, 0)
    };
  };

  const runAllTests = useCallback(async () => {
    setIsRunning(true);
    setResults([]);
    setProgress(0);
    
    const testResults: StressTestResult[] = [];
    
    for (let i = 0; i < tests.length; i++) {
      const test = tests[i];
      setCurrentTest(test.name);
      
      const result: StressTestResult = {
        testName: test.name,
        status: 'running',
        duration: 0
      };
      
      testResults.push(result);
      setResults([...testResults]);
      
      const startTime = performance.now();
      
      try {
        const testResult = await test.fn();
        const endTime = performance.now();
        
        result.status = 'passed';
        result.duration = endTime - startTime;
        result.result = testResult;
        
      } catch (error) {
        const endTime = performance.now();
        result.status = 'failed';
        result.duration = endTime - startTime;
        result.error = error instanceof Error ? error.message : 'Unknown error';
      }
      
      setResults([...testResults]);
      setProgress(((i + 1) / tests.length) * 100);
      
      // Short delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    setIsRunning(false);
    setCurrentTest("Teste concluído");
    
    // Calculate final metrics
    const totalTests = testResults.length;
    const passedTests = testResults.filter(r => r.status === 'passed').length;
    const avgDuration = testResults.reduce((sum, r) => sum + r.duration, 0) / totalTests;
    
    setMetrics(prev => ({
      ...prev,
      totalRequests: totalTests,
      failedRequests: totalTests - passedTests,
      avgResponseTime: avgDuration
    }));
    
  }, [tests]);

  const getStatusIcon = (status: StressTestResult['status']) => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <Activity className="h-4 w-4 text-blue-500 animate-pulse" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const passedTests = results.filter(r => r.status === 'passed').length;
  const failedTests = results.filter(r => r.status === 'failed').length;
  const overallStatus = failedTests === 0 && results.length === tests.length ? 'success' : 
                       failedTests > 0 ? 'error' : 'running';

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Executor de Teste de Estresse</h2>
          <p className="text-muted-foreground">Validação completa de paginação com 10.000+ mensagens</p>
        </div>
        <Button 
          onClick={runAllTests} 
          disabled={isRunning}
          className="flex items-center gap-2"
        >
          <TrendingUp className="h-4 w-4" />
          {isRunning ? 'Executando...' : 'Iniciar Testes'}
        </Button>
      </div>

      {isRunning && (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex justify-between text-sm">
                <span>{currentTest}</span>
                <span>{progress.toFixed(1)}%</span>
              </div>
              <Progress value={progress} />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results Summary */}
      {results.length > 0 && (
        <div className="grid grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Testes Aprovados</p>
                  <p className="text-2xl font-bold text-green-600">{passedTests}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Testes Falharam</p>
                  <p className="text-2xl font-bold text-red-600">{failedTests}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Tempo Médio</p>
                  <p className="text-2xl font-bold">
                    {formatDuration(metrics.avgResponseTime)}
                  </p>
                </div>
                <Clock className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>Resultados dos Testes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {tests.map((test, index) => {
              const result = results[index];
              return (
                <div 
                  key={test.name}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    {getStatusIcon(result?.status || 'pending')}
                    <span className="font-medium">{test.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {result?.status === 'passed' && (
                      <Badge variant="default" className="bg-green-500 text-white">
                        {formatDuration(result.duration)}
                      </Badge>
                    )}
                    {result?.status === 'failed' && (
                      <Badge variant="destructive">
                        Falhou
                      </Badge>
                    )}
                    {result?.status === 'running' && (
                      <Badge variant="secondary">
                        Executando...
                      </Badge>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Overall Status Alert */}
      {!isRunning && results.length === tests.length && (
        <Alert variant={overallStatus === 'success' ? 'default' : 'destructive'}>
          {overallStatus === 'success' ? 
            <CheckCircle className="h-4 w-4" /> : 
            <AlertTriangle className="h-4 w-4" />
          }
          <AlertDescription>
            {overallStatus === 'success' 
              ? `Todos os ${passedTests} testes de estresse foram aprovados com sucesso! O sistema de paginação está funcionando corretamente com 10.000+ mensagens.`
              : `${failedTests} de ${results.length} testes falharam. Revise os problemas detectados no sistema de paginação.`
            }
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}