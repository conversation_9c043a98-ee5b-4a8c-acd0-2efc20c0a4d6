@tailwind base;
@tailwind components;
@tailwind utilities;

/* Casa das Camisetas CRM Theme Colors */
:root {
  /* Light theme variables */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 160 84% 39%;
  --primary-foreground: 355 7% 97%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 160 84% 39%;
  --radius: 0.5rem;
  
  /* WhatsApp theme colors */
  --wa-bg-primary: #f0f2f5;
  --wa-bg-secondary: #ffffff;
  --wa-bg-tertiary: #e9edef;
  --wa-bg-chat: #e5ddd5;
  --wa-text-primary: #111b21;
  --wa-text-secondary: #667781;
  --wa-green: #00a884;
  --wa-green-hover: #06ac85;
  --wa-border: #e9edef;
}

.dark {
  /* Dark theme variables */
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 160 84% 39%;
  --primary-foreground: 355 7% 97%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 160 84% 39%;
  
  /* WhatsApp dark theme colors */
  --wa-bg-primary: #111b21;
  --wa-bg-secondary: #202c33;
  --wa-bg-tertiary: #2a3942;
  --wa-bg-chat: #0b141a;
  --wa-text-primary: #d1d7db;
  --wa-text-secondary: #8696a0;
  --wa-green: #00a884;
  --wa-green-hover: #06ac85;
  --wa-border: #313d45;
}

/* WhatsApp Web faithful styling */
.whatsapp-theme {
  background: var(--wa-bg-primary);
  color: var(--wa-text-primary);
  font-family: "Segoe UI", Helvetica, Arial, sans-serif;
}

.whatsapp-sidebar {
  background: var(--wa-bg-primary);
  border-right: 1px solid var(--wa-border);
}

.whatsapp-header {
  background: var(--wa-bg-secondary);
  border-bottom: 1px solid var(--wa-border);
  height: 60px;
}

.whatsapp-chat-item {
  background: var(--wa-bg-primary);
  border-bottom: 1px solid var(--wa-border);
  transition: background-color 0.1s ease;
}

.whatsapp-chat-item:hover {
  background: var(--wa-bg-tertiary);
}

.whatsapp-chat-item.active {
  background: var(--wa-bg-tertiary);
}

.whatsapp-main-chat {
  background: var(--wa-bg-chat);
}

.whatsapp-message-area {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60"><circle cx="30" cy="30" r="1" fill="%23ffffff" opacity="0.03"/></svg>');
  background-repeat: repeat;
}

.whatsapp-message-from-me {
  background: #005c4b;
  color: var(--wa-text-primary);
  border-radius: 7.5px 7.5px 7.5px 0;
  margin-left: auto;
}

.whatsapp-message-from-them {
  background: var(--wa-bg-secondary);
  color: var(--wa-text-primary);
  border-radius: 7.5px 7.5px 0 7.5px;
  margin-right: auto;
}

.whatsapp-input-field {
  background: var(--wa-bg-tertiary);
  border: none;
  border-radius: 21px;
  color: var(--wa-text-primary);
}

.whatsapp-input-field::placeholder {
  color: var(--wa-text-secondary);
}

.whatsapp-input-field:focus {
  background: #3b4a54;
  outline: none;
}

.whatsapp-button {
  color: var(--wa-text-secondary);
  transition: background-color 0.15s ease;
}

.whatsapp-button:hover {
  background: var(--wa-bg-tertiary);
}

.whatsapp-send-button {
  background: var(--wa-green);
  color: var(--wa-bg-primary);
}

.whatsapp-send-button:hover {
  background: var(--wa-green-hover);
}

.whatsapp-search {
  background: var(--wa-bg-tertiary);
  border-radius: 8px;
  color: var(--wa-text-primary);
}

.whatsapp-search::placeholder {
  color: var(--wa-text-secondary);
}

/* Scrollbar styling */
.whatsapp-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.whatsapp-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.whatsapp-scrollbar::-webkit-scrollbar-thumb {
  background: var(--wa-bg-tertiary);
  border-radius: 3px;
}

.whatsapp-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #4a5b66;
}

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(222.2, 84%, 4.9%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(222.2, 84%, 4.9%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(222.2, 84%, 4.9%);
  --primary: hsl(221, 83%, 53%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(142, 76%, 36%);
  --secondary-foreground: hsl(210, 40%, 98%);
  --muted: hsl(210, 40%, 96%);
  --muted-foreground: hsl(215.4, 16.3%, 46.9%);
  --accent: hsl(262, 83%, 58%);
  --accent-foreground: hsl(210, 40%, 98%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --border: hsl(214.3, 31.8%, 91.4%);
  --input: hsl(214.3, 31.8%, 91.4%);
  --ring: hsl(221, 83%, 53%);
  --radius: 0.75rem;
}

.dark {
  --background: hsl(222.2, 84%, 4.9%);
  --foreground: hsl(210, 40%, 98%);
  --card: hsl(222.2, 84%, 4.9%);
  --card-foreground: hsl(210, 40%, 98%);
  --popover: hsl(222.2, 84%, 4.9%);
  --popover-foreground: hsl(210, 40%, 98%);
  --primary: hsl(221, 83%, 53%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(142, 76%, 36%);
  --secondary-foreground: hsl(210, 40%, 98%);
  --muted: hsl(217.2, 32.6%, 17.5%);
  --muted-foreground: hsl(215, 20.2%, 65.1%);
  --accent: hsl(262, 83%, 58%);
  --accent-foreground: hsl(210, 40%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --border: hsl(217.2, 32.6%, 17.5%);
  --input: hsl(217.2, 32.6%, 17.5%);
  --ring: hsl(221, 83%, 53%);
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans antialiased;
  }
}

@layer components {
  .kanban-column {
    @apply bg-slate-50 rounded-xl p-4 min-h-[500px];
  }
  
  .task-card {
    @apply bg-white p-4 rounded-lg shadow-sm border border-slate-200 cursor-pointer hover:shadow-md transition-all;
  }
  
  .task-card:hover {
    @apply transform -translate-y-1;
  }
  
  .message-bubble-sent {
    @apply bg-primary text-primary-foreground ml-auto rounded-lg rounded-br-sm p-3 max-w-xs;
  }
  
  .message-bubble-received {
    @apply bg-slate-100 text-slate-800 mr-auto rounded-lg rounded-bl-sm p-3 max-w-xs;
  }
}
