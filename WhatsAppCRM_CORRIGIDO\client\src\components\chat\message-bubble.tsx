import { formatTime } from "@/lib/utils";
import { cn } from "@/lib/utils";
import { Check, CheckCheck } from "lucide-react";

interface MessageBubbleProps {
  content: string;
  type: 'sent' | 'received';
  timestamp: Date | string;
  senderName?: string;
  status?: 'sending' | 'sent' | 'delivered' | 'read';
}

export default function MessageBubble({ 
  content, 
  type, 
  timestamp, 
  senderName,
  status = 'sent'
}: MessageBubbleProps) {
  const isSent = type === 'sent';

  const getStatusIcon = () => {
    if (!isSent) return null;
    
    switch (status) {
      case 'sending':
        return <div className="w-4 h-4 rounded-full border-2 border-gray-300 border-t-blue-500 animate-spin" />;
      case 'sent':
        return <Check className="w-4 h-4 text-gray-400" />;
      case 'delivered':
        return <CheckCheck className="w-4 h-4 text-gray-400" />;
      case 'read':
        return <CheckCheck className="w-4 h-4 text-blue-500" />;
      default:
        return null;
    }
  };

  return (
    <div className={cn(
      "flex mb-3 px-4",
      isSent ? "justify-end" : "justify-start"
    )}>
      <div className={cn(
        "max-w-[70%] rounded-lg px-3 py-2 relative",
        isSent 
          ? "bg-green-500 text-white rounded-br-sm" 
          : "bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-700 rounded-bl-sm"
      )}>
        {!isSent && senderName && (
          <div className="text-xs font-semibold text-blue-500 mb-1">
            {senderName}
          </div>
        )}
        
        <div className="text-sm leading-relaxed whitespace-pre-wrap break-words">
          {content}
        </div>
        
        <div className={cn(
          "flex items-center justify-end gap-1 mt-1 text-xs",
          isSent ? "text-green-100" : "text-gray-500 dark:text-gray-400"
        )}>
          <span>{formatTime(timestamp)}</span>
          {getStatusIcon()}
        </div>
      </div>
    </div>
  );
}