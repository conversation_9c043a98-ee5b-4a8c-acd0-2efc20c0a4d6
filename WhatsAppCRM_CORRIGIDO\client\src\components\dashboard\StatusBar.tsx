import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useWhatsApp } from '@/hooks/useWhatsApp';
import { useWebSocket } from '@/hooks/useWebSocket';
import { useEffect, useState } from 'react';

export function StatusBar() {
  const [uptime, setUptime] = useState('0m');
  const [onlineUsers, setOnlineUsers] = useState(0);
  const { status: whatsappStatus } = useWhatsApp(1);
  
  const { data: healthCheck } = useQuery({
    queryKey: ['/api/health'],
    refetchInterval: 30000, // Check every 30 seconds
  });

  const { connectionStatus, lastMessage } = useWebSocket(
    `ws://${window.location.host}/ws?userId=1`,
    {
      onMessage: (message) => {
        // Handle real-time updates for connected users count
        if (message.type === 'users_update') {
          setOnlineUsers(message.data.count || 0);
        }
      },
    }
  );

  useEffect(() => {
    // Update uptime display
    if (healthCheck?.uptime) {
      const uptimeSeconds = Math.floor(healthCheck.uptime);
      const hours = Math.floor(uptimeSeconds / 3600);
      const minutes = Math.floor((uptimeSeconds % 3600) / 60);
      
      if (hours > 0) {
        setUptime(`${hours}h ${minutes}m`);
      } else {
        setUptime(`${minutes}m`);
      }
    }
  }, [healthCheck]);

  useEffect(() => {
    // Update online users from health check
    if (healthCheck?.connectedClients) {
      setOnlineUsers(healthCheck.connectedClients);
    }
  }, [healthCheck]);

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'bg-green-500';
      case 'connecting':
        return 'bg-amber-500';
      default:
        return 'bg-red-500';
    }
  };

  const getWhatsAppStatusText = () => {
    if (whatsappStatus.isConnected) {
      return whatsappStatus.phoneNumber ? `Conectado (${whatsappStatus.phoneNumber})` : 'Conectado';
    }
    return 'Desconectado';
  };

  const getWhatsAppStatusColor = () => {
    return whatsappStatus.isConnected ? 'text-green-500' : 'text-red-500';
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Card className="w-80 shadow-lg border border-gray-200 dark:border-gray-700">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium text-gray-900 dark:text-white">
              Status do Sistema
            </CardTitle>
            <div className={`w-2 h-2 rounded-full animate-pulse ${getConnectionStatusColor()}`}></div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">WhatsApp:</span>
              <span className={`font-medium ${getWhatsAppStatusColor()}`}>
                {getWhatsAppStatusText()}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">Banco de Dados:</span>
              <span className="text-green-500 font-medium">
                Conectado
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">WebSocket:</span>
              <span className={`font-medium ${
                connectionStatus === 'connected' ? 'text-green-500' : 
                connectionStatus === 'connecting' ? 'text-amber-500' : 'text-red-500'
              }`}>
                {connectionStatus === 'connected' ? 'Conectado' :
                 connectionStatus === 'connecting' ? 'Conectando' : 'Desconectado'}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">Usuários Online:</span>
              <span className="text-blue-500 font-medium">
                {onlineUsers}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">Uptime:</span>
              <span className="text-gray-700 dark:text-gray-300 font-medium">
                {uptime}
              </span>
            </div>
            
            {lastMessage && (
              <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Última atividade:</span>
                  <span className="text-gray-500 dark:text-gray-400 text-xs">
                    {new Date(lastMessage.timestamp).toLocaleTimeString()}
                  </span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
