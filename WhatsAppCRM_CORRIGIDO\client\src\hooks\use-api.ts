import { useQueryClient } from "@tanstack/react-query";
import { useMemo } from "react";
import { useMessages, useMessagesByContact } from "./use-messages";
import { useContacts, useWhatsAppChats } from "./use-contacts";
import { useTasks, useTasksByStatus } from "./use-tasks";
import { useWhatsAppStatus, useWhatsAppProgress } from "./use-whatsapp-sessions";

interface UseApiOptions {
  whatsappNumber?: string;
  userId?: string;
  contactPhone?: string;
  enableParallelQueries?: boolean;
  enableOptimisticUpdates?: boolean;
}

// Hook principal para carregar todos os dados necessários em paralelo
export function useApiData({
  whatsappNumber,
  userId,
  contactPhone,
  enableParallelQueries = true,
  enableOptimisticUpdates = true
}: UseApiOptions) {
  const queryClient = useQueryClient();

  // Queries individuais
  const messagesQuery = useMessages(whatsappNumber, contactPhone);
  const contactsQuery = useContacts(whatsappNumber);
  const tasksQuery = useTasks(whatsappNumber);
  const whatsappStatusQuery = useWhatsAppStatus(userId);
  const chatsQuery = useWhatsAppChats(userId);

  // Dados combinados usando queries individuais
  const data = useMemo(() => {
    return {
      messages: messagesQuery.data || [],
      contacts: contactsQuery.data || [],
      tasks: tasksQuery.data || [],
      whatsappStatus: whatsappStatusQuery.data || null,
      chats: chatsQuery.data || [],
    };
  }, [
    messagesQuery.data,
    contactsQuery.data,
    tasksQuery.data,
    whatsappStatusQuery.data,
    chatsQuery.data,
  ]);

  // Estados de loading
  const isLoading = useMemo(() => {
    return messagesQuery.isLoading || 
           contactsQuery.isLoading || 
           tasksQuery.isLoading || 
           whatsappStatusQuery.isLoading ||
           chatsQuery.isLoading;
  }, [
    messagesQuery.isLoading,
    contactsQuery.isLoading,
    tasksQuery.isLoading,
    whatsappStatusQuery.isLoading,
    chatsQuery.isLoading,
  ]);

  // Estados de erro
  const error = useMemo(() => {
    return messagesQuery.error || 
           contactsQuery.error || 
           tasksQuery.error || 
           whatsappStatusQuery.error ||
           chatsQuery.error ||
           null;
  }, [
    messagesQuery.error,
    contactsQuery.error,
    tasksQuery.error,
    whatsappStatusQuery.error,
    chatsQuery.error,
  ]);

  // Funções de cache otimizado
  const prefetchData = async (options: UseApiOptions) => {
    const { whatsappNumber, userId, contactPhone } = options;
    
    const prefetchPromises = [];

    if (whatsappNumber) {
      prefetchPromises.push(
        queryClient.prefetchQuery({
          queryKey: ['/api/messages', whatsappNumber, contactPhone],
          queryFn: async () => {
            const params = new URLSearchParams();
            params.append('whatsappNumber', whatsappNumber);
            if (contactPhone) params.append('clientPhone', contactPhone);
            const response = await fetch(`/api/messages?${params}`);
            return response.json();
          },
          staleTime: 1000,
        }),
        queryClient.prefetchQuery({
          queryKey: ['/api/clients', whatsappNumber],
          queryFn: async () => {
            const response = await fetch(`/api/clients/${encodeURIComponent(whatsappNumber)}`);
            return response.json();
          },
          staleTime: 5 * 60 * 1000,
        }),
        queryClient.prefetchQuery({
          queryKey: ['/api/tasks', whatsappNumber],
          queryFn: async () => {
            const response = await fetch(`/api/tasks/${encodeURIComponent(whatsappNumber)}`);
            return response.json();
          },
          staleTime: 2 * 60 * 1000,
        })
      );
    }

    if (userId) {
      prefetchPromises.push(
        queryClient.prefetchQuery({
          queryKey: ['/api/whatsapp/status', userId],
          queryFn: async () => {
            const response = await fetch(`/api/whatsapp/status?userId=${encodeURIComponent(userId)}`);
            return response.json();
          },
          staleTime: 1000,
        }),
        queryClient.prefetchQuery({
          queryKey: ['/api/whatsapp/chats', userId],
          queryFn: async () => {
            const response = await fetch(`/api/whatsapp/chats?userId=${encodeURIComponent(userId)}`);
            return response.json();
          },
          staleTime: 5000,
        })
      );
    }

    await Promise.all(prefetchPromises);
  };

  const invalidateAll = () => {
    queryClient.invalidateQueries({ queryKey: ['/api/messages'] });
    queryClient.invalidateQueries({ queryKey: ['/api/clients'] });
    queryClient.invalidateQueries({ queryKey: ['/api/tasks'] });
    queryClient.invalidateQueries({ queryKey: ['/api/whatsapp/status'] });
    queryClient.invalidateQueries({ queryKey: ['/api/whatsapp/chats'] });
  };

  const clearCache = () => {
    queryClient.clear();
  };

  return {
    // Dados
    data,
    
    // Estados
    isLoading,
    error,
    
    // Queries individuais (se não estiver usando paralelas)
    queries: {
      messages: messagesQuery,
      contacts: contactsQuery,
      tasks: tasksQuery,
      whatsappStatus: whatsappStatusQuery,
      chats: chatsQuery,
    },
    
    // Funções de utilitário
    prefetchData,
    invalidateAll,
    clearCache,
    
    // Estatísticas
    stats: {
      totalMessages: data.messages.length,
      totalContacts: data.contacts.length,
      totalTasks: data.tasks.length,
      tasksCompleted: data.tasks.filter((task: any) => task.status === 'done').length,
      unreadChats: data.chats.filter((chat: any) => chat.unreadCount > 0).length,
      isWhatsAppConnected: data.whatsappStatus?.connected || false,
    },
  };
}

// Hook para dashboard que carrega dados essenciais
export function useDashboardData(whatsappNumber?: string, userId?: string) {
  return useApiData({
    whatsappNumber,
    userId,
    enableParallelQueries: true,
    enableOptimisticUpdates: true,
  });
}

// Hook para conversas que prioriza mensagens e chats
export function useConversationData(whatsappNumber?: string, userId?: string, contactPhone?: string) {
  const messagesQuery = useMessagesByContact(whatsappNumber, contactPhone);
  const chatsQuery = useWhatsAppChats(userId);
  const statusQuery = useWhatsAppStatus(userId);

  return {
    messages: messagesQuery.data || [],
    chats: chatsQuery.data || [],
    whatsappStatus: statusQuery.data,
    isLoading: messagesQuery.isLoading || chatsQuery.isLoading || statusQuery.isLoading,
    error: messagesQuery.error || chatsQuery.error || statusQuery.error,
    refetch: () => {
      messagesQuery.refetch();
      chatsQuery.refetch();
      statusQuery.refetch();
    },
  };
}

// Hook para CRM que prioriza contatos e tarefas
export function useCRMData(whatsappNumber?: string) {
  const contactsQuery = useContacts(whatsappNumber);
  const tasksQuery = useTasks(whatsappNumber);
  const todoTasks = useTasksByStatus(whatsappNumber, 'todo');
  const inProgressTasks = useTasksByStatus(whatsappNumber, 'in_progress');
  const doneTasks = useTasksByStatus(whatsappNumber, 'done');

  return {
    contacts: contactsQuery.data || [],
    tasks: tasksQuery.data || [],
    tasksByStatus: {
      todo: todoTasks.data || [],
      inProgress: inProgressTasks.data || [],
      done: doneTasks.data || [],
    },
    isLoading: contactsQuery.isLoading || tasksQuery.isLoading,
    error: contactsQuery.error || tasksQuery.error,
    refetch: () => {
      contactsQuery.refetch();
      tasksQuery.refetch();
    },
  };
}