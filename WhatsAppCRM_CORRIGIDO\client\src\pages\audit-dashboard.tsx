import { useState } from "react";
import { 
  Shield, FileText, Search, Filter, Download, Calendar,
  Eye, Edit3, Trash2, Star, Forward, Users, Clock,
  AlertTriangle, CheckCircle, ArrowLeft, MoreVertical
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { useLocation } from "wouter";

export default function AuditDashboard() {
  const [, setLocation] = useLocation();
  const [selectedFilters, setSelectedFilters] = useState({
    action: '',
    entity: '',
    userId: '',
    timeRange: '7d'
  });
  const [searchQuery, setSearchQuery] = useState('');

  const { data: auditLogs, isLoading } = useQuery({
    queryKey: ['/api/audit/logs', selectedFilters],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (selectedFilters.action) params.append('action', selectedFilters.action);
      if (selectedFilters.entity) params.append('entity', selectedFilters.entity);
      if (selectedFilters.userId) params.append('userId', selectedFilters.userId);
      params.append('limit', '200');

      const response = await fetch(`/api/audit/logs?${params}`);
      if (!response.ok) throw new Error('Failed to fetch audit logs');
      return response.json();
    }
  });

  const { data: complianceReport } = useQuery({
    queryKey: ['/api/audit/compliance-report'],
    queryFn: async () => {
      const response = await fetch('/api/audit/compliance-report/global');
      if (!response.ok) throw new Error('Failed to fetch compliance report');
      return response.json();
    }
  });

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'star': return <Star className="w-4 h-4 text-yellow-500" />;
      case 'unstar': return <Star className="w-4 h-4 text-gray-400" />;
      case 'edit': return <Edit3 className="w-4 h-4 text-blue-500" />;
      case 'delete': return <Trash2 className="w-4 h-4 text-red-500" />;
      case 'forward': return <Forward className="w-4 h-4 text-green-500" />;
      case 'create': return <CheckCircle className="w-4 h-4 text-emerald-500" />;
      default: return <Eye className="w-4 h-4 text-gray-500" />;
    }
  };

  const getActionLabel = (action: string) => {
    const labels: Record<string, string> = {
      star: 'Favoritou',
      unstar: 'Desfavoritou',
      edit: 'Editou',
      delete: 'Apagou',
      forward: 'Encaminhou',
      create: 'Criou',
      update: 'Atualizou'
    };
    return labels[action] || action;
  };

  const getEntityLabel = (entity: string) => {
    const labels: Record<string, string> = {
      message: 'Mensagem',
      client: 'Cliente',
      task: 'Tarefa',
      session: 'Sessão',
      contact: 'Contato'
    };
    return labels[entity] || entity;
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('pt-BR');
  };

  const filteredLogs = auditLogs?.filter((log: any) => {
    if (!searchQuery) return true;
    const searchLower = searchQuery.toLowerCase();
    return (
      log.userId.toLowerCase().includes(searchLower) ||
      log.action.toLowerCase().includes(searchLower) ||
      log.entity.toLowerCase().includes(searchLower) ||
      log.entityId.toLowerCase().includes(searchLower)
    );
  }) || [];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setLocation('/whatsapp')}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <div className="flex items-center space-x-3">
                <Shield className="w-8 h-8 text-blue-600" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Auditoria Empresarial</h1>
                  <p className="text-sm text-gray-600">Monitoramento completo de ações e conformidade</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                <Download className="w-4 h-4" />
                <span>Exportar Relatório</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Eventos</p>
                <p className="text-2xl font-bold text-gray-900">
                  {complianceReport?.totalAuditEvents || 0}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <FileText className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Mensagens Editadas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {complianceReport?.editedMessages || 0}
                </p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <Edit3 className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Mensagens Apagadas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {complianceReport?.deletedMessages || 0}
                </p>
              </div>
              <div className="p-3 bg-red-100 rounded-full">
                <Trash2 className="w-6 h-6 text-red-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Mensagens Favoritas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {complianceReport?.starredMessages || 0}
                </p>
              </div>
              <div className="p-3 bg-amber-100 rounded-full">
                <Star className="w-6 h-6 text-amber-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Filtros de Auditoria</h3>
              <button
                onClick={() => setSelectedFilters({ action: '', entity: '', userId: '', timeRange: '7d' })}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                Limpar Filtros
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ação</label>
                <select
                  value={selectedFilters.action}
                  onChange={(e) => setSelectedFilters(prev => ({ ...prev, action: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Todas as ações</option>
                  <option value="star">Favoritar</option>
                  <option value="unstar">Desfavoritar</option>
                  <option value="edit">Editar</option>
                  <option value="delete">Apagar</option>
                  <option value="forward">Encaminhar</option>
                  <option value="create">Criar</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Entidade</label>
                <select
                  value={selectedFilters.entity}
                  onChange={(e) => setSelectedFilters(prev => ({ ...prev, entity: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Todas as entidades</option>
                  <option value="message">Mensagem</option>
                  <option value="client">Cliente</option>
                  <option value="task">Tarefa</option>
                  <option value="session">Sessão</option>
                  <option value="contact">Contato</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Usuário</label>
                <input
                  type="text"
                  value={selectedFilters.userId}
                  onChange={(e) => setSelectedFilters(prev => ({ ...prev, userId: e.target.value }))}
                  placeholder="ID do usuário"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Período</label>
                <select
                  value={selectedFilters.timeRange}
                  onChange={(e) => setSelectedFilters(prev => ({ ...prev, timeRange: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="1d">Últimas 24h</option>
                  <option value="7d">Últimos 7 dias</option>
                  <option value="30d">Últimos 30 dias</option>
                  <option value="90d">Últimos 90 dias</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Buscar</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Buscar logs..."
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Audit Logs Table */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                Logs de Auditoria ({filteredLogs.length} registros)
              </h3>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <AlertTriangle className="w-4 h-4" />
                <span>Todos os eventos são registrados permanentemente</span>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            {isLoading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Carregando logs de auditoria...</p>
              </div>
            ) : filteredLogs.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <FileText className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p>Nenhum log de auditoria encontrado</p>
                <p className="text-sm">Tente ajustar os filtros</p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Data/Hora
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Usuário
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ação
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Entidade
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      ID da Entidade
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      IP
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Detalhes
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredLogs.map((log: any) => (
                    <tr key={log.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatTimestamp(log.timestamp)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <Users className="w-4 h-4 text-blue-600" />
                          </div>
                          <span className="text-sm font-medium text-gray-900">{log.userId}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          {getActionIcon(log.action)}
                          <span className="text-sm text-gray-900">{getActionLabel(log.action)}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {getEntityLabel(log.entity)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-600">
                        {log.entityId}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {log.ipAddress || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button className="text-blue-600 hover:text-blue-800">
                          <MoreVertical className="w-4 h-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}