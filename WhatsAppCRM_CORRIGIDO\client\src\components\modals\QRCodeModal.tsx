import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useWebSocket } from '@/hooks/useWebSocket';
import { Loader2, AlertCircle, CheckCircle, Smartphone, QrCode, RefreshCw } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface QRCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: number;
}

export function QRCodeModal({ isOpen, onClose, userId }: QRCodeModalProps) {
  const [qrCode, setQrCode] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string>('');
  const [connectionStep, setConnectionStep] = useState<'idle' | 'generating' | 'waiting' | 'connected'>('idle');

  // Ensure userId is valid number, default to 1 for testing
  const validUserId = typeof userId === 'number' && !isNaN(userId) && userId > 0 ? userId : 1;

  // Real WhatsApp connection - no simulation
  const connectWhatsApp = async () => {
    try {
      setIsLoading(true);
      setError('');
      setConnectionStep('generating');
      console.log(`Iniciando conexão WhatsApp REAL para usuário ${validUserId}`);
      
      const response = await fetch(`/api/whatsapp/connect/${validUserId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.success && result.qr) {
        setQrCode(result.qr);
        setConnectionStep('waiting');
        console.log('QR Code gerado - escaneie com sua conta REAL do WhatsApp');
      } else if (result.success && result.message?.includes('conectado')) {
        setIsConnected(true);
        setConnectionStep('connected');
        console.log('Cliente já conectado');
      } else {
        setError(result.error || 'Falha ao conectar WhatsApp');
        setConnectionStep('idle');
      }
    } catch (error) {
      console.error('Erro ao conectar WhatsApp:', error);
      setError(error instanceof Error ? error.message : 'Erro desconhecido');
      setConnectionStep('idle');
    } finally {
      setIsLoading(false);
    }
  };

  // WebSocket for real-time updates
  useWebSocket({
    onMessage: (message) => {
      if (message.type === 'whatsapp_connection' && message.data.userId === validUserId) {
        if (message.data.connected) {
          setIsConnected(true);
          setConnectionStep('connected');
          setQrCode('');
          console.log('WhatsApp conectado com sucesso!');
        }
      }
      if (message.type === 'qr_updated' && message.data.userId === validUserId) {
        setQrCode(message.data.qr);
        console.log('QR Code atualizado em tempo real');
      }
    }
  });

  // Auto-connect on modal open
  useEffect(() => {
    if (isOpen && !isConnected && connectionStep === 'idle') {
      connectWhatsApp();
    }
  }, [isOpen, isConnected, connectionStep]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setConnectionStep('idle');
      setQrCode('');
      setError('');
      setIsLoading(false);
    }
  }, [isOpen]);

  const renderStepContent = () => {
    switch (connectionStep) {
      case 'generating':
        return (
          <div className="flex flex-col items-center justify-center py-8 space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-lg font-medium">Gerando QR Code Real...</p>
            <p className="text-sm text-muted-foreground text-center">
              Conectando com WhatsApp usando dados reais<br />
              Sem simulação ou dados falsos
            </p>
          </div>
        );

      case 'waiting':
        return (
          <div className="flex flex-col items-center space-y-6">
            <div className="flex items-center space-x-2 mb-4">
              <QrCode className="h-5 w-5 text-green-600" />
              <span className="font-medium text-green-600">QR Code Gerado!</span>
            </div>
            
            {qrCode && (
              <div className="bg-white p-4 rounded-lg border-2 border-gray-200 shadow-sm">
                <img 
                  src={qrCode}
                  alt="QR Code WhatsApp" 
                  className="w-64 h-64 object-contain mx-auto"
                />
              </div>
            )}
            
            <div className="text-center space-y-3">
              <div className="flex items-center justify-center space-x-2">
                <Smartphone className="h-5 w-5 text-blue-600" />
                <span className="font-medium">Como escanear:</span>
              </div>
              <ol className="text-sm text-muted-foreground space-y-1 text-left">
                <li>1. Abra o WhatsApp no seu telefone</li>
                <li>2. Toque em Menu (⋮) → "Dispositivos conectados"</li>
                <li>3. Toque em "Conectar um dispositivo"</li>
                <li>4. Escaneie o QR Code acima</li>
              </ol>
            </div>
            
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Este é um QR Code real que conecta sua conta do WhatsApp.
                Aguarde a confirmação após escanear.
              </AlertDescription>
            </Alert>

            <Button 
              onClick={connectWhatsApp} 
              variant="outline"
              size="sm"
              className="mt-4"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Gerar Novo QR Code
            </Button>
          </div>
        );

      case 'connected':
        return (
          <div className="flex flex-col items-center justify-center py-8 space-y-4">
            <CheckCircle className="h-12 w-12 text-green-600" />
            <p className="text-lg font-medium text-green-600">WhatsApp Conectado!</p>
            <p className="text-sm text-muted-foreground text-center">
              Sua conta do WhatsApp foi conectada com sucesso.<br />
              Agora você pode receber e enviar mensagens reais.
            </p>
            <Button onClick={onClose} className="mt-4">
              Fechar
            </Button>
          </div>
        );

      default:
        return (
          <div className="flex flex-col items-center justify-center py-8 space-y-4">
            <Button onClick={connectWhatsApp} size="lg" className="w-full">
              <QrCode className="h-5 w-5 mr-2" />
              Gerar QR Code Real
            </Button>
            <p className="text-sm text-muted-foreground text-center">
              Clique para gerar um QR Code real do WhatsApp
            </p>
          </div>
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Smartphone className="h-5 w-5" />
            <span>Conectar WhatsApp Real</span>
          </DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {renderStepContent()}
        </div>
      </DialogContent>
    </Dialog>
  );
}