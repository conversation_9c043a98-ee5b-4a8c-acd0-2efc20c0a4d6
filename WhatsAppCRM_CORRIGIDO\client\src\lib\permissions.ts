export type UserRole = 'admin' | 'operator' | 'viewer';

export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  permissions: string[];
  isActive: boolean;
  createdAt: Date;
  lastLogin?: Date;
}

// Permission definitions
export const PERMISSIONS: Record<string, Permission> = {
  // WhatsApp permissions
  'whatsapp:view': {
    id: 'whatsapp:view',
    name: 'Ver WhatsApp',
    description: 'Visualizar conversas e mensagens do WhatsApp',
    resource: 'whatsapp',
    action: 'view'
  },
  'whatsapp:send': {
    id: 'whatsapp:send',
    name: 'Enviar Mensagens',
    description: 'Enviar mensagens via WhatsApp',
    resource: 'whatsapp',
    action: 'send'
  },
  'whatsapp:manage': {
    id: 'whatsapp:manage',
    name: 'Gerenciar WhatsApp',
    description: 'Conectar/desconectar WhatsApp e gerenciar sessões',
    resource: 'whatsapp',
    action: 'manage'
  },
  
  // CRM permissions
  'crm:view': {
    id: 'crm:view',
    name: 'Ver CRM',
    description: 'Visualizar dados do CRM e clientes',
    resource: 'crm',
    action: 'view'
  },
  'crm:create': {
    id: 'crm:create',
    name: 'Criar no CRM',
    description: 'Criar novos clientes e registros no CRM',
    resource: 'crm',
    action: 'create'
  },
  'crm:update': {
    id: 'crm:update',
    name: 'Editar CRM',
    description: 'Editar clientes e registros existentes no CRM',
    resource: 'crm',
    action: 'update'
  },
  'crm:delete': {
    id: 'crm:delete',
    name: 'Excluir do CRM',
    description: 'Excluir clientes e registros do CRM',
    resource: 'crm',
    action: 'delete'
  },
  
  // Kanban permissions
  'kanban:view': {
    id: 'kanban:view',
    name: 'Ver Kanban',
    description: 'Visualizar quadro Kanban e tarefas',
    resource: 'kanban',
    action: 'view'
  },
  'kanban:manage': {
    id: 'kanban:manage',
    name: 'Gerenciar Kanban',
    description: 'Criar, editar e mover tarefas no Kanban',
    resource: 'kanban',
    action: 'manage'
  },
  
  // Admin permissions
  'admin:users': {
    id: 'admin:users',
    name: 'Gerenciar Usuários',
    description: 'Criar, editar e gerenciar usuários do sistema',
    resource: 'admin',
    action: 'users'
  },
  'admin:logs': {
    id: 'admin:logs',
    name: 'Ver Logs',
    description: 'Visualizar logs do sistema e auditoria',
    resource: 'admin',
    action: 'logs'
  },
  'admin:analytics': {
    id: 'admin:analytics',
    name: 'Ver Analytics',
    description: 'Visualizar métricas e relatórios do sistema',
    resource: 'admin',
    action: 'analytics'
  },
  'admin:settings': {
    id: 'admin:settings',
    name: 'Configurações',
    description: 'Gerenciar configurações do sistema',
    resource: 'admin',
    action: 'settings'
  }
};

// Role definitions with default permissions
export const ROLE_PERMISSIONS: Record<UserRole, string[]> = {
  admin: Object.keys(PERMISSIONS), // All permissions
  operator: [
    'whatsapp:view',
    'whatsapp:send',
    'crm:view',
    'crm:create',
    'crm:update',
    'kanban:view',
    'kanban:manage'
  ],
  viewer: [
    'whatsapp:view',
    'crm:view',
    'kanban:view'
  ]
};

export class PermissionManager {
  private currentUser: User | null = null;

  setCurrentUser(user: User) {
    this.currentUser = user;
  }

  getCurrentUser(): User | null {
    return this.currentUser;
  }

  hasPermission(permissionId: string): boolean {
    if (!this.currentUser || !this.currentUser.isActive) {
      return false;
    }

    // Admin has all permissions
    if (this.currentUser.role === 'admin') {
      return true;
    }

    return this.currentUser.permissions.includes(permissionId);
  }

  hasAnyPermission(permissionIds: string[]): boolean {
    return permissionIds.some(id => this.hasPermission(id));
  }

  hasAllPermissions(permissionIds: string[]): boolean {
    return permissionIds.every(id => this.hasPermission(id));
  }

  canAccessResource(resource: string): boolean {
    if (!this.currentUser) return false;

    const resourcePermissions = Object.values(PERMISSIONS)
      .filter(p => p.resource === resource)
      .map(p => p.id);

    return this.hasAnyPermission(resourcePermissions);
  }

  canPerformAction(resource: string, action: string): boolean {
    const permissionId = `${resource}:${action}`;
    return this.hasPermission(permissionId);
  }

  getAvailablePermissions(): Permission[] {
    if (!this.currentUser) return [];

    return Object.values(PERMISSIONS).filter(permission => 
      this.hasPermission(permission.id)
    );
  }

  getRolePermissions(role: UserRole): Permission[] {
    const permissionIds = ROLE_PERMISSIONS[role];
    return permissionIds.map(id => PERMISSIONS[id]).filter(Boolean);
  }
}

// Global permission manager instance
export const permissionManager = new PermissionManager();

// React hook for permissions
export function usePermissions() {
  const user = permissionManager.getCurrentUser();

  return {
    user,
    hasPermission: (permissionId: string) => permissionManager.hasPermission(permissionId),
    hasAnyPermission: (permissionIds: string[]) => permissionManager.hasAnyPermission(permissionIds),
    hasAllPermissions: (permissionIds: string[]) => permissionManager.hasAllPermissions(permissionIds),
    canAccessResource: (resource: string) => permissionManager.canAccessResource(resource),
    canPerformAction: (resource: string, action: string) => permissionManager.canPerformAction(resource, action),
    getAvailablePermissions: () => permissionManager.getAvailablePermissions(),
    isAdmin: user?.role === 'admin',
    isOperator: user?.role === 'operator',
    isViewer: user?.role === 'viewer',
  };
}