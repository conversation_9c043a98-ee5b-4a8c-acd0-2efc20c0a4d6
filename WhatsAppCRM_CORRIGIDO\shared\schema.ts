import { pgTable, text, serial, integer, boolean, timestamp, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  email: text("email"),
  fullName: text("full_name"),
  role: text("role").default("salesperson"), // admin, manager, salesperson
  department: text("department").default("sales"),
  isActive: boolean("is_active").default(true),
  isOnline: boolean("is_online").default(false),
  lastLoginAt: timestamp("last_login_at"),
  maxConcurrentChats: integer("max_concurrent_chats").default(10),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const whatsappSessions = pgTable("whatsapp_sessions", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  sessionId: text("session_id").notNull().unique(),
  isConnected: boolean("is_connected").default(false),
  phoneNumber: text("phone_number"),
  sessionData: jsonb("session_data"),
  lastConnected: timestamp("last_connected"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const leads = pgTable("leads", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  phone: text("phone").notNull(),
  email: text("email"),
  status: text("status").notNull().default("new"), // new, contact, proposal, closed, lost
  source: text("source").default("whatsapp"),
  value: integer("value"), // in cents
  notes: text("notes"),
  assignedTo: integer("assigned_to").references(() => users.id),
  lastContactAt: timestamp("last_contact_at"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const chats = pgTable("chats", {
  id: serial("id").primaryKey(),
  leadId: integer("lead_id").references(() => leads.id),
  assignedUserId: integer("assigned_user_id").references(() => users.id),
  whatsappChatId: text("whatsapp_chat_id").notNull().unique(),
  isGroup: boolean("is_group").default(false),
  status: text("status").default("active"), // active, archived, transferred
  lastMessageAt: timestamp("last_message_at"),
  unreadCount: integer("unread_count").default(0),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const messages = pgTable("messages", {
  id: serial("id").primaryKey(),
  chatId: integer("chat_id").references(() => chats.id),
  whatsappMessageId: text("whatsapp_message_id").notNull(),
  fromMe: boolean("from_me").default(false),
  content: text("content"),
  messageType: text("message_type").default("text"), // text, image, audio, video, document
  timestamp: timestamp("timestamp").notNull(),
  isRead: boolean("is_read").default(false),
  metadata: jsonb("metadata"),
  createdAt: timestamp("created_at").defaultNow(),
});

export const activities = pgTable("activities", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  leadId: integer("lead_id").references(() => leads.id),
  type: text("type").notNull(), // message_sent, message_received, status_change, note_added, chat_transferred, call_made
  description: text("description").notNull(),
  metadata: jsonb("metadata"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Tabela para gerenciar transferências de chat entre vendedores
export const chatTransfers = pgTable("chat_transfers", {
  id: serial("id").primaryKey(),
  chatId: integer("chat_id").references(() => chats.id),
  fromUserId: integer("from_user_id").references(() => users.id),
  toUserId: integer("to_user_id").references(() => users.id),
  reason: text("reason"),
  notes: text("notes"),
  status: text("status").default("pending"), // pending, accepted, rejected, completed
  transferredAt: timestamp("transferred_at").defaultNow(),
  acceptedAt: timestamp("accepted_at"),
});

// Tabela para sessões ativas de usuários (controle de login simultâneo)
export const userSessions = pgTable("user_sessions", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  sessionToken: text("session_token").notNull().unique(),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  isActive: boolean("is_active").default(true),
  lastActivity: timestamp("last_activity").defaultNow(),
  createdAt: timestamp("created_at").defaultNow(),
  expiresAt: timestamp("expires_at"),
});

// Tabela para controle de disponibilidade dos vendedores
export const userAvailability = pgTable("user_availability", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  status: text("status").default("available"), // available, busy, break, offline
  maxActiveChats: integer("max_active_chats").default(5),
  currentActiveChats: integer("current_active_chats").default(0),
  autoAssign: boolean("auto_assign").default(true),
  lastStatusChange: timestamp("last_status_change").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Tabela Kanban para gerenciamento de tarefas
export const kanbanTasks = pgTable("kanban_tasks", {
  id: text("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description"),
  status: text("status").notNull().default("todo"), // todo, in_progress, review, done
  priority: text("priority").notNull().default("medium"), // low, medium, high, urgent
  clientId: text("client_id"),
  assignedTo: text("assigned_to"),
  dueDate: timestamp("due_date"),
  tags: text("tags").array(),
  position: integer("position").notNull().default(0),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Zod schemas
export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  email: true,
  fullName: true,
  role: true,
});

export const insertLeadSchema = createInsertSchema(leads).pick({
  name: true,
  phone: true,
  email: true,
  status: true,
  source: true,
  value: true,
  notes: true,
  assignedTo: true,
});

export const insertChatSchema = createInsertSchema(chats).pick({
  leadId: true,
  assignedUserId: true,
  whatsappChatId: true,
  isGroup: true,
  status: true,
});

export const insertMessageSchema = createInsertSchema(messages).pick({
  chatId: true,
  whatsappMessageId: true,
  fromMe: true,
  content: true,
  messageType: true,
  timestamp: true,
  metadata: true,
});

export const insertActivitySchema = createInsertSchema(activities).pick({
  userId: true,
  leadId: true,
  type: true,
  description: true,
  metadata: true,
});

export const insertKanbanTaskSchema = createInsertSchema(kanbanTasks).pick({
  title: true,
  description: true,
  status: true,
  priority: true,
  clientId: true,
  assignedTo: true,
  dueDate: true,
  tags: true,
  position: true,
});

// Types
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type WhatsappSession = typeof whatsappSessions.$inferSelect;
export type Lead = typeof leads.$inferSelect;
export type InsertLead = z.infer<typeof insertLeadSchema>;
export type Chat = typeof chats.$inferSelect;
export type InsertChat = z.infer<typeof insertChatSchema>;
export type Message = typeof messages.$inferSelect;
export type InsertMessage = z.infer<typeof insertMessageSchema>;
export type Activity = typeof activities.$inferSelect;
export type InsertActivity = z.infer<typeof insertActivitySchema>;
export type KanbanTask = typeof kanbanTasks.$inferSelect;
export type InsertKanbanTask = z.infer<typeof insertKanbanTaskSchema>;

// Lead status enum
export const LeadStatus = {
  NEW: "new",
  CONTACT: "contact", 
  PROPOSAL: "proposal",
  CLOSED: "closed",
  LOST: "lost"
} as const;

export type LeadStatusType = typeof LeadStatus[keyof typeof LeadStatus];
