import { useState, useRef, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { MessageSquare, Wifi, WifiOff } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useWebSocket } from "@/hooks/use-websocket";
import { apiRequest } from "@/lib/queryClient";
import type { Client, Message } from "@shared/schema";

// Importar os novos componentes de chat
import ContactList from "@/components/chat/contact-list";
import ChatHeader from "@/components/chat/chat-header";
import MessageBubble from "@/components/chat/message-bubble";
import ChatInput from "@/components/chat/chat-input";

export default function Messages() {
  const [selectedClient, setSelectedClient] = useState<string>("");
  const [isConnected, setIsConnected] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Mock WhatsApp number - em uma aplicação real viria da autenticação
  const whatsappNumber = "5511999999999";

  // WebSocket connection para atualizações em tempo real
  const { isConnected: wsConnected } = useWebSocket('ws://localhost:5001', {
    onMessage: (message) => {
      switch (message.type) {
        case 'new_message':
          // Invalidar queries para atualizar mensagens
          queryClient.invalidateQueries({ queryKey: ["/api/messages", whatsappNumber] });
          queryClient.invalidateQueries({ queryKey: ["/api/clients", whatsappNumber] });
          
          // Mostrar notificação para mensagens recebidas
          if (message.message.type === 'received') {
            toast({
              title: "Nova mensagem",
              description: `${message.message.content.substring(0, 50)}...`,
            });
          }
          break;
          
        case 'new_client':
          queryClient.invalidateQueries({ queryKey: ["/api/clients", whatsappNumber] });
          toast({
            title: "Novo cliente",
            description: `${message.client.name} foi adicionado automaticamente.`,
          });
          break;
          
        case 'connection_status':
          setIsConnected(message.connected);
          if (message.connected) {
            toast({
              title: "WhatsApp conectado",
              description: `Número: ${message.phoneNumber}`,
            });
          } else {
            toast({
              title: "WhatsApp desconectado",
              description: message.reason || "Conexão perdida",
              variant: "destructive",
            });
          }
          break;
      }
    },
    onConnectionChange: (connected) => {
      if (!connected) {
        toast({
          title: "Conexão perdida",
          description: "Tentando reconectar...",
          variant: "destructive",
        });
      }
    }
  });

  const { data: clients = [], isLoading: clientsLoading } = useQuery({
    queryKey: ["/api/clients", whatsappNumber],
    refetchInterval: 30000, // Atualizar a cada 30 segundos
  });

  const { data: messages = [], isLoading: messagesLoading } = useQuery({
    queryKey: ["/api/messages", whatsappNumber],
    refetchInterval: 10000, // Atualizar mensagens a cada 10 segundos
  });

  const { data: whatsappStatus } = useQuery({
    queryKey: ["/api/whatsapp/status"],
    refetchInterval: 5000, // Verificar status a cada 5 segundos
  });

  const sendMessageMutation = useMutation({
    mutationFn: async (data: { clientPhone: string; content: string }) => {
      return apiRequest(`/api/messages/send`, {
        method: "POST",
        body: {
          ...data,
          whatsappNumber
        },
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/messages", whatsappNumber] });
      toast({
        title: "Mensagem enviada",
        description: "Sua mensagem foi enviada com sucesso.",
      });
    },
    onError: () => {
      toast({
        title: "Erro",
        description: "Falha ao enviar mensagem. Verifique sua conexão.",
        variant: "destructive",
      });
    },
  });

  const handleSendMessage = (content: string) => {
    if (!selectedClient || !content.trim()) return;
    
    sendMessageMutation.mutate({
      clientPhone: selectedClient,
      content: content.trim(),
    });
  };

  const getSelectedClientData = () => {
    return clients.find((client: Client) => client.phone === selectedClient);
  };

  const getSelectedClientMessages = () => {
    return messages
      .filter((msg: Message) => msg.clientPhone === selectedClient)
      .sort((a, b) => new Date(a.timestamp || 0).getTime() - new Date(b.timestamp || 0).getTime());
  };

  // Auto-scroll para a última mensagem
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, selectedClient]);

  // Verificar status do WhatsApp
  useEffect(() => {
    if (whatsappStatus?.connected) {
      setIsConnected(true);
    } else {
      setIsConnected(false);
    }
  }, [whatsappStatus]);

  if (clientsLoading || messagesLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-500">Carregando conversas...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-100 dark:bg-gray-900">
      {/* Header com status de conexão */}
      <div className="bg-green-600 text-white px-6 py-3 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <MessageSquare className="w-6 h-6" />
          <div>
            <h1 className="text-lg font-semibold">WhatsApp Business</h1>
            <p className="text-sm text-green-100">
              {isConnected ? "Conectado" : "Desconectado"}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {isConnected ? (
            <Wifi className="w-5 h-5 text-green-200" />
          ) : (
            <WifiOff className="w-5 h-5 text-red-300" />
          )}
          
          {wsConnected && (
            <div className="w-2 h-2 bg-green-300 rounded-full animate-pulse" title="Tempo real ativo" />
          )}
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* Lista de contatos */}
        <ContactList
          contacts={clients}
          messages={messages}
          selectedContact={selectedClient}
          onSelectContact={setSelectedClient}
        />

        {/* Área de chat */}
        <div className="flex-1 flex flex-col bg-gray-50 dark:bg-gray-900">
          {selectedClient ? (
            <>
              {/* Header do chat */}
              <ChatHeader
                clientName={getSelectedClientData()?.name || "Cliente"}
                clientPhone={selectedClient}
                isOnline={false}
                lastSeen={undefined}
              />

              {/* Mensagens */}
              <div 
                className="flex-1 overflow-y-auto bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIwIDIwQzIwIDIwIDIwIDIwIDIwIDIwWiIgZmlsbD0iIzAwMDAwMCIgZmlsbC1vcGFjaXR5PSIwLjA1Ii8+Cjwvc3ZnPgo=')] py-4"
                style={{ 
                  backgroundSize: '40px 40px',
                  backgroundRepeat: 'repeat',
                  minHeight: '400px'
                }}
              >
                {getSelectedClientMessages().map((message: Message) => (
                  <MessageBubble
                    key={message.id}
                    content={message.content}
                    type={message.type}
                    timestamp={message.timestamp!}
                    senderName={message.type === 'received' ? getSelectedClientData()?.name : undefined}
                    status={message.type === 'sent' ? 'delivered' : undefined}
                  />
                ))}
                <div ref={messagesEndRef} />
              </div>

              {/* Input de mensagem */}
              <ChatInput
                onSendMessage={handleSendMessage}
                disabled={!isConnected || sendMessageMutation.isPending}
                placeholder={
                  !isConnected 
                    ? "WhatsApp desconectado - conecte para enviar mensagens" 
                    : "Digite uma mensagem..."
                }
              />
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center text-gray-500 dark:text-gray-400">
                <MessageSquare className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium mb-2">Bem-vindo ao WhatsApp Business</h3>
                <p>Selecione uma conversa para começar a enviar mensagens</p>
                {!isConnected && (
                  <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                    <p className="text-yellow-800 dark:text-yellow-200">
                      WhatsApp não está conectado. Vá para as configurações para conectar.
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}