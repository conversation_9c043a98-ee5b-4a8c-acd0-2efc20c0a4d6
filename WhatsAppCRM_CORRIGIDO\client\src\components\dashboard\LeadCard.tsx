import { Lead } from '@shared/schema';
import { formatPhone, formatRelativeTime, formatCurrency, getStatusColor } from '@/lib/utils';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';

interface LeadCardProps {
  lead: Lead;
  onDragStart: () => void;
  onDragEnd: () => void;
  isDragging: boolean;
}

export function LeadCard({ lead, onDragStart, onDragEnd, isDragging }: LeadCardProps) {
  const initials = lead.name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .substring(0, 2)
    .toUpperCase();

  return (
    <Card
      className={`lead-card ${isDragging ? 'opacity-50' : ''}`}
      draggable
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
    >
      <CardContent className="p-3">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center space-x-2">
            <Avatar className="w-8 h-8">
              <AvatarFallback className="text-xs">
                {initials}
              </AvatarFallback>
            </Avatar>
            <h5 className="font-medium text-gray-900 dark:text-white text-sm">
              {lead.name}
            </h5>
          </div>
          <div className={`status-indicator ${getStatusColor(lead.status)}`}></div>
        </div>
        
        {lead.notes && (
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
            {lead.notes}
          </p>
        )}
        
        {lead.value && (
          <p className="text-sm font-medium text-green-600 dark:text-green-400 mb-2">
            {formatCurrency(lead.value)}
          </p>
        )}
        
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>{formatPhone(lead.phone)}</span>
          <span>
            {lead.lastContactAt 
              ? formatRelativeTime(lead.lastContactAt)
              : formatRelativeTime(lead.createdAt!)
            }
          </span>
        </div>
      </CardContent>
    </Card>
  );
}
