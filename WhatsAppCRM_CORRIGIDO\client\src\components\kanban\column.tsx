import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import TaskCard from "./task-card";
import { Draggable } from "react-beautiful-dnd";
import type { Task } from "@shared/schema";

interface ColumnProps {
  title: string;
  tasks: Task[];
  status: string;
  onTaskEdit?: (task: Task) => void;
}

export default function Column({ title, tasks, status, onTaskEdit }: ColumnProps) {
  const statusColors = {
    todo: "bg-red-100 text-red-800",
    in_progress: "bg-yellow-100 text-yellow-800",
    done: "bg-green-100 text-green-800"
  };

  return (
    <Card className="kanban-column">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between text-lg">
          <span>{title}</span>
          <Badge 
            variant="secondary"
            className={statusColors[status as keyof typeof statusColors]}
          >
            {tasks.length}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {tasks.map((task, index) => (
          <Draggable key={task.id} draggableId={task.id.toString()} index={index}>
            {(provided, snapshot) => (
              <div
                ref={provided.innerRef}
                {...provided.draggableProps}
                {...provided.dragHandleProps}
                style={{
                  ...provided.draggableProps.style,
                  opacity: snapshot.isDragging ? 0.8 : 1,
                }}
              >
                <TaskCard 
                  task={task} 
                  onEdit={onTaskEdit}
                />
              </div>
            )}
          </Draggable>
        ))}
        {tasks.length === 0 && (
          <div className="text-center py-8 text-slate-400">
            <p className="text-sm">Nenhuma tarefa</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
