import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { getInitials, formatTime } from "@/lib/utils";

interface WhatsAppChat {
  id: string;
  name: string;
  phone: string;
  lastMessage?: {
    body: string;
    timestamp: number;
    fromMe: boolean;
  } | null;
  unreadCount: number;
  isOnline: boolean;
}

interface ContactListProps {
  contacts: WhatsAppChat[];
  selectedContact?: string;
  onSelectContact: (chatId: string) => void;
}

export default function ContactListWhatsApp({
  contacts,
  selectedContact,
  onSelectContact
}: ContactListProps) {
  if (!contacts || contacts.length === 0) {
    return (
      <div className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Conversas</h2>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-gray-500 dark:text-gray-400">
            <p className="text-sm">Nenhuma conversa encontrada</p>
            <p className="text-xs mt-1">Aguardando mensagens...</p>
          </div>
        </div>
      </div>
    );
  }

  // Ordenar contatos por última mensagem (mais recente primeiro)
  const sortedContacts = contacts.sort((a, b) => {
    const aTime = a.lastMessage?.timestamp || 0;
    const bTime = b.lastMessage?.timestamp || 0;
    return bTime - aTime;
  });

  return (
    <div className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Conversas</h2>
        <p className="text-sm text-gray-500 dark:text-gray-400">{contacts.length} contatos</p>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        {sortedContacts.map((contact) => (
          <div
            key={contact.id}
            onClick={() => onSelectContact(contact.id)}
            className={cn(
              "flex items-center gap-3 p-4 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer border-b border-gray-100 dark:border-gray-800",
              selectedContact === contact.id && "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800"
            )}
          >
            <Avatar>
              <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${contact.name}`} />
              <AvatarFallback className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                {getInitials(contact.name)}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                  {contact.name}
                </h3>
                {contact.lastMessage && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {formatTime(new Date(contact.lastMessage.timestamp * 1000))}
                  </span>
                )}
              </div>
              
              <div className="flex items-center justify-between mt-1">
                <p className="text-sm text-gray-600 dark:text-gray-300 truncate">
                  {contact.lastMessage?.body ? (
                    <>
                      {contact.lastMessage.fromMe && (
                        <span className="text-green-600 mr-1">→</span>
                      )}
                      {contact.lastMessage.body}
                    </>
                  ) : (
                    <span className="italic text-gray-400">Nenhuma mensagem ainda</span>
                  )}
                </p>
                
                {contact.unreadCount > 0 && (
                  <Badge className="bg-green-500 text-white text-xs px-2 py-0.5">
                    {contact.unreadCount}
                  </Badge>
                )}
              </div>
              
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {contact.phone}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}