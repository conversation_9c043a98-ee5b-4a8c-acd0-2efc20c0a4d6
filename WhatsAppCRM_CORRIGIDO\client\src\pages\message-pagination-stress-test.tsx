import { useState, useRef, useEffect, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import StressTestRunner from "@/components/stress-test-runner";
import { 
  Play, Pause, RefreshCw, Activity, MemoryStick, 
  MessageSquare, Clock, CheckCircle, XCircle, AlertTriangle 
} from "lucide-react";

interface TestMessage {
  id: string;
  content: string;
  timestamp: number;
  fromMe: boolean;
  chatId: string;
  type: 'text' | 'image' | 'document' | 'audio';
  status: 'sent' | 'delivered' | 'read';
}

interface PaginationTestMetrics {
  totalMessages: number;
  loadedMessages: number;
  renderTime: number;
  memoryUsage: number;
  duplicateCount: number;
  orderingErrors: number;
  scrollPosition: number;
  loadingErrors: number;
  averageLoadTime: number;
  peakMemory: number;
}

interface LoadResult {
  messages: TestMessage[];
  hasMore: boolean;
  nextCursor?: string;
  loadTime: number;
}

export default function MessagePaginationStressTest() {
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string>("");
  const [progress, setProgress] = useState(0);
  const [metrics, setMetrics] = useState<PaginationTestMetrics>({
    totalMessages: 0,
    loadedMessages: 0,
    renderTime: 0,
    memoryUsage: 0,
    duplicateCount: 0,
    orderingErrors: 0,
    scrollPosition: 0,
    loadingErrors: 0,
    averageLoadTime: 0,
    peakMemory: 0
  });
  
  const [testMessages, setTestMessages] = useState<TestMessage[]>([]);
  const [visibleMessages, setVisibleMessages] = useState<TestMessage[]>([]);
  const [loadedMessageIds, setLoadedMessageIds] = useState(new Set<string>());
  const [testConfig, setTestConfig] = useState({
    totalMessages: 10000,
    pageSize: 50,
    scrollThreshold: 200,
    memoryLimit: 100 * 1024 * 1024 // 100MB
  });
  
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const metricsIntervalRef = useRef<NodeJS.Timeout>();
  const startTimeRef = useRef<number>(0);
  const lastScrollPosition = useRef<number>(0);

  // Generate realistic test messages
  const generateTestMessages = useCallback((count: number, chatId: string): TestMessage[] => {
    const messages: TestMessage[] = [];
    const baseTime = Date.now() - (count * 60000); // 1 minute apart
    
    const messageTemplates = [
      "Olá, como você está?",
      "Preciso de informações sobre o produto",
      "Obrigado pelo atendimento",
      "Quando posso receber o orçamento?",
      "Vou analisar a proposta",
      "Perfeito, vamos seguir com isso",
      "Tem alguma promoção disponível?",
      "Pode me enviar mais detalhes?",
      "Estou interessado no serviço",
      "Qual o prazo de entrega?"
    ];
    
    const types: TestMessage['type'][] = ['text', 'image', 'document', 'audio'];
    const statuses: TestMessage['status'][] = ['sent', 'delivered', 'read'];
    
    for (let i = 0; i < count; i++) {
      const template = messageTemplates[i % messageTemplates.length];
      messages.push({
        id: `msg_${chatId}_${i.toString().padStart(6, '0')}`,
        content: `${template} - Mensagem ${i + 1}`,
        timestamp: baseTime + (i * 60000),
        fromMe: Math.random() > 0.6,
        chatId,
        type: types[Math.floor(Math.random() * types.length)],
        status: statuses[Math.floor(Math.random() * statuses.length)]
      });
    }
    
    return messages;
  }, []);

  // Real API pagination
  const loadMessagesFromAPI = useCallback(async (
    chatId: string, 
    limit: number, 
    offset: number,
    order: 'asc' | 'desc' = 'desc'
  ): Promise<LoadResult> => {
    const startTime = performance.now();
    
    try {
      const response = await fetch(`/api/stress-test/messages?chatId=${chatId}&limit=${limit}&offset=${offset}&order=${order}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      const loadTime = performance.now() - startTime;
      
      return {
        messages: data.data.map((msg: any) => ({
          id: msg.id,
          content: msg.content,
          timestamp: msg.timestamp,
          fromMe: msg.fromMe,
          chatId: msg.chatId,
          type: msg.type,
          status: msg.status
        })),
        hasMore: data.pagination.hasMore,
        nextCursor: data.pagination.nextOffset?.toString(),
        loadTime
      };
    } catch (error) {
      console.error('API load error:', error);
      const loadTime = performance.now() - startTime;
      
      // Return empty result on error
      return {
        messages: [],
        hasMore: false,
        loadTime
      };
    }
  }, []);

  // Memory monitoring
  const getMemoryUsage = useCallback((): number => {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    // Estimate based on loaded messages
    return visibleMessages.length * 200; // ~200 bytes per message estimate
  }, [visibleMessages.length]);

  // Detect duplicates
  const detectDuplicates = useCallback((messages: TestMessage[]): number => {
    const seen = new Set<string>();
    let duplicates = 0;
    
    for (const message of messages) {
      if (seen.has(message.id)) {
        duplicates++;
      } else {
        seen.add(message.id);
      }
    }
    
    return duplicates;
  }, []);

  // Check message ordering
  const checkMessageOrdering = useCallback((messages: TestMessage[]): number => {
    let errors = 0;
    
    for (let i = 1; i < messages.length; i++) {
      if (messages[i].timestamp < messages[i - 1].timestamp) {
        errors++;
      }
    }
    
    return errors;
  }, []);

  // Scroll position handler
  const handleScroll = useCallback((event: Event) => {
    const target = event.target as HTMLDivElement;
    const scrollTop = target.scrollTop;
    const scrollHeight = target.scrollHeight;
    const clientHeight = target.clientHeight;
    
    // Update scroll position in metrics
    setMetrics(prev => ({
      ...prev,
      scrollPosition: scrollTop
    }));
    
    // Check if we need to load more messages (infinite scroll)
    if (scrollHeight - scrollTop - clientHeight < testConfig.scrollThreshold) {
      loadMoreMessages();
    }
    
    lastScrollPosition.current = scrollTop;
  }, [testConfig.scrollThreshold]);

  // Load more messages
  const loadMoreMessages = useCallback(async () => {
    if (!isRunning || (testConfig.totalMessages > 0 && visibleMessages.length >= testConfig.totalMessages)) return;
    
    const offset = visibleMessages.length;
    const result = await loadMessagesFromAPI('stress_test_chat', testConfig.pageSize, offset);
    
    if (result.messages.length === 0) {
      setMetrics(prev => ({
        ...prev,
        loadingErrors: prev.loadingErrors + 1
      }));
      return;
    }
    
    const newMessages = result.messages.filter(msg => !loadedMessageIds.has(msg.id));
    
    setVisibleMessages(prev => [...prev, ...newMessages]);
    setLoadedMessageIds(prev => {
      const newSet = new Set(prev);
      newMessages.forEach(msg => newSet.add(msg.id));
      return newSet;
    });
    
    // Update metrics
    const allMessages = [...visibleMessages, ...newMessages];
    const duplicates = detectDuplicates(allMessages);
    const orderingErrors = checkMessageOrdering(allMessages);
    const currentMemory = getMemoryUsage();
    
    setMetrics(prev => ({
      ...prev,
      loadedMessages: prev.loadedMessages + newMessages.length,
      duplicateCount: duplicates,
      orderingErrors,
      memoryUsage: currentMemory,
      peakMemory: Math.max(prev.peakMemory, currentMemory),
      averageLoadTime: (prev.averageLoadTime + result.loadTime) / 2
    }));
  }, [
    isRunning, 
    visibleMessages, 
    testConfig.totalMessages,
    testConfig.pageSize, 
    loadedMessageIds,
    detectDuplicates,
    checkMessageOrdering,
    getMemoryUsage,
    loadMessagesFromAPI
  ]);

  // Start stress test
  const startStressTest = useCallback(async () => {
    setIsRunning(true);
    setProgress(0);
    setCurrentTest("Inicializando teste de estresse...");
    startTimeRef.current = performance.now();
    
    // Reset state
    setVisibleMessages([]);
    setLoadedMessageIds(new Set());
    setTestMessages([]);
    
    setCurrentTest("Carregando mensagens iniciais...");
    
    // Load initial batch from API
    const initialResult = await loadMessagesFromAPI('stress_test_chat', testConfig.pageSize, 0);
    
    if (initialResult.messages.length === 0) {
      setCurrentTest("❌ Erro ao carregar mensagens iniciais");
      setIsRunning(false);
      return;
    }
    
    setVisibleMessages(initialResult.messages);
    setLoadedMessageIds(new Set(initialResult.messages.map((m: TestMessage) => m.id)));
    
    setMetrics({
      totalMessages: testConfig.totalMessages,
      loadedMessages: initialResult.messages.length,
      renderTime: 0,
      memoryUsage: getMemoryUsage(),
      duplicateCount: 0,
      orderingErrors: 0,
      scrollPosition: 0,
      loadingErrors: 0,
      averageLoadTime: initialResult.loadTime,
      peakMemory: getMemoryUsage()
    });
    
    setCurrentTest("Executando teste de estresse...");
    
    // Start metrics monitoring
    metricsIntervalRef.current = setInterval(() => {
      const currentMemory = getMemoryUsage();
      const elapsed = performance.now() - startTimeRef.current;
      const progressPercent = (visibleMessages.length / testConfig.totalMessages) * 100;
      
      setProgress(progressPercent);
      setMetrics(prev => ({
        ...prev,
        memoryUsage: currentMemory,
        peakMemory: Math.max(prev.peakMemory, currentMemory),
        renderTime: elapsed
      }));
      
      // Check memory limit
      if (currentMemory > testConfig.memoryLimit) {
        setCurrentTest("⚠️ Limite de memória excedido!");
      }
    }, 1000);
  }, [
    testConfig,
    loadMessagesFromAPI,
    getMemoryUsage,
    visibleMessages.length
  ]);

  // Stop test
  const stopTest = useCallback(() => {
    setIsRunning(false);
    setCurrentTest("Teste interrompido");
    if (metricsIntervalRef.current) {
      clearInterval(metricsIntervalRef.current);
    }
  }, []);

  // Reset test
  const resetTest = useCallback(() => {
    setIsRunning(false);
    setProgress(0);
    setCurrentTest("");
    setTestMessages([]);
    setVisibleMessages([]);
    setLoadedMessageIds(new Set());
    setMetrics({
      totalMessages: 0,
      loadedMessages: 0,
      renderTime: 0,
      memoryUsage: 0,
      duplicateCount: 0,
      orderingErrors: 0,
      scrollPosition: 0,
      loadingErrors: 0,
      averageLoadTime: 0,
      peakMemory: 0
    });
    
    if (metricsIntervalRef.current) {
      clearInterval(metricsIntervalRef.current);
    }
  }, []);

  // Setup scroll listener
  useEffect(() => {
    const scrollElement = scrollAreaRef.current;
    if (scrollElement) {
      scrollElement.addEventListener('scroll', handleScroll);
      return () => scrollElement.removeEventListener('scroll', handleScroll);
    }
  }, [handleScroll]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (metricsIntervalRef.current) {
        clearInterval(metricsIntervalRef.current);
      }
    };
  }, []);

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusColor = (value: number, threshold: number) => {
    if (value === 0) return 'bg-gray-500';
    if (value < threshold) return 'bg-green-500';
    if (value < threshold * 2) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Teste de Estresse - Paginação de Mensagens</h1>
          <p className="text-muted-foreground">Validação completa com 10.000+ mensagens para detectar vazamentos de memória, duplicatas e problemas de ordenação</p>
        </div>
      </div>

      <Tabs defaultValue="automated" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="automated">Testes Automatizados</TabsTrigger>
          <TabsTrigger value="manual">Teste Manual Interativo</TabsTrigger>
        </TabsList>
        
        <TabsContent value="automated" className="space-y-6">
          <StressTestRunner />
        </TabsContent>
        
        <TabsContent value="manual" className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold">Teste Manual de Scroll Infinito</h2>
            <div className="flex gap-2">
              <Button 
                onClick={startStressTest} 
                disabled={isRunning}
                className="flex items-center gap-2"
              >
                <Play className="h-4 w-4" />
                Iniciar Teste
              </Button>
              <Button 
                onClick={stopTest} 
                disabled={!isRunning}
                variant="destructive"
                className="flex items-center gap-2"
              >
                <Pause className="h-4 w-4" />
                Parar
              </Button>
              <Button 
                onClick={resetTest}
                variant="outline"
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Reset
              </Button>
            </div>
          </div>

      {/* Test Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Configuração do Teste</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div>
            <label className="text-sm font-medium">Total de Mensagens</label>
            <Input
              type="number"
              value={testConfig.totalMessages}
              onChange={(e) => setTestConfig(prev => ({
                ...prev,
                totalMessages: parseInt(e.target.value) || 10000
              }))}
              disabled={isRunning}
            />
          </div>
          <div>
            <label className="text-sm font-medium">Tamanho da Página</label>
            <Input
              type="number"
              value={testConfig.pageSize}
              onChange={(e) => setTestConfig(prev => ({
                ...prev,
                pageSize: parseInt(e.target.value) || 50
              }))}
              disabled={isRunning}
            />
          </div>
          <div>
            <label className="text-sm font-medium">Threshold de Scroll (px)</label>
            <Input
              type="number"
              value={testConfig.scrollThreshold}
              onChange={(e) => setTestConfig(prev => ({
                ...prev,
                scrollThreshold: parseInt(e.target.value) || 200
              }))}
              disabled={isRunning}
            />
          </div>
          <div>
            <label className="text-sm font-medium">Limite de Memória (MB)</label>
            <Input
              type="number"
              value={testConfig.memoryLimit / (1024 * 1024)}
              onChange={(e) => setTestConfig(prev => ({
                ...prev,
                memoryLimit: (parseInt(e.target.value) || 100) * 1024 * 1024
              }))}
              disabled={isRunning}
            />
          </div>
        </CardContent>
      </Card>

      {/* Progress and Status */}
      {isRunning && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 animate-pulse" />
              Status do Teste
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm">
                  <span>{currentTest}</span>
                  <span>{progress.toFixed(1)}%</span>
                </div>
                <Progress value={progress} className="mt-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Metrics Dashboard */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Mensagens Carregadas</p>
                <p className="text-2xl font-bold">{metrics.loadedMessages.toLocaleString()}</p>
                <p className="text-xs text-muted-foreground">
                  de {metrics.totalMessages.toLocaleString()}
                </p>
              </div>
              <MessageSquare className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Uso de Memória</p>
                <p className="text-2xl font-bold">{formatBytes(metrics.memoryUsage)}</p>
                <p className="text-xs text-muted-foreground">
                  Pico: {formatBytes(metrics.peakMemory)}
                </p>
              </div>
              <MemoryStick className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Duplicatas</p>
                <p className="text-2xl font-bold">{metrics.duplicateCount}</p>
                <div className="flex items-center gap-1 mt-1">
                  <div className={`w-2 h-2 rounded-full ${getStatusColor(metrics.duplicateCount, 1)}`} />
                  <span className="text-xs text-muted-foreground">
                    {metrics.duplicateCount === 0 ? 'Nenhuma' : 'Detectadas'}
                  </span>
                </div>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Erros de Ordem</p>
                <p className="text-2xl font-bold">{metrics.orderingErrors}</p>
                <div className="flex items-center gap-1 mt-1">
                  <div className={`w-2 h-2 rounded-full ${getStatusColor(metrics.orderingErrors, 1)}`} />
                  <span className="text-xs text-muted-foreground">
                    {metrics.orderingErrors === 0 ? 'Ordem correta' : 'Problemas detectados'}
                  </span>
                </div>
              </div>
              <Clock className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Métricas de Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm font-medium">Tempo de Renderização</p>
              <p className="text-xl">{(metrics.renderTime / 1000).toFixed(2)}s</p>
            </div>
            <div>
              <p className="text-sm font-medium">Tempo Médio de Carregamento</p>
              <p className="text-xl">{metrics.averageLoadTime.toFixed(2)}ms</p>
            </div>
            <div>
              <p className="text-sm font-medium">Posição do Scroll</p>
              <p className="text-xl">{metrics.scrollPosition.toFixed(0)}px</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Message Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Preview das Mensagens (Scroll Infinito)</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-96 w-full border rounded-md p-4" ref={scrollAreaRef}>
            <div className="space-y-2">
              {visibleMessages.map((message, index) => (
                <div 
                  key={message.id}
                  className={`p-3 rounded-lg max-w-[70%] ${
                    message.fromMe 
                      ? 'bg-blue-500 text-white ml-auto' 
                      : 'bg-gray-200 dark:bg-gray-700'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <div className="flex items-center justify-between mt-1 text-xs opacity-70">
                    <span>#{index + 1}</span>
                    <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
                  </div>
                </div>
              ))}
              {isRunning && visibleMessages.length < testMessages.length && (
                <div className="flex justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500" />
                </div>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Alerts for Issues */}
      {(metrics.duplicateCount > 0 || metrics.orderingErrors > 0 || metrics.memoryUsage > testConfig.memoryLimit) && (
        <div className="space-y-2">
          {metrics.duplicateCount > 0 && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                Detectadas {metrics.duplicateCount} mensagens duplicadas no carregamento!
              </AlertDescription>
            </Alert>
          )}
          
          {metrics.orderingErrors > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Detectados {metrics.orderingErrors} erros na ordenação das mensagens!
              </AlertDescription>
            </Alert>
          )}
          
          {metrics.memoryUsage > testConfig.memoryLimit && (
            <Alert variant="destructive">
              <MemoryStick className="h-4 w-4" />
              <AlertDescription>
                Uso de memória ({formatBytes(metrics.memoryUsage)}) excedeu o limite configurado ({formatBytes(testConfig.memoryLimit)})!
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}
        </TabsContent>
      </Tabs>
    </div>
  );
}