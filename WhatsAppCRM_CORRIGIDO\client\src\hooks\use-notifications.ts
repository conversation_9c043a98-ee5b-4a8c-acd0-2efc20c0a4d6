import { useState, useEffect, useCallback } from 'react';

interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  tag?: string;
  requireInteraction?: boolean;
  silent?: boolean;
  data?: any;
}

interface UseNotificationsReturn {
  permission: NotificationPermission | null;
  isSupported: boolean;
  requestPermission: () => Promise<NotificationPermission>;
  sendNotification: (options: NotificationOptions) => Promise<Notification | null>;
  isEnabled: boolean;
  toggleNotifications: () => void;
}

export function useNotifications(): UseNotificationsReturn {
  const [permission, setPermission] = useState<NotificationPermission | null>(null);
  const [isEnabled, setIsEnabled] = useState(false);
  
  const isSupported = 'Notification' in window;

  useEffect(() => {
    if (isSupported) {
      setPermission(Notification.permission);
      
      // Load notification preference from localStorage
      const savedPreference = localStorage.getItem('notifications_enabled');
      setIsEnabled(savedPreference === 'true' && Notification.permission === 'granted');
    }
  }, [isSupported]);

  const requestPermission = useCallback(async (): Promise<NotificationPermission> => {
    if (!isSupported) {
      throw new Error('Notifications not supported');
    }

    const result = await Notification.requestPermission();
    setPermission(result);
    
    if (result === 'granted') {
      setIsEnabled(true);
      localStorage.setItem('notifications_enabled', 'true');
    }
    
    return result;
  }, [isSupported]);

  const sendNotification = useCallback(async (options: NotificationOptions): Promise<Notification | null> => {
    if (!isSupported || !isEnabled || permission !== 'granted') {
      return null;
    }

    try {
      // Check if the page is visible
      if (document.visibilityState === 'visible' && document.hasFocus()) {
        // Don't send notification if user is actively using the app
        return null;
      }

      const notification = new Notification(options.title, {
        body: options.body,
        icon: options.icon || '/favicon.ico',
        tag: options.tag,
        requireInteraction: options.requireInteraction || false,
        silent: options.silent || false,
        data: options.data
      });

      // Auto-close notification after 5 seconds unless requireInteraction is true
      if (!options.requireInteraction) {
        setTimeout(() => {
          notification.close();
        }, 5000);
      }

      // Handle notification click
      notification.onclick = () => {
        window.focus();
        
        // If there's specific data, handle navigation
        if (options.data?.chatId) {
          // Navigate to specific chat
          window.postMessage({
            type: 'NOTIFICATION_CLICK',
            chatId: options.data.chatId
          }, '*');
        }
        
        notification.close();
      };

      return notification;
    } catch (error) {
      console.error('Error sending notification:', error);
      return null;
    }
  }, [isSupported, isEnabled, permission]);

  const toggleNotifications = useCallback(() => {
    const newState = !isEnabled;
    setIsEnabled(newState);
    localStorage.setItem('notifications_enabled', newState.toString());
    
    if (newState && permission !== 'granted') {
      requestPermission();
    }
  }, [isEnabled, permission, requestPermission]);

  return {
    permission,
    isSupported,
    requestPermission,
    sendNotification,
    isEnabled,
    toggleNotifications
  };
}