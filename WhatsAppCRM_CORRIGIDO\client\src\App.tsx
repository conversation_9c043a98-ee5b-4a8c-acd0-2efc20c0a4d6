import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { useEffect, useState } from "react";
import LoginFixed from "@/pages/login-fixed";
import DashboardPage from "@/pages/dashboard";
import CRM from "@/pages/crm";
import CrmGlobal from "@/pages/crm-global";
import Kanban from "@/pages/kanban";
// Removed duplicate WhatsApp components to prevent routing conflicts
import KanbanDemo from "@/pages/kanban-demo";
import ApiHooksDemo from "@/pages/api-hooks-demo";
import StatusReal from "@/pages/status-real";
import SistemaReal from "@/pages/sistema-real";
import WhatsAppCompleto from "@/pages/whatsapp-completo";
import WhatsAppReal from "@/pages/whatsapp-real";
import QRCode from "@/pages/qr-code";
import HealthDashboard from "@/pages/health-dashboard";
import AuditDashboard from "@/pages/audit-dashboard";
import AdminDashboard from "@/pages/admin-dashboard";
import WebSocketTestPage from "@/pages/websocket-test";
import WebSocketReconnectionTestPage from "@/pages/websocket-reconnection-test";
import MessagePaginationStressTest from "@/pages/message-pagination-stress-test";
import SmokeTestSuite from "@/pages/smoke-test-suite";
import ComprehensiveSmokeTest from "@/pages/smoke-test-comprehensive";
import SystemDiagnosticReport from "@/pages/system-diagnostic-report";
import MessageValidationPage from "@/pages/message-validation";
import MediaValidationPage from "@/pages/media-validation";
import UXValidationPage from "@/pages/ux-validation";
import CachePerformanceValidationPage from "@/pages/cache-performance-validation";
import FinalBatteryTestPage from "@/pages/final-battery-test";
import ProtectionPage from "@/pages/protection";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import { useThemeStore } from "@/lib/theme-store";
import NotificationCenter from "@/components/ui/notification-center";
import { WhatsAppProvider } from "@/hooks/use-whatsapp";

function Router() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [currentSection, setCurrentSection] = useState({
    title: "Dashboard Geral",
    description: "Visão geral do seu CRM WhatsApp"
  });

  useEffect(() => {
    const checkAuth = async () => {
      const savedUserId = localStorage.getItem('whatsapp_user_id');
      
      if (!savedUserId) {
        setIsAuthenticated(false);
        return;
      }

      try {
        // Check via clients endpoint which shows correct connection status
        const clientsResponse = await fetch('/api/whatsapp/clients');
        if (clientsResponse.ok) {
          const clients = await clientsResponse.json();
          const userSession = clients[savedUserId];
          
          setUserId(savedUserId);
          
          // Update localStorage based on actual connection status
          if (userSession && userSession.isConnected && userSession.phoneNumber) {
            localStorage.setItem('whatsapp_connected', 'true');
            localStorage.setItem('whatsapp_phone', userSession.phoneNumber);
            setIsAuthenticated(true);
          } else {
            localStorage.setItem('whatsapp_connected', 'false');
            localStorage.removeItem('whatsapp_phone');
            setIsAuthenticated(false);
          }
        } else {
          localStorage.setItem('whatsapp_connected', 'false');
          setIsAuthenticated(false);
        }
      } catch {
        localStorage.setItem('whatsapp_connected', 'false');
        setIsAuthenticated(false);
      }
    };

    const savedUserId = localStorage.getItem('whatsapp_user_id');
    checkAuth();
    
    // WebSocket connection for instant status updates
    let ws: WebSocket | null = null;
    if (savedUserId) {
      try {
        ws = new WebSocket('ws://localhost:5000/ws');
        
        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            if (data.type === 'whatsapp_connected' && data.userId == parseInt(savedUserId)) {
              localStorage.setItem('whatsapp_connected', 'true');
              localStorage.setItem('whatsapp_phone', data.phoneNumber);
              setIsAuthenticated(true);
            } else if (data.type === 'whatsapp_disconnected' && data.userId == parseInt(savedUserId)) {
              localStorage.setItem('whatsapp_connected', 'false');
              localStorage.removeItem('whatsapp_phone');
              setIsAuthenticated(false);
            }
          } catch (e) {
            console.log('WebSocket message parse error:', e);
          }
        };
      } catch (e) {
        console.log('WebSocket connection failed, using polling only');
      }
    }
    
    // Check every 1 second for fast response
    const interval = setInterval(checkAuth, 1000);
    
    return () => {
      clearInterval(interval);
      if (ws) {
        ws.close();
      }
    };
  }, []);

  if (isAuthenticated === null) {
    return (
      <div className="min-h-screen bg-[#f0f2f5] flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-[#00a884] border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginFixed />;
  }

  return (
    <Switch>
      {/* Standalone Pages */}
      <Route path="/whatsapp" component={WhatsAppCompleto} />
      <Route path="/whatsapp-real" component={WhatsAppReal} />
      <Route path="/kanban-demo" component={KanbanDemo} />
      <Route path="/api-hooks" component={ApiHooksDemo} />
      <Route path="/status" component={StatusReal} />
      <Route path="/real" component={SistemaReal} />
      <Route path="/audit" component={AuditDashboard} />
      <Route path="/websocket-test" component={WebSocketTestPage} />
      <Route path="/websocket-reconnection-test" component={WebSocketReconnectionTestPage} />
      <Route path="/message-pagination-stress-test" component={MessagePaginationStressTest} />
      <Route path="/smoke-test-suite" component={SmokeTestSuite} />
      <Route path="/smoke-test-comprehensive" component={ComprehensiveSmokeTest} />
      <Route path="/system-diagnostic" component={SystemDiagnosticReport} />
      <Route path="/message-validation" component={MessageValidationPage} />
      <Route path="/media-validation" component={MediaValidationPage} />
      <Route path="/ux-validation" component={UXValidationPage} />
      <Route path="/cache-performance-validation" component={CachePerformanceValidationPage} />
      <Route path="/final-battery-test" component={FinalBatteryTestPage} />
      <Route path="/dashboard" component={DashboardPage} />
      
      {/* New Standalone Components */}
      <Route path="/crm-global" component={CrmGlobal} />
      <Route path="/kanban-board" component={Kanban} />
      
      {/* Default Layout with Sidebar */}
      <Route>
        <div className="flex h-screen bg-slate-50 dark:bg-slate-900 transition-colors">
          <Sidebar onSectionChange={setCurrentSection} />
          <div className="flex-1 flex flex-col overflow-hidden">
            <Header currentSection={currentSection} />
            <main className="flex-1 overflow-auto">
              <Switch>
                <Route path="/" component={DashboardPage} />
                <Route path="/crm" component={CRM} />
                <Route path="/kanban" component={Kanban} />
                <Route path="/protection" component={ProtectionPage} />
                <Route path="/health" component={HealthDashboard} />
                <Route path="/admin" component={AdminDashboard} />
              </Switch>
            </main>
          </div>
        </div>
      </Route>
    </Switch>
  );
}

function App() {
  const { theme } = useThemeStore();

  // Apply theme to document element on mount and theme changes
  useEffect(() => {
    document.documentElement.classList.toggle('dark', theme === 'dark');
  }, [theme]);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <WhatsAppProvider>
          <Toaster />
          <NotificationCenter />
          <Router />
        </WhatsAppProvider>
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
