import { useState } from "react";
import { ArrowLeft, RefreshCw, Database, MessageSquare, Users, CheckSquare, Smartphone } from "lucide-react";
import { useLocation } from "wouter";
import { 
  useDashboardData, 
  useConversationData, 
  useCRMData,
  useSendMessage,
  useCreateTask,
  useCreateContact,
  useInitializeWhatsApp
} from "@/hooks";

export default function ApiHooksDemo() {
  const [selectedUserId] = useState("demo_user_123");
  const [selectedWhatsAppNumber] = useState("5511999887766");
  const [selectedContactPhone] = useState("5511888776655");
  const [, setLocation] = useLocation();

  // Demonstração dos hooks principais
  const dashboardData = useDashboardData(selectedWhatsAppNumber, selectedUserId);
  const conversationData = useConversationData(selectedWhatsAppNumber, selectedUserId, selectedContactPhone);
  const crmData = useCRMData(selectedWhatsAppNumber);

  // Mutations para demonstração
  const sendMessageMutation = useSendMessage();
  const createTaskMutation = useCreateTask();
  const createContactMutation = useCreateContact();
  const initializeWhatsAppMutation = useInitializeWhatsApp();

  const handleSendTestMessage = () => {
    sendMessageMutation.mutate({
      to: selectedContactPhone,
      message: "Mensagem de teste enviada via React Query hook!",
      userId: selectedUserId
    });
  };

  const handleCreateTestTask = () => {
    createTaskMutation.mutate({
      whatsappNumber: selectedWhatsAppNumber,
      title: "Tarefa criada via hook",
      description: "Esta tarefa foi criada usando o hook useCreateTask",
      priority: "medium",
      status: "todo"
    });
  };

  const handleCreateTestContact = () => {
    createContactMutation.mutate({
      whatsappNumber: selectedWhatsAppNumber,
      name: "Contato Teste",
      phone: "5511777666555",
      email: "<EMAIL>",
      company: "Empresa Demo"
    });
  };

  const handleInitializeWhatsApp = () => {
    initializeWhatsAppMutation.mutate(selectedUserId);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setLocation('/')}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </button>
          <div className="flex-1">
            <h1 className="text-xl font-semibold text-gray-900">
              Demonstração dos Hooks React Query
            </h1>
            <p className="text-sm text-gray-500">
              Hooks otimizados para consumir a API Node.js + Express com cache e chamadas paralelas
            </p>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Dashboard Data Hook */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center space-x-2 mb-4">
            <Database className="w-5 h-5 text-blue-600" />
            <h2 className="text-lg font-semibold">useDashboardData Hook</h2>
            <button
              onClick={() => dashboardData.invalidateAll()}
              className="ml-auto p-2 hover:bg-gray-100 rounded-full"
              title="Recarregar dados"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
          </div>
          
          {dashboardData.isLoading ? (
            <div className="flex items-center space-x-2 text-gray-500">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span>Carregando dados do dashboard...</span>
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-blue-700">{dashboardData.stats.totalMessages}</div>
                <div className="text-sm text-blue-600">Mensagens</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-700">{dashboardData.stats.totalContacts}</div>
                <div className="text-sm text-green-600">Contatos</div>
              </div>
              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-yellow-700">{dashboardData.stats.totalTasks}</div>
                <div className="text-sm text-yellow-600">Tarefas</div>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-purple-700">{dashboardData.stats.tasksCompleted}</div>
                <div className="text-sm text-purple-600">Concluídas</div>
              </div>
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-red-700">{dashboardData.stats.unreadChats}</div>
                <div className="text-sm text-red-600">Não lidas</div>
              </div>
            </div>
          )}
        </div>

        {/* Conversation Data Hook */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center space-x-2 mb-4">
            <MessageSquare className="w-5 h-5 text-green-600" />
            <h2 className="text-lg font-semibold">useConversationData Hook</h2>
            <button
              onClick={conversationData.refetch}
              className="ml-auto p-2 hover:bg-gray-100 rounded-full"
              title="Recarregar conversas"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium mb-3">Mensagens Recentes</h3>
              {conversationData.isLoading ? (
                <div className="text-gray-500">Carregando mensagens...</div>
              ) : (
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {(conversationData.messages || []).slice(0, 5).map((message: any, index: number) => (
                    <div key={index} className="text-sm bg-gray-50 p-2 rounded">
                      <div className="font-medium">{message.fromMe ? 'Você' : 'Contato'}</div>
                      <div className="text-gray-600 truncate">{message.content || message.body}</div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            <div>
              <h3 className="font-medium mb-3">Status WhatsApp</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${
                    conversationData.whatsappStatus?.connected ? 'bg-green-500' : 'bg-red-500'
                  }`}></div>
                  <span className="text-sm">
                    {conversationData.whatsappStatus?.connected ? 'Conectado' : 'Desconectado'}
                  </span>
                </div>
                {conversationData.whatsappStatus?.phoneNumber && (
                  <div className="text-sm text-gray-600">
                    Número: {conversationData.whatsappStatus.phoneNumber}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* CRM Data Hook */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center space-x-2 mb-4">
            <Users className="w-5 h-5 text-purple-600" />
            <h2 className="text-lg font-semibold">useCRMData Hook</h2>
            <button
              onClick={crmData.refetch}
              className="ml-auto p-2 hover:bg-gray-100 rounded-full"
              title="Recarregar dados CRM"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="font-medium mb-3">Tarefas por Status</h3>
              {crmData.isLoading ? (
                <div className="text-gray-500">Carregando tarefas...</div>
              ) : (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>A Fazer:</span>
                    <span className="font-medium">{crmData.tasksByStatus.todo.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Em Progresso:</span>
                    <span className="font-medium">{crmData.tasksByStatus.inProgress.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Concluídas:</span>
                    <span className="font-medium">{crmData.tasksByStatus.done.length}</span>
                  </div>
                </div>
              )}
            </div>
            
            <div>
              <h3 className="font-medium mb-3">Contatos Recentes</h3>
              {crmData.isLoading ? (
                <div className="text-gray-500">Carregando contatos...</div>
              ) : (
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {crmData.contacts.slice(0, 5).map((contact: any) => (
                    <div key={contact.id} className="text-sm bg-gray-50 p-2 rounded">
                      <div className="font-medium">{contact.name}</div>
                      <div className="text-gray-600">{contact.phone}</div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            <div>
              <h3 className="font-medium mb-3">Últimas Tarefas</h3>
              {crmData.isLoading ? (
                <div className="text-gray-500">Carregando tarefas...</div>
              ) : (
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {crmData.tasks.slice(0, 5).map((task: any) => (
                    <div key={task.id} className="text-sm bg-gray-50 p-2 rounded">
                      <div className="font-medium truncate">{task.title}</div>
                      <div className="text-gray-600 capitalize">{task.status.replace('_', ' ')}</div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Mutations Demo */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <CheckSquare className="w-5 h-5 text-orange-600" />
            <h2 className="text-lg font-semibold">Demonstração de Mutations</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <button
              onClick={handleSendTestMessage}
              disabled={sendMessageMutation.isPending}
              className="p-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {sendMessageMutation.isPending ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Enviando...</span>
                </div>
              ) : (
                <>
                  <MessageSquare className="w-5 h-5 mx-auto mb-2" />
                  <div className="text-sm">Enviar Mensagem</div>
                </>
              )}
            </button>

            <button
              onClick={handleCreateTestTask}
              disabled={createTaskMutation.isPending}
              className="p-4 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
            >
              {createTaskMutation.isPending ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Criando...</span>
                </div>
              ) : (
                <>
                  <CheckSquare className="w-5 h-5 mx-auto mb-2" />
                  <div className="text-sm">Criar Tarefa</div>
                </>
              )}
            </button>

            <button
              onClick={handleCreateTestContact}
              disabled={createContactMutation.isPending}
              className="p-4 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 transition-colors"
            >
              {createContactMutation.isPending ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Criando...</span>
                </div>
              ) : (
                <>
                  <Users className="w-5 h-5 mx-auto mb-2" />
                  <div className="text-sm">Criar Contato</div>
                </>
              )}
            </button>

            <button
              onClick={handleInitializeWhatsApp}
              disabled={initializeWhatsAppMutation.isPending}
              className="p-4 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 transition-colors"
            >
              {initializeWhatsAppMutation.isPending ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Conectando...</span>
                </div>
              ) : (
                <>
                  <Smartphone className="w-5 h-5 mx-auto mb-2" />
                  <div className="text-sm">Conectar WhatsApp</div>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Demo Info Footer */}
      <div className="bg-blue-50 border-t border-blue-200 px-6 py-3">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          <p className="text-sm text-blue-700">
            <strong>Hooks React Query:</strong> Sistema otimizado de cache com invalidação automática, 
            chamadas paralelas, estados de loading/error e mutations com rollback em caso de falha. 
            Dados em tempo real com refetch automático.
          </p>
        </div>
      </div>
    </div>
  );
}