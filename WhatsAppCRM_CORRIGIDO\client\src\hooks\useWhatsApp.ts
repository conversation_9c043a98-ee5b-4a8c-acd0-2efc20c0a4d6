import { useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

interface WhatsAppStatus {
  isConnected: boolean;
  phoneNumber?: string;
}

interface WhatsAppConnection {
  success: boolean;
  message?: string;
  qr?: string;
  error?: string;
}

export function useWhatsApp(userId: number) {
  const [status, setStatus] = useState<WhatsAppStatus>({ isConnected: false });
  const [isConnecting, setIsConnecting] = useState(false);
  const [qrCode, setQrCode] = useState<string | null>(null);
  const { toast } = useToast();

  const getStatus = useCallback(async () => {
    try {
      const response = await apiRequest('GET', `/api/whatsapp/status/${userId}`);
      const statusData = await response.json();
      setStatus(statusData);
      return statusData;
    } catch (error) {
      console.error('Error getting WhatsApp status:', error);
      return { isConnected: false };
    }
  }, [userId]);

  const connect = useCallback(async () => {
    if (isConnecting) return;
    
    setIsConnecting(true);
    setQrCode(null);
    
    try {
      const response = await apiRequest('POST', '/api/whatsapp/connect', { userId });
      const result: WhatsAppConnection = await response.json();
      
      if (result.success) {
        if (result.qr) {
          setQrCode(result.qr);
          toast({
            title: "QR Code Gerado",
            description: "Escaneie o QR Code com seu WhatsApp para conectar",
          });
        } else {
          setStatus({ isConnected: true });
          toast({
            title: "Conectado",
            description: "WhatsApp conectado com sucesso!",
          });
        }
      } else {
        throw new Error(result.error || 'Falha ao conectar');
      }
    } catch (error) {
      console.error('Error connecting WhatsApp:', error);
      toast({
        title: "Erro de Conexão",
        description: error.message || "Falha ao conectar o WhatsApp",
        variant: "destructive"
      });
    } finally {
      setIsConnecting(false);
    }
  }, [userId, isConnecting, toast]);

  const disconnect = useCallback(async () => {
    try {
      await apiRequest('POST', '/api/whatsapp/disconnect', { userId });
      setStatus({ isConnected: false });
      setQrCode(null);
      
      toast({
        title: "Desconectado",
        description: "WhatsApp desconectado com sucesso",
      });
    } catch (error) {
      console.error('Error disconnecting WhatsApp:', error);
      toast({
        title: "Erro",
        description: "Falha ao desconectar o WhatsApp",
        variant: "destructive"
      });
    }
  }, [userId, toast]);

  const sendMessage = useCallback(async (chatId: string, content: string) => {
    try {
      const response = await apiRequest('POST', '/api/whatsapp/send-message', {
        userId,
        chatId,
        content
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast({
          title: "Mensagem Enviada",
          description: "Mensagem enviada com sucesso",
        });
        return true;
      } else {
        throw new Error(result.error || 'Falha ao enviar mensagem');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: "Erro",
        description: error.message || "Falha ao enviar mensagem",
        variant: "destructive"
      });
      return false;
    }
  }, [userId, toast]);

  const handleWebSocketMessage = useCallback((message: any) => {
    const { type, data } = message;
    
    switch (type) {
      case `qr_${userId}`:
        setQrCode(data.qr);
        break;
        
      case `connection_${userId}`:
        if (data.status === 'connected') {
          setStatus({
            isConnected: true,
            phoneNumber: data.phoneNumber
          });
          setQrCode(null);
          setIsConnecting(false);
          
          toast({
            title: "Conectado",
            description: `WhatsApp conectado: ${data.phoneNumber}`,
          });
        } else if (data.status === 'disconnected') {
          setStatus({ isConnected: false });
          setQrCode(null);
          setIsConnecting(false);
          
          if (data.shouldReconnect) {
            toast({
              title: "Desconectado",
              description: "Tentando reconectar...",
              variant: "destructive"
            });
          } else {
            toast({
              title: "Desconectado",
              description: "WhatsApp foi desconectado",
              variant: "destructive"
            });
          }
        }
        break;
    }
  }, [userId, toast]);

  return {
    status,
    isConnecting,
    qrCode,
    getStatus,
    connect,
    disconnect,
    sendMessage,
    handleWebSocketMessage
  };
}
