#!/bin/bash

# WhatsApp CRM - Script de Instalação Completo
# Versão: 2.0 - Totalmente Funcional
# Data: 30/06/2025

set -e

echo "🚀 INICIANDO INSTALAÇÃO DO WHATSAPP CRM SISTEMA COMPLETO"
echo "========================================================"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Verificar se está rodando como root
if [[ $EUID -eq 0 ]]; then
   error "Este script não deve ser executado como root. Execute como usuário normal."
fi

# Verificar sistema operacional
if [[ "$OSTYPE" != "linux-gnu"* ]]; then
    error "Este script é compatível apenas com Linux."
fi

# Detectar distribuição
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
else
    error "Não foi possível detectar a distribuição Linux."
fi

log "Sistema detectado: $OS $VER"

# Verificar se é Ubuntu/Debian
if [[ "$ID" != "ubuntu" && "$ID" != "debian" ]]; then
    warning "Este script foi testado apenas no Ubuntu/Debian. Continuando mesmo assim..."
fi

# Definir diretório de instalação
INSTALL_DIR="/opt/whatsappcrm"
SERVICE_USER="whatsappcrm"

log "Diretório de instalação: $INSTALL_DIR"

# 1. ATUALIZAR SISTEMA
log "Atualizando sistema..."
sudo apt update && sudo apt upgrade -y

# 2. INSTALAR DEPENDÊNCIAS BÁSICAS
log "Instalando dependências básicas..."
sudo apt install -y curl wget git build-essential software-properties-common apt-transport-https ca-certificates gnupg lsb-release

# 3. INSTALAR NODE.JS 20
log "Instalando Node.js 20..."
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs

# Verificar instalação do Node.js
NODE_VERSION=$(node --version)
NPM_VERSION=$(npm --version)
log "Node.js instalado: $NODE_VERSION"
log "NPM instalado: $NPM_VERSION"

# 4. INSTALAR POSTGRESQL
log "Instalando PostgreSQL..."
sudo apt install -y postgresql postgresql-contrib

# Iniciar e habilitar PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 5. CONFIGURAR POSTGRESQL
log "Configurando PostgreSQL..."

# Configurar senha do usuário postgres
sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'postgres';"

# Criar banco de dados
sudo -u postgres createdb whatsappcrm || true

# Configurar PostgreSQL para aceitar conexões locais
sudo sed -i "s/#listen_addresses = 'localhost'/listen_addresses = '*'/" /etc/postgresql/*/main/postgresql.conf
sudo sed -i "s/#port = 5432/port = 5432/" /etc/postgresql/*/main/postgresql.conf

# Configurar autenticação
sudo cp /etc/postgresql/*/main/pg_hba.conf /etc/postgresql/*/main/pg_hba.conf.backup
sudo sed -i 's/local   all             postgres                                peer/local   all             postgres                                md5/' /etc/postgresql/*/main/pg_hba.conf
sudo sed -i 's/local   all             all                                     peer/local   all             all                                     md5/' /etc/postgresql/*/main/pg_hba.conf

# Reiniciar PostgreSQL
sudo systemctl restart postgresql

# Testar conexão
log "Testando conexão com PostgreSQL..."
PGPASSWORD=postgres psql -h localhost -U postgres -d whatsappcrm -c "SELECT 1;" || error "Falha ao conectar com PostgreSQL"

# 6. INSTALAR PM2
log "Instalando PM2..."
sudo npm install -g pm2

# 7. CRIAR USUÁRIO DO SISTEMA
log "Criando usuário do sistema..."
sudo useradd -r -s /bin/bash -d $INSTALL_DIR $SERVICE_USER || true
sudo mkdir -p $INSTALL_DIR
sudo chown $SERVICE_USER:$SERVICE_USER $INSTALL_DIR

# 8. COPIAR ARQUIVOS DO PROJETO
log "Copiando arquivos do projeto..."
sudo cp -r . $INSTALL_DIR/
sudo chown -R $SERVICE_USER:$SERVICE_USER $INSTALL_DIR

# 9. INSTALAR DEPENDÊNCIAS DO PROJETO
log "Instalando dependências do projeto..."
cd $INSTALL_DIR
sudo -u $SERVICE_USER npm install

# 10. CONFIGURAR VARIÁVEIS DE AMBIENTE
log "Configurando variáveis de ambiente..."
sudo -u $SERVICE_USER tee $INSTALL_DIR/.env > /dev/null <<EOF
# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/whatsappcrm
DB_HOST=localhost
DB_PORT=5432
DB_NAME=whatsappcrm
DB_USER=postgres
DB_PASSWORD=postgres

# Server Configuration
PORT=5000
NODE_ENV=production
HOST=0.0.0.0

# WhatsApp Configuration
SESSIONS_DIR=$INSTALL_DIR/sessions
UPLOADS_DIR=$INSTALL_DIR/uploads

# Security
JWT_SECRET=whatsappcrm_jwt_secret_$(openssl rand -hex 32)
SESSION_SECRET=whatsappcrm_session_secret_$(openssl rand -hex 32)

# CORS
CORS_ORIGIN=*

# Logging
LOG_LEVEL=info
LOG_DIR=$INSTALL_DIR/logs
EOF

# 11. CRIAR DIRETÓRIOS NECESSÁRIOS
log "Criando diretórios necessários..."
sudo -u $SERVICE_USER mkdir -p $INSTALL_DIR/sessions
sudo -u $SERVICE_USER mkdir -p $INSTALL_DIR/uploads
sudo -u $SERVICE_USER mkdir -p $INSTALL_DIR/logs
sudo -u $SERVICE_USER mkdir -p $INSTALL_DIR/backups

# 12. CONFIGURAR BANCO DE DADOS
log "Configurando banco de dados..."
sudo -u $SERVICE_USER tee $INSTALL_DIR/setup_database.sql > /dev/null <<'EOF'
-- Criar tabelas do sistema

-- Tabela de usuários/vendedores
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(50),
    role VARCHAR(50) DEFAULT 'vendedor',
    is_active BOOLEAN DEFAULT true,
    whatsapp_connected BOOLEAN DEFAULT false,
    whatsapp_phone VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de leads/clientes
CREATE TABLE IF NOT EXISTS leads (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'novo',
    source VARCHAR(100),
    notes TEXT,
    assigned_to INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de tarefas do Kanban
CREATE TABLE IF NOT EXISTS kanban_tasks (
    id VARCHAR(255) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'todo',
    priority VARCHAR(50) DEFAULT 'medium',
    client_id INTEGER REFERENCES leads(id),
    assigned_to VARCHAR(255),
    due_date TIMESTAMP,
    tags TEXT[],
    position INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de mensagens do WhatsApp
CREATE TABLE IF NOT EXISTS whatsapp_messages (
    id SERIAL PRIMARY KEY,
    chat_id VARCHAR(255) NOT NULL,
    message_id VARCHAR(255) NOT NULL,
    from_me BOOLEAN NOT NULL,
    content TEXT NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    user_id INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de sessões do WhatsApp
CREATE TABLE IF NOT EXISTS whatsapp_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) UNIQUE,
    is_connected BOOLEAN DEFAULT false,
    phone_number VARCHAR(50),
    last_activity TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de atividades/logs
CREATE TABLE IF NOT EXISTS activities (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    type VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Inserir usuário padrão
INSERT INTO users (name, email, phone, role) 
VALUES ('Administrador', '<EMAIL>', '11999999999', 'admin')
ON CONFLICT (email) DO NOTHING;

-- Inserir dados de exemplo
INSERT INTO leads (name, email, phone, status, source) VALUES
('João Silva', '<EMAIL>', '11987654321', 'novo', 'whatsapp'),
('Maria Santos', '<EMAIL>', '11876543210', 'contato', 'site'),
('Pedro Costa', '<EMAIL>', '11765432109', 'proposta', 'indicacao')
ON CONFLICT DO NOTHING;

-- Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_leads_phone ON leads(phone);
CREATE INDEX IF NOT EXISTS idx_leads_status ON leads(status);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_chat_id ON whatsapp_messages(chat_id);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_timestamp ON whatsapp_messages(timestamp);
CREATE INDEX IF NOT EXISTS idx_kanban_tasks_status ON kanban_tasks(status);
CREATE INDEX IF NOT EXISTS idx_activities_user_id ON activities(user_id);
CREATE INDEX IF NOT EXISTS idx_activities_created_at ON activities(created_at);
EOF

# Executar script SQL
PGPASSWORD=postgres psql -h localhost -U postgres -d whatsappcrm -f $INSTALL_DIR/setup_database.sql

# 13. BUILD DO FRONTEND
log "Fazendo build do frontend..."
cd $INSTALL_DIR
sudo -u $SERVICE_USER npm run build

# 14. CONFIGURAR PM2
log "Configurando PM2..."
sudo -u $SERVICE_USER tee $INSTALL_DIR/ecosystem.config.js > /dev/null <<EOF
module.exports = {
  apps: [{
    name: 'whatsapp-crm',
    script: './server/index.js',
    cwd: '$INSTALL_DIR',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: '$INSTALL_DIR/logs/error.log',
    out_file: '$INSTALL_DIR/logs/out.log',
    log_file: '$INSTALL_DIR/logs/combined.log',
    time: true,
    autorestart: true,
    max_restarts: 10,
    min_uptime: '10s',
    max_memory_restart: '1G'
  }]
};
EOF

# 15. CONFIGURAR SYSTEMD SERVICE
log "Configurando serviço systemd..."
sudo tee /etc/systemd/system/whatsappcrm.service > /dev/null <<EOF
[Unit]
Description=WhatsApp CRM System
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=forking
User=$SERVICE_USER
WorkingDirectory=$INSTALL_DIR
ExecStart=/usr/bin/pm2 start $INSTALL_DIR/ecosystem.config.js --no-daemon
ExecReload=/usr/bin/pm2 reload $INSTALL_DIR/ecosystem.config.js
ExecStop=/usr/bin/pm2 stop $INSTALL_DIR/ecosystem.config.js
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 16. CONFIGURAR FIREWALL
log "Configurando firewall..."
sudo ufw allow 5000/tcp || true
sudo ufw allow 5432/tcp || true

# 17. CONFIGURAR NGINX (OPCIONAL)
if command -v nginx &> /dev/null; then
    log "Configurando Nginx..."
    sudo tee /etc/nginx/sites-available/whatsappcrm > /dev/null <<EOF
server {
    listen 80;
    server_name _;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    location /socket.io/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
    
    sudo ln -sf /etc/nginx/sites-available/whatsappcrm /etc/nginx/sites-enabled/
    sudo nginx -t && sudo systemctl reload nginx
fi

# 18. INICIAR SERVIÇOS
log "Iniciando serviços..."
sudo systemctl daemon-reload
sudo systemctl enable whatsappcrm
sudo systemctl start whatsappcrm

# 19. VERIFICAR INSTALAÇÃO
log "Verificando instalação..."
sleep 10

# Verificar se o serviço está rodando
if sudo systemctl is-active --quiet whatsappcrm; then
    log "✅ Serviço WhatsApp CRM está rodando"
else
    error "❌ Falha ao iniciar o serviço WhatsApp CRM"
fi

# Verificar se a API está respondendo
if curl -s http://localhost:5000/api/health > /dev/null; then
    log "✅ API está respondendo"
else
    warning "⚠️ API não está respondendo ainda (pode levar alguns segundos)"
fi

# Verificar banco de dados
if PGPASSWORD=postgres psql -h localhost -U postgres -d whatsappcrm -c "SELECT COUNT(*) FROM leads;" > /dev/null 2>&1; then
    log "✅ Banco de dados está funcionando"
else
    error "❌ Problema com o banco de dados"
fi

# 20. INFORMAÇÕES FINAIS
echo ""
echo "🎉 INSTALAÇÃO CONCLUÍDA COM SUCESSO!"
echo "====================================="
echo ""
echo "📋 INFORMAÇÕES DO SISTEMA:"
echo "• URL de acesso: http://$(hostname -I | awk '{print $1}'):5000"
echo "• Diretório: $INSTALL_DIR"
echo "• Usuário do sistema: $SERVICE_USER"
echo "• Banco de dados: PostgreSQL (porta 5432)"
echo "• Logs: $INSTALL_DIR/logs/"
echo ""
echo "🔧 COMANDOS ÚTEIS:"
echo "• Verificar status: sudo systemctl status whatsappcrm"
echo "• Parar serviço: sudo systemctl stop whatsappcrm"
echo "• Iniciar serviço: sudo systemctl start whatsappcrm"
echo "• Reiniciar serviço: sudo systemctl restart whatsappcrm"
echo "• Ver logs: sudo journalctl -u whatsappcrm -f"
echo "• Ver logs PM2: sudo -u $SERVICE_USER pm2 logs"
echo ""
echo "📱 PRÓXIMOS PASSOS:"
echo "1. Acesse o sistema pelo navegador"
echo "2. Vá para a seção 'WhatsApp Web'"
echo "3. Clique em 'Conectar WhatsApp'"
echo "4. Escaneie o QR Code com seu celular"
echo "5. Comece a usar o sistema!"
echo ""
echo "🔒 SEGURANÇA:"
echo "• Configure um firewall adequado"
echo "• Use HTTPS em produção"
echo "• Faça backups regulares do banco de dados"
echo ""
echo "📞 SUPORTE:"
echo "• Logs do sistema: $INSTALL_DIR/logs/"
echo "• Configuração: $INSTALL_DIR/.env"
echo ""

# Mostrar IP para acesso
IP=$(hostname -I | awk '{print $1}')
echo "🌐 ACESSE O SISTEMA EM: http://$IP:5000"
echo ""

log "Instalação finalizada! O sistema está pronto para uso."

