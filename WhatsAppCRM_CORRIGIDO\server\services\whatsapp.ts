import { 
  ConnectionState, 
  DisconnectReason, 
  WAMessage, 
  isJidUser,
  extractMessageContent,
  getContentType,
  proto,
  delay,
  useMultiFileAuthState
} from '@whiskeysockets/baileys';
import { Boom } from '@hapi/boom';
import { storage } from '../storage';
import { broadcastToAll } from './websocket';
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';

interface WhatsAppClient {
  sock?: any;
  qr?: string;
  isConnected: boolean;
  userId: number;
  phoneNumber?: string;
}

class WhatsAppService {
  private clients: Map<number, WhatsAppClient> = new Map();
  private sessionsDir = path.join(process.cwd(), 'sessions');

  constructor() {
    // Ensure sessions directory exists
    if (!fs.existsSync(this.sessionsDir)) {
      fs.mkdirSync(this.sessionsDir, { recursive: true });
    }
  }

  async initializeClient(userId: number): Promise<{ success: boolean; qr?: string; error?: string }> {
    try {
      console.log(`🔐 INICIANDO AUTENTICAÇÃO WHATSAPP REAL PARA USUÁRIO ${userId}`);
      console.log(`🚫 NENHUMA SIMULAÇÃO SERÁ USADA - APENAS DADOS REAIS`);
      
      const existingClient = this.clients.get(userId);
      if (existingClient?.isConnected) {
        console.log(`✅ Cliente já conectado para usuário ${userId}`);
        return { success: true };
      }

      const sessionPath = path.join(this.sessionsDir, `session_${userId}`);
      console.log(`📁 Diretório de sessão: ${sessionPath}`);
      
      // Ensure session directory exists
      if (!fs.existsSync(sessionPath)) {
        fs.mkdirSync(sessionPath, { recursive: true });
        console.log(`📁 Criado diretório de sessão: ${sessionPath}`);
      }
      
      // Create auth state with Baileys v2 Multi-Device - REAL authentication only
      const { default: pino } = await import('pino');
      const logger = pino({ level: 'warn' });
      const authState = await (await import('@whiskeysockets/baileys')).useMultiFileAuthState(sessionPath);
      const { state, saveCreds } = authState;

      const { default: makeWASocket } = await import('@whiskeysockets/baileys');
      
      // Create a proper logger for Baileys
      const baileyLogger = {
        level: 'silent',
        child: () => baileyLogger,
        trace: () => {},
        debug: () => {},
        info: () => {},
        warn: () => {},
        error: () => {},
        fatal: () => {}
      };
      
      const sock = makeWASocket({
        auth: state,
        printQRInTerminal: false,
        logger: baileyLogger,
        browser: ['Casa das Camisetas CRM', 'Chrome', '1.0.0'],
        generateHighQualityLinkPreview: true,
      });

      const client: WhatsAppClient = {
        sock,
        isConnected: false,
        userId,
        qr: undefined
      };

      this.clients.set(userId, client);

      // Handle connection updates
      sock.ev.on('connection.update', async (update) => {
        await this.handleConnectionUpdate(userId, update, client);
      });

      // Handle credential updates
      sock.ev.on('creds.update', saveCreds);

      // Handle incoming messages
      sock.ev.on('messages.upsert', async (m) => {
        await this.handleIncomingMessages(userId, m);
      });

      // Wait for QR code or connection
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          resolve({ success: false, error: 'QR code generation timeout' });
        }, 30000);

        const checkConnection = () => {
          const currentClient = this.clients.get(userId);
          if (currentClient?.isConnected) {
            clearTimeout(timeout);
            resolve({ success: true });
          } else if (currentClient?.qr) {
            clearTimeout(timeout);
            resolve({ success: true, qr: currentClient.qr });
          } else {
            setTimeout(checkConnection, 1000);
          }
        };

        checkConnection();
      });

    } catch (error) {
      console.error(`Error initializing WhatsApp client for user ${userId}:`, error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  }

  private async handleConnectionUpdate(
    userId: number, 
    update: Partial<ConnectionState>, 
    client: WhatsAppClient
  ) {
    const { connection, lastDisconnect, qr } = update;

    if (qr) {
      console.log(`QR Code generated for user ${userId}`);
      try {
        const qrString = await QRCode.toString(qr, { type: 'svg' });
        client.qr = qrString;
        
        // Broadcast QR code to frontend
        broadcastToAll(`qr_${userId}`, { qr: qrString });
        
        // Show QR code instructions
        console.log('\n🔐 ESCANEIE COM SUA CONTA REAL DO WHATSAPP:');
        console.log('Este QR Code conecta sua conta real ao sistema. Nada será simulado.');
        console.log('Por favor, escaneie com o aplicativo do WhatsApp e aguarde a conexão...');
        console.log('QR Code também disponível na interface web em /api/whatsapp/qr/' + userId);
        
        // Also save QR as image for easy access
        const qrImagePath = path.join(this.sessionsDir, `qr_${userId}.png`);
        await QRCode.toFile(qrImagePath, qr);
        console.log(`QR Code salvo em: ${qrImagePath}`);
        
        // Wait for user to scan - this is REAL authentication, no simulation
        if (process.env.NODE_ENV === 'production') {
          await this.waitForUserInput();
        }
        
      } catch (error) {
        console.error('Error generating QR code:', error);
      }
    }

    if (connection === 'close') {
      const shouldReconnect = (lastDisconnect?.error as Boom)?.output?.statusCode !== DisconnectReason.loggedOut;
      
      console.log(`Connection closed for user ${userId}, reconnecting: ${shouldReconnect}`);
      
      client.isConnected = false;
      await storage.updateSessionStatus(userId, false);
      
      broadcastToAll(`connection_${userId}`, { 
        status: 'disconnected',
        shouldReconnect 
      });

      if (shouldReconnect) {
        // Reconnect after delay
        setTimeout(() => {
          this.initializeClient(userId);
        }, 5000);
      } else {
        // Logged out, remove client
        this.clients.delete(userId);
      }
    } else if (connection === 'open') {
      console.log(`WhatsApp connected successfully for user ${userId}`);
      
      client.isConnected = true;
      client.phoneNumber = client.sock?.user?.id?.split(':')[0];
      
      await storage.updateSessionStatus(userId, true, client.phoneNumber);
      
      broadcastToAll(`connection_${userId}`, { 
        status: 'connected',
        phoneNumber: client.phoneNumber 
      });

      // Save session data
      const sessionData = {
        phoneNumber: client.phoneNumber,
        connectedAt: new Date().toISOString()
      };
      
      await storage.createOrUpdateWhatsappSession(userId, sessionData);
    }
  }

  private async waitForUserInput(): Promise<void> {
    return new Promise((resolve) => {
      const stdin = process.stdin;
      stdin.setRawMode(true);
      stdin.resume();
      stdin.setEncoding('utf8');
      
      const onData = (key: string) => {
        if (key === '\r' || key === '\n') {
          stdin.removeListener('data', onData);
          stdin.setRawMode(false);
          stdin.pause();
          resolve();
        }
      };
      
      stdin.on('data', onData);
    });
  }

  private async handleIncomingMessages(userId: number, messageUpdate: any) {
    try {
      const messages = messageUpdate.messages;
      
      for (const message of messages) {
        if (message.key.fromMe) continue; // Skip messages sent by us
        
        const chatId = message.key.remoteJid;
        const messageId = message.key.id;
        const timestamp = new Date(message.messageTimestamp * 1000);
        
        // Extract message content
        const messageContent = extractMessageContent(message.message);
        const messageType = getContentType(message.message);
        let content = '';
        
        if (messageType === 'conversation') {
          content = message.message.conversation;
        } else if (messageType === 'extendedTextMessage') {
          content = message.message.extendedTextMessage.text;
        } else if (messageContent) {
          content = JSON.stringify(messageContent);
        }

        // Check if it's a user chat (not group)
        if (isJidUser(chatId)) {
          const phoneNumber = chatId.split('@')[0];
          
          // Find or create lead
          let lead = await storage.getLeadByPhone(phoneNumber);
          if (!lead) {
            lead = await storage.createLead({
              name: message.pushName || phoneNumber,
              phone: phoneNumber,
              status: 'new',
              source: 'whatsapp',
              assignedTo: userId
            });

            // Create activity
            await storage.createActivity({
              userId,
              leadId: lead.id,
              type: 'lead_created',
              description: `New lead created from WhatsApp message`
            });
          }

          // Find or create chat
          let chat = await storage.getChatByWhatsappId(chatId);
          if (!chat) {
            chat = await storage.createChat({
              leadId: lead.id,
              whatsappChatId: chatId,
              isGroup: false
            });
          }

          // Create message record
          await storage.createMessage({
            chatId: chat.id,
            whatsappMessageId: messageId,
            fromMe: false,
            content,
            messageType: messageType || 'text',
            timestamp,
            metadata: {
              pushName: message.pushName,
              originalMessage: message
            }
          });

          // Update chat last message time
          await storage.updateChatLastMessage(chat.id, timestamp);

          // Update lead notes with last contact info
          const currentNotes = lead.notes || '';
          const lastContactNote = `\n[${timestamp.toISOString()}] Última mensagem recebida`;
          await storage.updateLead(lead.id, {
            notes: currentNotes + lastContactNote
          });

          // Create activity
          await storage.createActivity({
            userId,
            leadId: lead.id,
            type: 'message_received',
            description: `New message: ${content.substring(0, 50)}${content.length > 50 ? '...' : ''}`
          });

          // Broadcast new message to all connected clients
          broadcastToAll('new_message', {
            chatId: chat.id,
            leadId: lead.id,
            message: {
              id: messageId,
              content,
              timestamp,
              fromMe: false,
              messageType
            }
          });

          console.log(`New message from ${phoneNumber}: ${content}`);
        }
      }
    } catch (error) {
      console.error('Error handling incoming messages:', error);
    }
  }

  async sendMessage(userId: number, chatId: string, content: string): Promise<{ success: boolean; error?: string }> {
    try {
      const client = this.clients.get(userId);
      
      if (!client || !client.isConnected || !client.sock) {
        return { success: false, error: 'WhatsApp client not connected' };
      }

      const result = await client.sock.sendMessage(chatId, { text: content });
      
      if (result) {
        // Find chat in database
        const chat = await storage.getChatByWhatsappId(chatId);
        if (chat) {
          // Create message record
          await storage.createMessage({
            chatId: chat.id,
            whatsappMessageId: result.key.id!,
            fromMe: true,
            content,
            messageType: 'text',
            timestamp: new Date(),
            metadata: { sent: true }
          });

          // Update chat last message time
          await storage.updateChatLastMessage(chat.id, new Date());

          // Broadcast sent message
          broadcastToAll('message_sent', {
            chatId: chat.id,
            message: {
              id: result.key.id,
              content,
              timestamp: new Date(),
              fromMe: true,
              messageType: 'text'
            }
          });
        }

        return { success: true };
      }

      return { success: false, error: 'Failed to send message' };
    } catch (error) {
      console.error('Error sending message:', error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  }

  async disconnectClient(userId: number): Promise<void> {
    const client = this.clients.get(userId);
    if (client?.sock) {
      await client.sock.logout();
      client.sock.end(undefined);
    }
    
    this.clients.delete(userId);
    await storage.updateSessionStatus(userId, false);
    
    broadcastToAll(`connection_${userId}`, { 
      status: 'disconnected',
      shouldReconnect: false 
    });
  }

  getClientStatus(userId: number): { isConnected: boolean; phoneNumber?: string } {
    const client = this.clients.get(userId);
    return {
      isConnected: client?.isConnected || false,
      phoneNumber: client?.phoneNumber
    };
  }

  getAllClientsStatus(): Record<number, { isConnected: boolean; phoneNumber?: string }> {
    const status: Record<number, { isConnected: boolean; phoneNumber?: string }> = {};
    
    for (const [userId, client] of Array.from(this.clients.entries())) {
      status[userId] = {
        isConnected: client.isConnected,
        phoneNumber: client.phoneNumber
      };
    }
    
    return status;
  }
}

export const whatsappService = new WhatsAppService();
