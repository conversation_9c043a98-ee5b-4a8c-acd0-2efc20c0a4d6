import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Phone, Video, MoreVertical } from "lucide-react";
import { getInitials } from "@/lib/utils";

interface ChatHeaderProps {
  clientName: string;
  clientPhone: string;
  isOnline?: boolean;
  lastSeen?: Date | string;
  onCall?: () => void;
  onVideoCall?: () => void;
  onMoreOptions?: () => void;
}

export default function ChatHeader({
  clientName,
  clientPhone,
  isOnline = false,
  lastSeen,
  onCall,
  onVideoCall,
  onMoreOptions
}: ChatHeaderProps) {
  const getStatusText = () => {
    if (isOnline) return "Online";
    if (lastSeen) {
      const date = new Date(lastSeen);
      const now = new Date();
      const diffHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
      
      if (diffHours < 1) return "Visto há alguns minutos";
      if (diffHours < 24) return `Visto há ${diffHours}h`;
      return `Visto em ${date.toLocaleDateString()}`;
    }
    return "Offline";
  };

  return (
    <div className="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Avatar className="w-10 h-10">
            <AvatarImage src="" alt={clientName} />
            <AvatarFallback className="bg-green-500 text-white">
              {getInitials(clientName)}
            </AvatarFallback>
          </Avatar>
          
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100">
              {clientName}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {getStatusText()}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onCall}
            className="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <Phone className="w-5 h-5" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onVideoCall}
            className="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <Video className="w-5 h-5" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onMoreOptions}
            className="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <MoreVertical className="w-5 h-5" />
          </Button>
        </div>
      </div>
    </div>
  );
}