import { useState } from "react";
import { 
  Clock, Plus, Calendar, Send, Edit3, Trash2, X, 
  <PERSON><PERSON><PERSON><PERSON>, AlertCircle, Pause, Play
} from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";

interface ScheduledMessagesProps {
  isOpen: boolean;
  onClose: () => void;
  whatsappNumber: string;
  userId: string;
}

interface ScheduledMessage {
  id: number;
  contactPhone: string;
  content: string;
  scheduledDate: string;
  status: 'pending' | 'sent' | 'failed' | 'cancelled';
  createdBy: string;
  createdAt: string;
}

export default function ScheduledMessages({ 
  isOpen, 
  onClose, 
  whatsappNumber, 
  userId 
}: ScheduledMessagesProps) {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingMessage, setEditingMessage] = useState<ScheduledMessage | null>(null);
  const [formData, setFormData] = useState({
    contactPhone: "",
    content: "",
    scheduledDate: "",
    scheduledTime: ""
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: scheduledMessages = [], isLoading } = useQuery({
    queryKey: ['/api/scheduled-messages', whatsappNumber],
    queryFn: async () => {
      const response = await fetch(`/api/scheduled-messages?whatsappNumber=${whatsappNumber}`);
      if (!response.ok) throw new Error('Failed to fetch scheduled messages');
      return response.json();
    },
    enabled: isOpen
  });

  const createMessageMutation = useMutation({
    mutationFn: async (messageData: any) => {
      const response = await fetch('/api/scheduled-messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...messageData,
          whatsappNumber,
          createdBy: userId,
          scheduledDate: new Date(`${messageData.scheduledDate}T${messageData.scheduledTime}`)
        })
      });
      if (!response.ok) throw new Error('Failed to create scheduled message');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/scheduled-messages'] });
      setShowCreateForm(false);
      setFormData({ contactPhone: "", content: "", scheduledDate: "", scheduledTime: "" });
      toast({
        title: "Mensagem agendada",
        description: "A mensagem foi agendada com sucesso"
      });
    }
  });

  const updateMessageMutation = useMutation({
    mutationFn: async ({ id, ...data }: any) => {
      const response = await fetch(`/api/scheduled-messages/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...data,
          scheduledDate: new Date(`${data.scheduledDate}T${data.scheduledTime}`)
        })
      });
      if (!response.ok) throw new Error('Failed to update scheduled message');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/scheduled-messages'] });
      setEditingMessage(null);
      setShowCreateForm(false);
      setFormData({ contactPhone: "", content: "", scheduledDate: "", scheduledTime: "" });
      toast({
        title: "Mensagem atualizada",
        description: "As alterações foram salvas"
      });
    }
  });

  const deleteMessageMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await fetch(`/api/scheduled-messages/${id}`, {
        method: 'DELETE'
      });
      if (!response.ok) throw new Error('Failed to delete scheduled message');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/scheduled-messages'] });
      toast({
        title: "Mensagem removida",
        description: "A mensagem agendada foi cancelada"
      });
    }
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'sent': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed': return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'cancelled': return <Pause className="w-4 h-4 text-gray-500" />;
      default: return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      pending: 'Pendente',
      sent: 'Enviada',
      failed: 'Falhou',
      cancelled: 'Cancelada'
    };
    return labels[status] || status;
  };

  const handleCreateMessage = () => {
    if (!formData.contactPhone.trim() || !formData.content.trim() || !formData.scheduledDate || !formData.scheduledTime) {
      toast({
        title: "Campos obrigatórios",
        description: "Preencha todos os campos obrigatórios",
        variant: "destructive"
      });
      return;
    }
    createMessageMutation.mutate(formData);
  };

  const handleUpdateMessage = () => {
    if (!editingMessage || !formData.contactPhone.trim() || !formData.content.trim()) return;
    updateMessageMutation.mutate({
      id: editingMessage.id,
      ...formData
    });
  };

  const handleEdit = (message: ScheduledMessage) => {
    setEditingMessage(message);
    const scheduledDate = new Date(message.scheduledDate);
    setFormData({
      contactPhone: message.contactPhone,
      content: message.content,
      scheduledDate: scheduledDate.toISOString().split('T')[0],
      scheduledTime: scheduledDate.toTimeString().slice(0, 5)
    });
    setShowCreateForm(true);
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-BR');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-5xl max-h-[90vh] mx-4 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <Clock className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold">Mensagens Agendadas</h2>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowCreateForm(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>Agendar Mensagem</span>
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex">
          {/* Messages List */}
          <div className="flex-1 overflow-y-auto p-6">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Carregando mensagens agendadas...</p>
              </div>
            ) : scheduledMessages.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Clock className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p>Nenhuma mensagem agendada</p>
                <p className="text-sm">Agende sua primeira mensagem</p>
              </div>
            ) : (
              <div className="space-y-4">
                {scheduledMessages.map((message: ScheduledMessage) => (
                  <div
                    key={message.id}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          {getStatusIcon(message.status)}
                          <span className="font-medium text-gray-900">
                            {message.contactPhone}
                          </span>
                          <span className="text-sm text-gray-500">
                            {getStatusLabel(message.status)}
                          </span>
                        </div>
                        <div className="text-sm text-gray-600">
                          Agendado para: {formatDateTime(message.scheduledDate)}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {message.status === 'pending' && (
                          <button
                            onClick={() => handleEdit(message)}
                            className="p-1 hover:bg-gray-100 rounded"
                            title="Editar"
                          >
                            <Edit3 className="w-4 h-4 text-gray-600" />
                          </button>
                        )}
                        <button
                          onClick={() => deleteMessageMutation.mutate(message.id)}
                          className="p-1 hover:bg-gray-100 rounded"
                          title="Cancelar"
                        >
                          <Trash2 className="w-4 h-4 text-red-600" />
                        </button>
                      </div>
                    </div>
                    <div className="text-gray-700 text-sm bg-gray-50 rounded p-2">
                      {message.content}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Create/Edit Form */}
          {showCreateForm && (
            <div className="w-96 border-l bg-gray-50 p-6">
              <h3 className="text-lg font-medium mb-4">
                {editingMessage ? 'Editar Mensagem' : 'Agendar Nova Mensagem'}
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Número do Contato
                  </label>
                  <input
                    type="text"
                    value={formData.contactPhone}
                    onChange={(e) => setFormData(prev => ({ ...prev, contactPhone: e.target.value }))}
                    placeholder="Ex: 5511999999999"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Data do Agendamento
                  </label>
                  <input
                    type="date"
                    value={formData.scheduledDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, scheduledDate: e.target.value }))}
                    min={new Date().toISOString().split('T')[0]}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Horário
                  </label>
                  <input
                    type="time"
                    value={formData.scheduledTime}
                    onChange={(e) => setFormData(prev => ({ ...prev, scheduledTime: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Mensagem
                  </label>
                  <textarea
                    value={formData.content}
                    onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                    placeholder="Digite a mensagem que será enviada..."
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 resize-none"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {formData.content.length} caracteres
                  </p>
                </div>

                <div className="flex space-x-3 pt-4">
                  <button
                    onClick={() => {
                      setShowCreateForm(false);
                      setEditingMessage(null);
                      setFormData({ contactPhone: "", content: "", scheduledDate: "", scheduledTime: "" });
                    }}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={editingMessage ? handleUpdateMessage : handleCreateMessage}
                    disabled={createMessageMutation.isPending || updateMessageMutation.isPending}
                    className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center space-x-2"
                  >
                    <Send className="w-4 h-4" />
                    <span>{editingMessage ? 'Atualizar' : 'Agendar'}</span>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}