// Messages hooks
export {
  useMessages,
  useMessagesByContact,
  useSendMessage,
  useCreateMessage,
} from './use-messages';

// Contacts hooks
export {
  useContacts,
  useContact,
  useWhatsAppChats,
  useCreateContact,
  useUpdateContact,
  useDeleteContact,
  useSaveWhatsAppContact,
} from './use-contacts';

// Tasks hooks
export {
  useTasks,
  useTask,
  useTasksByStatus,
  useTasksByPriority,
  useCreateTask,
  useUpdateTask,
  useDeleteTask,
  useBulkUpdateTasks,
} from './use-tasks';

// WhatsApp Sessions hooks
export {
  useWhatsAppStatus,
  useWhatsAppSessions,
  useWhatsAppProgress,
  useInitializeWhatsApp,
  useDisconnectWhatsApp,
  useBlockContact,
  useWhatsAppHealth,
  useRestartWhatsApp,
  useWhatsAppSession,
} from './use-whatsapp-sessions';

// Combined API hooks
export {
  useApiData,
  useDashboardData,
  useConversationData,
  useCRMData,
} from './use-api';

// Re-export existing hooks
export { useToast } from './use-toast';
export { useWhatsApp } from './use-whatsapp';
export { useWebSocket } from './use-websocket';