import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { 
  BarChart3, Users, DollarSign, TrendingUp, Clock, Phone, 
  Mail, MessageSquare, Calendar, Target, Award, Building2,
  Activity, ArrowUp, ArrowDown, Plus, Filter, Search
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function CrmDashboard() {
  const [timeRange, setTimeRange] = useState("30d");
  const [selectedTeam, setSelectedTeam] = useState("all");

  // Dashboard metrics
  const { data: dashboardData } = useQuery({
    queryKey: ['/api/crm/dashboard', timeRange, selectedTeam],
    queryFn: async () => {
      const params = new URLSearchParams({ period: timeRange });
      if (selectedTeam !== "all") params.append("teamId", selectedTeam);
      
      const response = await fetch(`/api/crm/dashboard?${params}`);
      return response.json();
    },
  });

  // Recent activities
  const { data: recentActivities } = useQuery({
    queryKey: ['/api/crm/activities', 'recent'],
    queryFn: async () => {
      const response = await fetch('/api/crm/activities?status=pending&limit=5');
      return response.json();
    },
  });

  // Deals by stage
  const { data: dealsData } = useQuery({
    queryKey: ['/api/crm/deals', 'pipeline'],
    queryFn: async () => {
      const response = await fetch('/api/crm/deals?isActive=true&limit=100');
      return response.json();
    },
  });

  const metrics = dashboardData?.metrics || {
    totalDeals: 0,
    wonDeals: { count: 0, value: 0 },
    lostDeals: 0,
    pendingActivities: 0,
    conversionRate: '0%',
    dealsByStage: []
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('pt-BR').format(value);
  };

  const getStageColor = (stage: string) => {
    const colors: Record<string, string> = {
      'novo': 'bg-blue-100 text-blue-800',
      'prospecção': 'bg-yellow-100 text-yellow-800',
      'proposta': 'bg-orange-100 text-orange-800',
      'negociação': 'bg-purple-100 text-purple-800',
      'fechado_ganho': 'bg-green-100 text-green-800',
      'fechado_perdido': 'bg-red-100 text-red-800'
    };
    return colors[stage] || 'bg-gray-100 text-gray-800';
  };

  const getStageName = (stage: string) => {
    const names: Record<string, string> = {
      'novo': 'Novo',
      'prospecção': 'Prospecção',
      'proposta': 'Proposta',
      'negociação': 'Negociação',
      'fechado_ganho': 'Fechado - Ganho',
      'fechado_perdido': 'Fechado - Perdido'
    };
    return names[stage] || stage;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
                <Building2 className="w-7 h-7 text-blue-600" />
                Casa das Camisetas CRM
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Dashboard de Vendas e Relacionamento | Desenvolvido por Kauã - Codestorm 2025
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">7 dias</SelectItem>
                  <SelectItem value="30d">30 dias</SelectItem>
                  <SelectItem value="90d">90 dias</SelectItem>
                  <SelectItem value="365d">1 ano</SelectItem>
                </SelectContent>
              </Select>
              
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                Nova Oportunidade
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm font-medium">Total de Negócios</p>
                  <p className="text-3xl font-bold">{formatNumber(metrics.totalDeals)}</p>
                  <p className="text-blue-100 text-sm mt-1">
                    <ArrowUp className="w-4 h-4 inline mr-1" />
                    +12% vs período anterior
                  </p>
                </div>
                <div className="w-12 h-12 bg-blue-400 bg-opacity-30 rounded-lg flex items-center justify-center">
                  <Target className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm font-medium">Vendas Fechadas</p>
                  <p className="text-3xl font-bold">{formatCurrency(metrics.wonDeals.value)}</p>
                  <p className="text-green-100 text-sm mt-1">
                    {metrics.wonDeals.count} negócios ganhos
                  </p>
                </div>
                <div className="w-12 h-12 bg-green-400 bg-opacity-30 rounded-lg flex items-center justify-center">
                  <Award className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm font-medium">Taxa de Conversão</p>
                  <p className="text-3xl font-bold">{metrics.conversionRate}</p>
                  <p className="text-purple-100 text-sm mt-1">
                    <TrendingUp className="w-4 h-4 inline mr-1" />
                    Meta: 25%
                  </p>
                </div>
                <div className="w-12 h-12 bg-purple-400 bg-opacity-30 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100 text-sm font-medium">Atividades Pendentes</p>
                  <p className="text-3xl font-bold">{formatNumber(metrics.pendingActivities)}</p>
                  <p className="text-orange-100 text-sm mt-1">
                    <Clock className="w-4 h-4 inline mr-1" />
                    Para hoje
                  </p>
                </div>
                <div className="w-12 h-12 bg-orange-400 bg-opacity-30 rounded-lg flex items-center justify-center">
                  <Activity className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Pipeline Visual */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5 text-blue-600" />
                Pipeline de Vendas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {metrics.dealsByStage?.map((stage: any) => (
                  <div key={stage.stage} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge className={getStageColor(stage.stage)}>
                        {getStageName(stage.stage)}
                      </Badge>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {stage.count} negócios
                      </span>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {formatCurrency(stage.value || 0)}
                      </p>
                    </div>
                  </div>
                ))}
                
                {(!metrics.dealsByStage || metrics.dealsByStage.length === 0) && (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <Target className="w-12 h-12 mx-auto mb-3 opacity-50" />
                    <p>Nenhum negócio encontrado</p>
                    <p className="text-sm">Crie sua primeira oportunidade</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Atividades Recentes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-orange-600" />
                Atividades Recentes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentActivities?.activities?.slice(0, 5).map((activity: any) => (
                  <div key={activity.id} className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0">
                      {activity.type === 'call' && <Phone className="w-4 h-4 text-blue-600" />}
                      {activity.type === 'email' && <Mail className="w-4 h-4 text-blue-600" />}
                      {activity.type === 'whatsapp' && <MessageSquare className="w-4 h-4 text-blue-600" />}
                      {activity.type === 'meeting' && <Calendar className="w-4 h-4 text-blue-600" />}
                      {!['call', 'email', 'whatsapp', 'meeting'].includes(activity.type) && 
                        <Activity className="w-4 h-4 text-blue-600" />}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {activity.subject}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {activity.scheduledAt ? 
                          new Date(activity.scheduledAt).toLocaleDateString('pt-BR') : 
                          'Sem data'}
                      </p>
                    </div>
                  </div>
                ))}
                
                {(!recentActivities?.activities || recentActivities.activities.length === 0) && (
                  <div className="text-center py-6 text-gray-500 dark:text-gray-400">
                    <Clock className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Nenhuma atividade pendente</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5 text-green-600" />
              Ações Rápidas
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <Button variant="outline" className="h-20 flex-col gap-2">
                <Users className="w-6 h-6" />
                <span className="text-xs">Novo Contato</span>
              </Button>
              
              <Button variant="outline" className="h-20 flex-col gap-2">
                <Target className="w-6 h-6" />
                <span className="text-xs">Nova Oportunidade</span>
              </Button>
              
              <Button variant="outline" className="h-20 flex-col gap-2">
                <Calendar className="w-6 h-6" />
                <span className="text-xs">Agendar Reunião</span>
              </Button>
              
              <Button variant="outline" className="h-20 flex-col gap-2">
                <Phone className="w-6 h-6" />
                <span className="text-xs">Registrar Ligação</span>
              </Button>
              
              <Button variant="outline" className="h-20 flex-col gap-2">
                <Mail className="w-6 h-6" />
                <span className="text-xs">Enviar Email</span>
              </Button>
              
              <Button variant="outline" className="h-20 flex-col gap-2">
                <BarChart3 className="w-6 h-6" />
                <span className="text-xs">Relatórios</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Performance Insights */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Insights de Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium">Tempo médio de fechamento</span>
                  </div>
                  <span className="text-sm font-bold text-green-600">18 dias</span>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm font-medium">Ticket médio</span>
                  </div>
                  <span className="text-sm font-bold text-blue-600">R$ 2.850</span>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-sm font-medium">Melhor fonte de leads</span>
                  </div>
                  <span className="text-sm font-bold text-purple-600">WhatsApp</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Metas do Mês</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Receita</span>
                    <span>R$ 45.000 / R$ 60.000</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '75%' }}></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Novos Contatos</span>
                    <span>24 / 30</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-blue-500 h-2 rounded-full" style={{ width: '80%' }}></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Ligações</span>
                    <span>156 / 200</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-orange-500 h-2 rounded-full" style={{ width: '78%' }}></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}