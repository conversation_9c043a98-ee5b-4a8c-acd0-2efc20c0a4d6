import { useState, useRef, useEffect, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { 
  Send, Mic, Smile, Paperclip, Check, CheckCheck, Forward, 
  Download, Star, Reply, MoreVertical, Play, Pause
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { formatTime } from "@/lib/utils";

interface Message {
  id: string;
  body: string;
  content?: string;
  timestamp: number;
  fromMe: boolean;
  from: string;
  to: string;
  ack?: number; // 0: pending, 1: sent, 2: delivered, 3: read
  type?: string;
  hasMedia?: boolean;
  mediaType?: string;
  mediaUrl?: string;
  mediaName?: string;
  mediaMimeType?: string;
  isStarred?: boolean;
  isForwarded?: boolean;
  mentionedIds?: string[];
  quotedMsgId?: string;
  authorName?: string;
  authorNumber?: string;
  location?: {
    latitude: number;
    longitude: number;
    description?: string;
  };
}

interface ChatMessagesProps {
  contactId: string;
  contactName: string;
}

export default function ChatMessages({ contactId, contactName }: ChatMessagesProps) {
  const [messageInput, setMessageInput] = useState("");
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const currentUserId = typeof window !== 'undefined' ? localStorage.getItem('userId') || '' : '';

  // Fetch messages for selected chat with paginated structure handling
  const { data: messagesData, isLoading: messagesLoading } = useQuery({
    queryKey: ["/api/whatsapp/messages", currentUserId, contactId],
    queryFn: async () => {
      if (!currentUserId || !contactId) return { data: [], total: 0, offset: 0 };
      const response = await fetch(`/api/whatsapp/messages?userId=${encodeURIComponent(currentUserId)}&chatId=${encodeURIComponent(contactId)}&limit=100`);
      if (!response.ok) throw new Error('Failed to fetch messages');
      const result = await response.json();
      
      // Handle both array and paginated responses
      if (Array.isArray(result)) {
        return { data: result, total: result.length, offset: 0 };
      }
      return result.data ? result : { data: result, total: Array.isArray(result) ? result.length : 0, offset: 0 };
    },
    enabled: !!currentUserId && !!contactId,
    refetchInterval: 5000,
  });

  // Extract messages array from paginated structure with proper fallback
  const messages = Array.isArray(messagesData?.data) ? messagesData.data : 
                   Array.isArray(messagesData) ? messagesData : [];

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: async ({ message }: { message: string }) => {
      const cleanPhone = contactId.replace('@c.us', '').replace('@g.us', '');
      const response = await fetch(`/api/whatsapp/send?userId=${encodeURIComponent(currentUserId)}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ clientPhone: cleanPhone, message })
      });
      if (!response.ok) throw new Error('Failed to send message');
      return response.json();
    },
    onSuccess: () => {
      setMessageInput("");
      queryClient.invalidateQueries({ queryKey: ["/api/whatsapp/messages"] });
      queryClient.invalidateQueries({ queryKey: ["/api/whatsapp/chats"] });
    },
    onError: (error) => {
      toast({
        title: "Erro ao enviar mensagem",
        description: "Não foi possível enviar a mensagem. Tente novamente.",
        variant: "destructive"
      });
    }
  });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Focus input when component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [contactId]);

  const handleSendMessage = useCallback(() => {
    if (!messageInput.trim() || sendMessageMutation.isPending) return;
    
    sendMessageMutation.mutate({
      message: messageInput.trim()
    });
  }, [messageInput, sendMessageMutation]);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getMessageStatusIcon = (message: Message) => {
    if (!message.fromMe) return null;
    
    switch (message.ack) {
      case 0: return <Check className="w-3 h-3 text-gray-400" />;
      case 1: return <Check className="w-3 h-3 text-gray-500" />;
      case 2: return <CheckCheck className="w-3 h-3 text-gray-500" />;
      case 3: return <CheckCheck className="w-3 h-3 text-blue-500" />;
      default: return <Check className="w-3 h-3 text-gray-400" />;
    }
  };

  const formatMessageTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) {
      return date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
    } else if (diffInDays === 1) {
      return 'Ontem';
    } else if (diffInDays < 7) {
      return date.toLocaleDateString('pt-BR', { weekday: 'long' });
    } else {
      return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit', year: 'numeric' });
    }
  };

  if (messagesLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-gray-500">Carregando mensagens...</div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3 bg-gray-50">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500">
              <p className="text-lg mb-2">Nenhuma mensagem ainda</p>
              <p className="text-sm">Envie a primeira mensagem para {contactName}</p>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message: Message, index: number) => {
              const showDateSeparator = index === 0 || 
                new Date(message.timestamp).toDateString() !== new Date(messages[index - 1].timestamp).toDateString();

              return (
                <div key={message.id}>
                  {/* Date Separator */}
                  {showDateSeparator && (
                    <div className="flex justify-center my-4">
                      <div className="bg-white px-3 py-1 rounded-lg shadow-sm text-xs text-gray-600">
                        {formatMessageTime(message.timestamp)}
                      </div>
                    </div>
                  )}

                  {/* Message Bubble */}
                  <div className={`flex ${message.fromMe ? 'justify-end' : 'justify-start'}`}>
                    <div
                      className={`max-w-xs lg:max-w-md px-3 py-2 rounded-lg shadow-sm ${
                        message.fromMe
                          ? 'bg-green-500 text-white'
                          : 'bg-white text-gray-800'
                      }`}
                    >
                      {/* Forwarded indicator */}
                      {message.isForwarded && (
                        <p className="text-xs text-gray-500 mb-1 flex items-center">
                          <Forward className="w-3 h-3 mr-1" />
                          Encaminhada
                        </p>
                      )}
                      
                      {/* Media content */}
                      {message.hasMedia && message.mediaUrl && (
                        <div className="mb-2">
                          {message.mediaType === 'image' ? (
                            <img 
                              src={message.mediaUrl} 
                              alt="Imagem"
                              className="max-w-full rounded-lg cursor-pointer hover:opacity-90"
                              onClick={() => window.open(message.mediaUrl, '_blank')}
                            />
                          ) : message.mediaType === 'sticker' ? (
                            <img 
                              src={message.mediaUrl} 
                              alt="Figurinha"
                              className="max-w-32 rounded-lg"
                            />
                          ) : message.mediaType === 'audio' ? (
                            <div className="flex items-center space-x-3 p-2 bg-black bg-opacity-10 rounded">
                              <button className="p-1 rounded-full bg-green-500 text-white">
                                <Play className="w-3 h-3" />
                              </button>
                              <div className="flex-1">
                                <div className="w-20 h-1 bg-gray-300 rounded"></div>
                              </div>
                              <span className="text-xs">0:15</span>
                            </div>
                          ) : (
                            <div className="flex items-center space-x-2 p-2 bg-black bg-opacity-10 rounded cursor-pointer hover:bg-opacity-20">
                              <Download className="w-4 h-4" />
                              <span className="text-sm">{message.mediaName || 'Arquivo'}</span>
                            </div>
                          )}
                        </div>
                      )}
                      
                      {/* Message text */}
                      {message.body && (
                        <p className="text-sm whitespace-pre-wrap break-words">{message.body}</p>
                      )}
                      
                      {/* Location */}
                      {message.location && (
                        <div className="mt-2 p-2 bg-black bg-opacity-10 rounded">
                          <p className="text-xs">📍 Localização</p>
                          {message.location.description && (
                            <p className="text-sm mt-1">{message.location.description}</p>
                          )}
                        </div>
                      )}
                      
                      {/* Message footer */}
                      <div className="flex items-center justify-end space-x-1 mt-1">
                        {message.isStarred && <Star className="w-3 h-3 text-yellow-400" />}
                        <span className="text-xs opacity-70">
                          {formatTime(new Date(message.timestamp))}
                        </span>
                        {getMessageStatusIcon(message)}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Message Input */}
      <div className="h-16 bg-gray-100 px-4 flex items-center space-x-3 border-t border-gray-300">
        {/* Emoji Button */}
        <div className="relative">
          <button 
            onClick={() => setShowEmojiPicker(!showEmojiPicker)}
            className="p-2 text-gray-600 hover:bg-gray-200 rounded-full transition-colors"
            title="Emojis"
          >
            <Smile className="w-5 h-5" />
          </button>
          {showEmojiPicker && (
            <div className="absolute bottom-12 left-0 bg-white border border-gray-200 rounded-lg p-4 shadow-lg z-10 w-80">
              <div className="grid grid-cols-8 gap-2 text-lg max-h-48 overflow-y-auto">
                {['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '❤️', '💕', '💖', '💗', '💙', '💚', '💛', '🧡', '💜', '🖤', '🤍', '🤎', '💯', '💢', '💥', '💫', '💦', '💨', '🕳️', '💬', '👁️‍🗨️', '🗨️', '🗯️', '💭', '💤', '👋', '🤚', '🖐️', '✋', '🖖', '👌', '🤌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👍', '👎', '👊', '✊', '🤛', '🤜'].map((emoji) => (
                  <button
                    key={emoji}
                    onClick={() => {
                      setMessageInput(prev => prev + emoji);
                      setShowEmojiPicker(false);
                      inputRef.current?.focus();
                    }}
                    className="hover:bg-gray-100 p-1 rounded transition-colors"
                    title={emoji}
                  >
                    {emoji}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Attachment Button */}
        <label className="p-2 text-gray-600 hover:bg-gray-200 rounded-full transition-colors cursor-pointer" title="Anexar arquivo">
          <Paperclip className="w-5 h-5" />
          <input 
            type="file" 
            className="hidden" 
            accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) {
                toast({
                  title: "Arquivo selecionado",
                  description: `${file.name} - Funcionalidade será implementada em breve`,
                });
              }
            }}
          />
        </label>
        
        {/* Message Input Field */}
        <div className="flex-1 relative">
          <input
            ref={inputRef}
            type="text"
            placeholder="Digite uma mensagem"
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
            onKeyPress={handleKeyPress}
            className="w-full px-4 py-2 bg-white border border-gray-300 rounded-full focus:outline-none focus:ring-1 focus:ring-green-500"
          />
        </div>
        
        {/* Send/Mic Button */}
        {messageInput.trim() ? (
          <button 
            onClick={handleSendMessage}
            disabled={sendMessageMutation.isPending}
            className="p-2 bg-green-600 text-white rounded-full hover:bg-green-700 disabled:opacity-50 transition-colors"
            title="Enviar mensagem"
          >
            <Send className="w-5 h-5" />
          </button>
        ) : (
          <button 
            onClick={() => {
              toast({
                title: "Gravação de áudio",
                description: "Funcionalidade será implementada em breve",
              });
            }}
            className="p-2 text-gray-600 hover:bg-gray-200 rounded-full transition-colors"
            title="Gravar áudio"
          >
            <Mic className="w-5 h-5" />
          </button>
        )}
      </div>
    </div>
  );
}