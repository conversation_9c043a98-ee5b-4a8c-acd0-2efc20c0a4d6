import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useWhatsApp } from "@/hooks/use-whatsapp";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { UserPlus, Search, Edit, Trash2, MessageSquare, Building, Mail } from "lucide-react";
import { formatPhone, getInitials, formatDate } from "@/lib/utils";
import { useState } from "react";
import AddClientModal from "@/components/modals/add-client-modal";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { Client } from "@shared/schema";

export default function CRM() {
  const { phoneNumber, isConnected } = useWhatsApp();
  const [showAddClient, setShowAddClient] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: clients = [], isLoading } = useQuery({
    queryKey: [`/api/clients/${phoneNumber}`],
    enabled: !!phoneNumber && isConnected,
  });

  const deleteClient = useMutation({
    mutationFn: async (clientId: number) => {
      return apiRequest("DELETE", `/api/clients/${clientId}`, { whatsappNumber: phoneNumber });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/clients/${phoneNumber}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/stats/${phoneNumber}`] });
      toast({
        title: "Sucesso!",
        description: "Cliente excluído com sucesso",
      });
    },
    onError: () => {
      toast({
        title: "Erro",
        description: "Erro ao excluir cliente",
        variant: "destructive",
      });
    }
  });

  const filteredClients = clients.filter((client: Client) =>
    client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.phone.includes(searchTerm) ||
    (client.email && client.email.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (!isConnected || !phoneNumber) {
    return (
      <div className="p-6">
        <Card className="max-w-md mx-auto">
          <CardContent className="pt-6 text-center">
            <MessageSquare className="h-16 w-16 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-slate-800 mb-2">
              WhatsApp não conectado
            </h3>
            <p className="text-slate-600">
              Conecte seu WhatsApp para gerenciar clientes
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="relative flex-1 sm:w-80">
              <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Buscar clientes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Button onClick={() => setShowAddClient(true)} className="sm:w-auto">
            <UserPlus className="w-4 h-4 mr-2" />
            Novo Cliente
          </Button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <UserPlus className="text-primary text-xl" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-slate-800">{clients.length}</p>
                  <p className="text-sm text-slate-500">Total de Clientes</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <MessageSquare className="text-secondary text-xl" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-slate-800">
                    {clients.filter((c: Client) => c.email).length}
                  </p>
                  <p className="text-sm text-slate-500">Com Email</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-violet-100 rounded-lg flex items-center justify-center">
                  <Building className="text-accent text-xl" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-slate-800">
                    {clients.filter((c: Client) => c.company).length}
                  </p>
                  <p className="text-sm text-slate-500">Com Empresa</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Clients List */}
        <Card>
          <CardHeader>
            <CardTitle>
              Clientes ({filteredClients.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="flex items-center space-x-4 p-4">
                      <div className="w-12 h-12 bg-slate-200 rounded-full"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-slate-200 rounded w-1/4"></div>
                        <div className="h-3 bg-slate-200 rounded w-1/6"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : filteredClients.length === 0 ? (
              <div className="text-center py-12">
                <UserPlus className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-slate-800 mb-2">
                  {searchTerm ? "Nenhum cliente encontrado" : "Nenhum cliente cadastrado"}
                </h3>
                <p className="text-slate-600 mb-4">
                  {searchTerm 
                    ? "Tente usar outros termos de busca"
                    : "Comece adicionando seu primeiro cliente"
                  }
                </p>
                {!searchTerm && (
                  <Button onClick={() => setShowAddClient(true)}>
                    <UserPlus className="w-4 h-4 mr-2" />
                    Adicionar Cliente
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredClients.map((client: Client) => (
                  <div
                    key={client.id}
                    className="flex items-center space-x-4 p-4 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors"
                  >
                    <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-white font-medium">
                      {getInitials(client.name)}
                    </div>
                    
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold text-slate-800">{client.name}</h3>
                        {client.company && (
                          <Badge variant="outline" className="text-xs">
                            {client.company}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-slate-500">
                        <div className="flex items-center space-x-1">
                          <MessageSquare className="h-3 w-3" />
                          <span>{formatPhone(client.phone)}</span>
                        </div>
                        
                        {client.email && (
                          <div className="flex items-center space-x-1">
                            <Mail className="h-3 w-3" />
                            <span>{client.email}</span>
                          </div>
                        )}
                        
                        <span>•</span>
                        <span>Criado em {formatDate(client.createdAt!)}</span>
                      </div>
                      
                      {client.notes && (
                        <p className="text-sm text-slate-600 line-clamp-1">
                          {client.notes}
                        </p>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => deleteClient.mutate(client.id)}
                        disabled={deleteClient.isPending}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <AddClientModal 
        isOpen={showAddClient} 
        onClose={() => setShowAddClient(false)} 
      />
    </>
  );
}
