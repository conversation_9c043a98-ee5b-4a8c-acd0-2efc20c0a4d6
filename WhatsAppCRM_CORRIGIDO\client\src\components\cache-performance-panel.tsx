import { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  HardDrive, 
  Trash2, 
  RefreshCw,
  MemoryStick,
  Clock,
  Activity,
  Zap,
  Database,
  Timer,
  CheckCircle2,
  AlertTriangle,
  TrendingUp,
  Image as ImageIcon,
  Video as VideoIcon,
  Music,
  FileText
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface CacheItem {
  id: string;
  type: 'image' | 'video' | 'audio' | 'document';
  url: string;
  size: number;
  lastAccessed: Date;
  accessCount: number;
  blob?: Blob;
  expiry: Date;
}

interface MemoryStats {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  timestamp: Date;
}

interface PerformanceMetrics {
  cacheHitRate: number;
  averageLoadTime: number;
  memoryUsage: number;
  activeItems: number;
  totalRequests: number;
  cacheSize: number;
}

export function CachePerformancePanel() {
  const [cache, setCache] = useState<Map<string, CacheItem>>(new Map());
  const [memoryStats, setMemoryStats] = useState<MemoryStats[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics>({
    cacheHitRate: 0,
    averageLoadTime: 0,
    memoryUsage: 0,
    activeItems: 0,
    totalRequests: 0,
    cacheSize: 0
  });
  const [validationTests, setValidationTests] = useState({
    mediaCache: false,
    memoryManagement: false,
    autoCleanup: false,
    performanceOptimization: false
  });
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [longHistoryItems, setLongHistoryItems] = useState<any[]>([]);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [lastCleanup, setLastCleanup] = useState<Date>(new Date());

  const { toast } = useToast();
  const cleanupIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const memoryMonitorRef = useRef<NodeJS.Timeout | null>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Cache Management Functions
  const addToCache = useCallback((id: string, type: 'image' | 'video' | 'audio' | 'document', url: string, blob?: Blob) => {
    const size = blob?.size || Math.floor(Math.random() * 5000000); // 0-5MB
    const item: CacheItem = {
      id,
      type,
      url,
      size,
      lastAccessed: new Date(),
      accessCount: 1,
      blob,
      expiry: new Date(Date.now() + 30 * 60 * 1000) // 30 minutes
    };

    setCache(prev => {
      const newCache = new Map(prev);
      newCache.set(id, item);
      return newCache;
    });

    setPerformanceMetrics(prev => ({
      ...prev,
      activeItems: prev.activeItems + 1,
      cacheSize: prev.cacheSize + size
    }));
  }, []);

  const getFromCache = useCallback((id: string): CacheItem | null => {
    const item = cache.get(id);
    if (!item) {
      setPerformanceMetrics(prev => ({
        ...prev,
        totalRequests: prev.totalRequests + 1
      }));
      return null;
    }

    // Check if expired
    if (item.expiry < new Date()) {
      cache.delete(id);
      setCache(new Map(cache));
      return null;
    }

    // Update access stats
    item.lastAccessed = new Date();
    item.accessCount += 1;
    
    setCache(prev => {
      const newCache = new Map(prev);
      newCache.set(id, item);
      return newCache;
    });

    setPerformanceMetrics(prev => ({
      ...prev,
      totalRequests: prev.totalRequests + 1,
      cacheHitRate: ((prev.totalRequests * prev.cacheHitRate + 1) / (prev.totalRequests + 1)) * 100
    }));

    return item;
  }, [cache]);

  const cleanupCache = useCallback(() => {
    const now = new Date();
    const expiredItems: string[] = [];
    const lruItems: CacheItem[] = [];

    // Find expired and LRU items
    cache.forEach((item, id) => {
      if (item.expiry < now) {
        expiredItems.push(id);
      } else {
        lruItems.push(item);
      }
    });

    // Sort by last accessed (LRU)
    lruItems.sort((a, b) => a.lastAccessed.getTime() - b.lastAccessed.getTime());

    let removedSize = 0;
    let removedCount = 0;

    // Remove expired items
    expiredItems.forEach(id => {
      const item = cache.get(id);
      if (item) {
        removedSize += item.size;
        removedCount++;
        cache.delete(id);
      }
    });

    // Remove LRU items if cache is too large (> 50MB)
    const maxCacheSize = 50 * 1024 * 1024; // 50MB
    let currentSize = Array.from(cache.values()).reduce((sum, item) => sum + item.size, 0);

    while (currentSize > maxCacheSize && lruItems.length > 0) {
      const itemToRemove = lruItems.shift();
      if (itemToRemove) {
        cache.delete(itemToRemove.id);
        currentSize -= itemToRemove.size;
        removedSize += itemToRemove.size;
        removedCount++;
      }
    }

    setCache(new Map(cache));
    setLastCleanup(now);

    setPerformanceMetrics(prev => ({
      ...prev,
      activeItems: cache.size,
      cacheSize: currentSize
    }));

    if (removedCount > 0) {
      toast({
        title: "Cache Limpo",
        description: `${removedCount} itens removidos (${(removedSize / 1024 / 1024).toFixed(1)} MB)`,
        variant: "default"
      });
    }

    if (!validationTests.autoCleanup) {
      setValidationTests(prev => ({ ...prev, autoCleanup: true }));
    }
  }, [cache, validationTests.autoCleanup, toast]);

  // Memory monitoring
  const updateMemoryStats = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const stats: MemoryStats = {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        timestamp: new Date()
      };

      setMemoryStats(prev => {
        const newStats = [...prev, stats];
        // Keep only last 100 measurements
        return newStats.slice(-100);
      });

      setPerformanceMetrics(prev => ({
        ...prev,
        memoryUsage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
      }));
    }
  }, []);

  // Generate large history for memory testing
  const generateLongHistory = useCallback(() => {
    const items = [];
    for (let i = 0; i < 1000; i++) {
      const type = ['image', 'video', 'audio', 'document'][Math.floor(Math.random() * 4)] as any;
      items.push({
        id: `item_${i}`,
        type,
        content: `Item ${i + 1} - ${type} content with some longer text to test memory usage during scrolling`,
        timestamp: new Date(Date.now() - (i * 60000)),
        hasMedia: Math.random() > 0.5,
        mediaUrl: `https://example.com/media/${i}.${type === 'image' ? 'jpg' : type === 'video' ? 'mp4' : type === 'audio' ? 'mp3' : 'pdf'}`
      });
    }
    setLongHistoryItems(items);

    if (!validationTests.memoryManagement) {
      setValidationTests(prev => ({ ...prev, memoryManagement: true }));
    }
  }, [validationTests.memoryManagement]);

  // Test media caching
  const testMediaCache = useCallback(async () => {
    const testUrls = [
      'https://example.com/image1.jpg',
      'https://example.com/video1.mp4',
      'https://example.com/audio1.mp3',
      'https://example.com/doc1.pdf'
    ];

    const startTime = performance.now();

    // First load - should cache
    for (let i = 0; i < testUrls.length; i++) {
      const url = testUrls[i];
      const type = ['image', 'video', 'audio', 'document'][i] as any;
      
      // Simulate loading
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Create mock blob
      const blob = new Blob([new ArrayBuffer(1024 * 1024)], { type: `${type}/*` });
      addToCache(`test_${i}`, type, url, blob);
    }

    // Second load - should hit cache
    let cacheHits = 0;
    for (let i = 0; i < testUrls.length; i++) {
      const cached = getFromCache(`test_${i}`);
      if (cached) cacheHits++;
    }

    const endTime = performance.now();
    const loadTime = endTime - startTime;

    setPerformanceMetrics(prev => ({
      ...prev,
      averageLoadTime: loadTime,
      cacheHitRate: (cacheHits / testUrls.length) * 100
    }));

    setValidationTests(prev => ({ ...prev, mediaCache: true }));

    toast({
      title: "Cache de Mídia Testado",
      description: `${cacheHits}/${testUrls.length} hits de cache (${loadTime.toFixed(0)}ms)`,
      variant: "default"
    });
  }, [addToCache, getFromCache, toast]);

  // Start monitoring
  const startMonitoring = useCallback(() => {
    setIsMonitoring(true);

    // Setup automatic cleanup every 30 seconds
    cleanupIntervalRef.current = setInterval(cleanupCache, 30000);

    // Setup memory monitoring every 1 second
    memoryMonitorRef.current = setInterval(updateMemoryStats, 1000);

    updateMemoryStats(); // Initial measurement

    toast({
      title: "Monitoramento Iniciado",
      description: "Cache e performance sendo monitorados",
      variant: "default"
    });
  }, [cleanupCache, updateMemoryStats, toast]);

  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false);

    if (cleanupIntervalRef.current) {
      clearInterval(cleanupIntervalRef.current);
      cleanupIntervalRef.current = null;
    }

    if (memoryMonitorRef.current) {
      clearInterval(memoryMonitorRef.current);
      memoryMonitorRef.current = null;
    }

    toast({
      title: "Monitoramento Parado",
      description: "Monitoramento de cache e performance interrompido",
      variant: "default"
    });
  }, [toast]);

  // Handle scroll events for memory testing
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    setScrollPosition(scrollTop);

    // Update memory stats during scroll
    updateMemoryStats();

    // Simulate loading more items when near bottom
    if (scrollTop + clientHeight >= scrollHeight - 100) {
      // This would trigger more data loading in a real scenario
      updateMemoryStats();
    }
  }, [updateMemoryStats]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (cleanupIntervalRef.current) {
        clearInterval(cleanupIntervalRef.current);
      }
      if (memoryMonitorRef.current) {
        clearInterval(memoryMonitorRef.current);
      }
    };
  }, []);

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'image': return <ImageIcon className="h-4 w-4" />;
      case 'video': return <VideoIcon className="h-4 w-4" />;
      case 'audio': return <Music className="h-4 w-4" />;
      case 'document': return <FileText className="h-4 w-4" />;
      default: return <HardDrive className="h-4 w-4" />;
    }
  };

  const completedTests = Object.values(validationTests).filter(Boolean).length;
  const totalTests = Object.keys(validationTests).length;

  const latestMemory = memoryStats[memoryStats.length - 1];
  const timeSinceCleanup = Math.floor((Date.now() - lastCleanup.getTime()) / 1000);

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Validação de Cache e Performance</h2>
        <Badge variant="outline" className="text-lg px-3 py-1">
          {completedTests}/{totalTests} Validações
        </Badge>
      </div>

      {/* Real-time Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <HardDrive className="h-8 w-8 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{cache.size}</div>
                <div className="text-sm text-muted-foreground">Itens em Cache</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <MemoryStick className="h-8 w-8 text-green-500" />
              <div>
                <div className="text-2xl font-bold">
                  {formatBytes(performanceMetrics.cacheSize)}
                </div>
                <div className="text-sm text-muted-foreground">Tamanho do Cache</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <TrendingUp className="h-8 w-8 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">
                  {performanceMetrics.cacheHitRate.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">Taxa de Acerto</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Activity className="h-8 w-8 text-orange-500" />
              <div>
                <div className="text-2xl font-bold">
                  {latestMemory ? (latestMemory.usedJSHeapSize / 1024 / 1024).toFixed(1) : '0'} MB
                </div>
                <div className="text-sm text-muted-foreground">Memória Usada</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Memory Usage Chart */}
      {memoryStats.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Uso de Memória em Tempo Real
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-32 flex items-end gap-1">
              {memoryStats.slice(-20).map((stat, index) => {
                const percentage = (stat.usedJSHeapSize / stat.jsHeapSizeLimit) * 100;
                return (
                  <div
                    key={index}
                    className="flex-1 bg-blue-500 rounded-t"
                    style={{ height: `${percentage}%` }}
                    title={`${(stat.usedJSHeapSize / 1024 / 1024).toFixed(1)} MB`}
                  />
                );
              })}
            </div>
            <div className="flex justify-between text-sm text-muted-foreground mt-2">
              <span>Últimos 20 segundos</span>
              <span>
                {latestMemory && `${((latestMemory.usedJSHeapSize / latestMemory.jsHeapSizeLimit) * 100).toFixed(1)}% usado`}
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Cache Management */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Controles de Cache</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={isMonitoring ? stopMonitoring : startMonitoring}
                variant={isMonitoring ? "destructive" : "default"}
              >
                {isMonitoring ? (
                  <>
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    Parar Monitoramento
                  </>
                ) : (
                  <>
                    <Activity className="h-4 w-4 mr-2" />
                    Iniciar Monitoramento
                  </>
                )}
              </Button>

              <Button onClick={testMediaCache} variant="outline">
                <Zap className="h-4 w-4 mr-2" />
                Testar Cache de Mídia
              </Button>

              <Button onClick={cleanupCache} variant="outline">
                <Trash2 className="h-4 w-4 mr-2" />
                Limpar Cache Agora
              </Button>

              <Button onClick={generateLongHistory} variant="outline">
                <Database className="h-4 w-4 mr-2" />
                Gerar Histórico Longo
              </Button>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Último cleanup:</span>
                <span className="font-mono">{timeSinceCleanup}s atrás</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Próximo cleanup:</span>
                <span className="font-mono">{30 - (timeSinceCleanup % 30)}s</span>
              </div>
              <Progress value={(timeSinceCleanup % 30) / 30 * 100} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Status dos Testes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { key: 'mediaCache', label: 'Cache de Mídia', desc: 'Armazenamento e reutilização' },
                { key: 'memoryManagement', label: 'Gerenciamento de Memória', desc: 'Uso eficiente durante scroll' },
                { key: 'autoCleanup', label: 'Limpeza Automática', desc: 'Cleanup a cada 30 segundos' },
                { key: 'performanceOptimization', label: 'Otimização de Performance', desc: 'Métricas e melhorias' }
              ].map((test) => (
                <div key={test.key} className="flex items-center gap-3">
                  {validationTests[test.key as keyof typeof validationTests] ? (
                    <CheckCircle2 className="h-5 w-5 text-green-600" />
                  ) : (
                    <Clock className="h-5 w-5 text-gray-400" />
                  )}
                  <div className="flex-1">
                    <div className="font-medium text-sm">{test.label}</div>
                    <div className="text-xs text-muted-foreground">{test.desc}</div>
                  </div>
                  <Badge variant={validationTests[test.key as keyof typeof validationTests] ? "default" : "secondary"}>
                    {validationTests[test.key as keyof typeof validationTests] ? "OK" : "Pendente"}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Cache Contents */}
      <Card>
        <CardHeader>
          <CardTitle>Conteúdo do Cache</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-64">
            <div className="space-y-2">
              {Array.from(cache.entries()).map(([id, item]) => (
                <div key={id} className="flex items-center gap-3 p-3 border rounded-lg">
                  {getTypeIcon(item.type)}
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm truncate">{id}</div>
                    <div className="text-xs text-muted-foreground">
                      {formatBytes(item.size)} • {item.accessCount} acessos • 
                      último: {item.lastAccessed.toLocaleTimeString()}
                    </div>
                  </div>
                  <Badge variant="outline">{item.type}</Badge>
                </div>
              ))}
              {cache.size === 0 && (
                <div className="text-center text-muted-foreground py-8">
                  Cache vazio - Execute os testes para popular
                </div>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Long History Scroll Test */}
      {longHistoryItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Teste de Scroll com Histórico Longo</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span>Posição do scroll:</span>
                <span className="font-mono">{scrollPosition}px</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Total de itens:</span>
                <span className="font-mono">{longHistoryItems.length}</span>
              </div>
            </div>
            
            <ScrollArea 
              ref={scrollContainerRef}
              className="h-64 border rounded-lg"
              onScrollCapture={handleScroll}
            >
              <div className="p-3 space-y-2">
                {longHistoryItems.map((item, index) => (
                  <div key={item.id} className="flex items-center gap-3 p-2 border rounded">
                    {getTypeIcon(item.type)}
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium">Item {index + 1}</div>
                      <div className="text-xs text-muted-foreground truncate">
                        {item.content}
                      </div>
                    </div>
                    {item.hasMedia && (
                      <Badge variant="secondary" className="text-xs">
                        {item.type}
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  );
}