import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { LeadCard } from './LeadCard';
import { apiRequest } from '@/lib/queryClient';
import { Lead, LeadStatus } from '@shared/schema';

const columns = [
  { 
    id: LeadStatus.NEW, 
    title: 'Novos Leads', 
    color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' 
  },
  { 
    id: LeadStatus.CONTACT, 
    title: 'Em Contato', 
    color: 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200' 
  },
  { 
    id: LeadStatus.PROPOSAL, 
    title: 'Proposta Enviada', 
    color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' 
  },
  { 
    id: LeadStatus.CLOSED, 
    title: 'Fecha<PERSON>', 
    color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
  },
];

interface KanbanBoardProps {
  onAddLead: () => void;
}

export function KanbanBoard({ onAddLead }: KanbanBoardProps) {
  const [draggedLead, setDraggedLead] = useState<Lead | null>(null);
  const queryClient = useQueryClient();

  const { data: leads = [], isLoading } = useQuery({
    queryKey: ['/api/leads'],
    refetchInterval: 10000, // Refresh every 10 seconds
  });

  const updateLeadMutation = useMutation({
    mutationFn: async ({ leadId, status }: { leadId: number; status: string }) => {
      const response = await apiRequest('PUT', `/api/leads/${leadId}`, { status });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/leads'] });
    },
  });

  const handleDragStart = (lead: Lead) => {
    setDraggedLead(lead);
  };

  const handleDragEnd = () => {
    setDraggedLead(null);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, targetStatus: string) => {
    e.preventDefault();
    
    if (draggedLead && draggedLead.status !== targetStatus) {
      updateLeadMutation.mutate({
        leadId: draggedLead.id,
        status: targetStatus
      });
    }
    
    setDraggedLead(null);
  };

  const getLeadsByStatus = (status: string) => {
    return leads.filter((lead: Lead) => lead.status === status);
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="kanban-column">
            <div className="kanban-header">
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
            <div className="kanban-content">
              {Array.from({ length: 3 }).map((_, cardIndex) => (
                <div key={cardIndex} className="h-24 bg-gray-100 dark:bg-gray-700 rounded-lg animate-pulse"></div>
              ))}
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Pipeline de Vendas
        </h3>
        <Button 
          onClick={onAddLead}
          className="bg-blue-600 hover:bg-blue-700 text-white flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Novo Lead</span>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {columns.map((column) => {
          const columnLeads = getLeadsByStatus(column.id);
          
          return (
            <div 
              key={column.id} 
              className="kanban-column"
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, column.id)}
            >
              <div className="kanban-header">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold text-gray-900 dark:text-white">
                    {column.title}
                  </h4>
                  <span className={`status-badge ${column.color}`}>
                    {columnLeads.length}
                  </span>
                </div>
              </div>
              
              <div className="kanban-content">
                {columnLeads.map((lead: Lead) => (
                  <LeadCard
                    key={lead.id}
                    lead={lead}
                    onDragStart={() => handleDragStart(lead)}
                    onDragEnd={handleDragEnd}
                    isDragging={draggedLead?.id === lead.id}
                  />
                ))}
                
                {columnLeads.length === 0 && (
                  <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                    <p className="text-sm">Nenhum lead nesta etapa</p>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
