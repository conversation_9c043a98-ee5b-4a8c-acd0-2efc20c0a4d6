import { useState } from "react";
import WhatsAppChat from "@/components/WhatsAppChat";
import { ArrowLeft, MessageSquare } from "lucide-react";
import { useLocation } from "wouter";

// Mock data para demonstração
const mockContacts = [
  {
    id: "5511999887766",
    name: "<PERSON>",
    phone: "5511999887766",
    avatar: undefined,
    isOnline: true,
    lastSeen: "agora"
  },
  {
    id: "5511888776655",
    name: "<PERSON>", 
    phone: "5511888776655",
    avatar: undefined,
    isOnline: false,
    lastSeen: "hoje às 14:30"
  },
  {
    id: "5511777665544",
    name: "<PERSON>",
    phone: "5511777665544", 
    avatar: undefined,
    isOnline: false,
    lastSeen: "ontem às 20:15"
  }
];

export default function WhatsAppChatDemo() {
  const [selectedContact, setSelectedContact] = useState(mockContacts[0]);
  const [, setLocation] = useLocation();

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setLocation('/')}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </button>
          <div>
            <h1 className="text-xl font-semibold text-gray-900">
              Chat do WhatsApp Web
            </h1>
            <p className="text-sm text-gray-500">
              Componente idêntico ao WhatsApp oficial
            </p>
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Lista de Contatos */}
        <div className="w-80 bg-white border-r border-gray-200">
          <div className="p-4 border-b border-gray-200">
            <h3 className="font-medium text-gray-900 mb-3">Conversas</h3>
          </div>
          
          <div className="overflow-y-auto">
            {mockContacts.map((contact) => (
              <button
                key={contact.id}
                onClick={() => setSelectedContact(contact)}
                className={`w-full p-4 text-left hover:bg-gray-50 border-b border-gray-100 ${
                  selectedContact.id === contact.id ? 'bg-green-50 border-l-4 border-l-green-500' : ''
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-gray-600">
                        {contact.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </span>
                    </div>
                    {contact.isOnline && (
                      <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 truncate">{contact.name}</h4>
                    <p className="text-sm text-gray-500 truncate">
                      {contact.isOnline ? "online" : `visto por último ${contact.lastSeen}`}
                    </p>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Área do Chat */}
        <div className="flex-1">
          {selectedContact ? (
            <WhatsAppChat
              contactId={selectedContact.id}
              contactName={selectedContact.name}
              contactPhone={selectedContact.phone}
              contactAvatar={selectedContact.avatar}
              isOnline={selectedContact.isOnline}
              lastSeen={selectedContact.lastSeen}
            />
          ) : (
            <div className="flex-1 flex items-center justify-center bg-[#efeae2]">
              <div className="text-center">
                <MessageSquare className="w-24 h-24 text-gray-300 mx-auto mb-6" />
                <h2 className="text-2xl font-light text-gray-600 mb-2">WhatsApp Web</h2>
                <p className="text-gray-500 max-w-md">
                  Selecione uma conversa para começar a enviar mensagens.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Informações da Demo */}
      <div className="bg-blue-50 border-t border-blue-200 px-6 py-3">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          <p className="text-sm text-blue-700">
            <strong>Demo:</strong> Esta é uma demonstração do componente de chat idêntico ao WhatsApp Web com 
            mensagens enviadas/recebidas, timestamps, status de leitura (✓✓), suporte a anexos, 
            scroll automático e picker de emojis.
          </p>
        </div>
      </div>
    </div>
  );
}