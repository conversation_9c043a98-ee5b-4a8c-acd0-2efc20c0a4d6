import { useState, useEffect } from "react";
import { Search, MoreVertical, ArrowLeft, Settings, Users, MessageSquare, Phone, VideoIcon, Star, Archive, VolumeX, Trash2, Pin, Clock, CheckCircle2, AlertCircle, XCircle } from "lucide-react";
import { useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { useWhatsApp } from "@/hooks/use-whatsapp";
import { formatTime, getInitials } from "@/lib/utils";

interface WhatsAppLayoutProps {
  children?: React.ReactNode;
  selectedContact?: string;
  onContactSelect?: (contactId: string, contactName: string) => void;
}

interface Contact {
  id: string;
  name: string;
  phone: string;
  lastMessage?: {
    body: string;
    timestamp: number;
    fromMe: boolean;
  };
  unreadCount: number;
  isOnline: boolean;
  isGroup: boolean;
  profilePicUrl?: string;
  timestamp: number;
}

interface Task {
  id: number;
  title: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed';
  priority: 'low' | 'medium' | 'high';
  dueDate: Date;
  clientId?: number;
  whatsappNumber: string;
  createdAt: Date;
  updatedAt: Date;
}

export default function WhatsAppLayout({ children, selectedContact = "", onContactSelect }: WhatsAppLayoutProps) {
  const [, setLocation] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [showContactInfo, setShowContactInfo] = useState(false);
  const [activeTab, setActiveTab] = useState<'chats' | 'crm' | 'tasks'>('chats');
  const { isConnected, phoneNumber } = useWhatsApp();

  const currentUserId = 'user_1749665821961'; // ID da sessão conectada

  // Fetch WhatsApp chats
  const { data: contacts = [] } = useQuery({
    queryKey: ["/api/whatsapp/chats", currentUserId],
    queryFn: async () => {
      if (!currentUserId || !isConnected) return [];
      const response = await fetch(`/api/whatsapp/chats?userId=${encodeURIComponent(currentUserId)}`);
      if (!response.ok) throw new Error('Failed to fetch chats');
      return response.json();
    },
    enabled: !!currentUserId && isConnected,
    refetchInterval: 30000,
  });

  // Fetch CRM clients
  const { data: crmClients = [] } = useQuery({
    queryKey: ["/api/clients", phoneNumber],
    queryFn: async () => {
      if (!phoneNumber) return [];
      const response = await fetch(`/api/clients/${phoneNumber}`);
      if (!response.ok) throw new Error('Failed to fetch clients');
      return response.json();
    },
    enabled: !!phoneNumber,
  });

  // Fetch tasks
  const { data: tasks = [] } = useQuery({
    queryKey: ["/api/tasks", phoneNumber],
    queryFn: async () => {
      if (!phoneNumber) return [];
      const response = await fetch(`/api/tasks/${phoneNumber}`);
      if (!response.ok) throw new Error('Failed to fetch tasks');
      return response.json();
    },
    enabled: !!phoneNumber,
  });

  const filteredContacts = contacts.filter((contact: Contact) =>
    contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contact.phone?.includes(searchQuery)
  );

  const selectedContactData = contacts.find((contact: Contact) => contact.id === selectedContact);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle2 className="w-4 h-4 text-green-500" />;
      case 'in_progress': return <Clock className="w-4 h-4 text-blue-500" />;
      case 'pending': return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      default: return <XCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-l-red-500 bg-red-50';
      case 'medium': return 'border-l-yellow-500 bg-yellow-50';
      case 'low': return 'border-l-green-500 bg-green-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Left Sidebar - Contacts & CRM */}
      <div className="w-80 bg-white border-r border-gray-300 flex flex-col">
        {/* Header */}
        <div className="h-14 bg-gray-100 px-4 flex items-center justify-between border-b border-gray-300">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setLocation('/')}
              className="p-1 text-gray-600 hover:bg-gray-200 rounded-full transition-colors"
              title="Voltar ao menu"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              {phoneNumber ? getInitials(phoneNumber) : <Users className="w-4 h-4" />}
            </div>
          </div>
          
          <div className="flex items-center space-x-1">
            <button className="p-2 text-gray-600 hover:bg-gray-200 rounded-full">
              <MessageSquare className="w-5 h-5" />
            </button>
            <button className="p-2 text-gray-600 hover:bg-gray-200 rounded-full">
              <MoreVertical className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="p-3 border-b border-gray-200">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Pesquisar ou começar uma nova conversa"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-100 border-0 rounded-lg focus:outline-none focus:bg-white focus:ring-1 focus:ring-green-500"
            />
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('chats')}
            className={`flex-1 py-3 px-4 text-sm font-medium ${
              activeTab === 'chats' 
                ? 'text-green-600 border-b-2 border-green-600' 
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            Conversas
          </button>
          <button
            onClick={() => setActiveTab('crm')}
            className={`flex-1 py-3 px-4 text-sm font-medium ${
              activeTab === 'crm' 
                ? 'text-green-600 border-b-2 border-green-600' 
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            CRM
          </button>
          <button
            onClick={() => setActiveTab('tasks')}
            className={`flex-1 py-3 px-4 text-sm font-medium ${
              activeTab === 'tasks' 
                ? 'text-green-600 border-b-2 border-green-600' 
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            Tarefas
          </button>
        </div>

        {/* Content List */}
        <div className="flex-1 overflow-y-auto">
          {activeTab === 'chats' && (
            <>
              {filteredContacts.length === 0 ? (
                <div className="p-6 text-center text-gray-500">
                  {searchQuery ? 'Nenhuma conversa encontrada' : 'Nenhuma conversa ainda'}
                </div>
              ) : (
                filteredContacts.map((contact: Contact) => (
                  <div
                    key={contact.id}
                    onClick={() => onContactSelect?.(contact.id, contact.name)}
                    className={`p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors ${
                      selectedContact === contact.id ? 'bg-gray-100' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        {contact.profilePicUrl ? (
                          <img 
                            src={contact.profilePicUrl} 
                            alt={contact.name}
                            className="w-12 h-12 rounded-full object-cover"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                              e.currentTarget.nextElementSibling?.classList.remove('hidden');
                            }}
                          />
                        ) : null}
                        <div className={`w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 font-medium ${contact.profilePicUrl ? 'hidden' : ''}`}>
                          {contact.isGroup ? <Users className="w-6 h-6" /> : getInitials(contact.name)}
                        </div>
                        {contact.isOnline && !contact.isGroup && (
                          <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                        )}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-gray-900 truncate">{contact.name}</h4>
                          <span className="text-xs text-gray-500">
                            {contact.lastMessage && formatTime(new Date(contact.lastMessage.timestamp))}
                          </span>
                        </div>
                        
                        <div className="flex items-center justify-between mt-1">
                          <p className="text-sm text-gray-600 truncate">
                            {contact.lastMessage ? (
                              <>
                                {contact.lastMessage.fromMe && <span className="text-green-600">Você: </span>}
                                {contact.lastMessage.body}
                              </>
                            ) : (
                              <span className="text-gray-400">Nenhuma mensagem</span>
                            )}
                          </p>
                          {contact.unreadCount > 0 && (
                            <div className="bg-green-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                              {contact.unreadCount > 99 ? '99+' : contact.unreadCount}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </>
          )}

          {activeTab === 'crm' && (
            <>
              {crmClients.length === 0 ? (
                <div className="p-6 text-center text-gray-500">
                  Nenhum cliente cadastrado ainda
                </div>
              ) : (
                crmClients.map((client: any) => (
                  <div
                    key={client.id}
                    className="p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 font-medium text-sm">
                          {getInitials(client.name)}
                        </span>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{client.name}</h4>
                        <p className="text-sm text-gray-600">{client.phone}</p>
                        {client.email && (
                          <p className="text-xs text-gray-500">{client.email}</p>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </>
          )}

          {activeTab === 'tasks' && (
            <>
              {tasks.length === 0 ? (
                <div className="p-6 text-center text-gray-500">
                  Nenhuma tarefa cadastrada ainda
                </div>
              ) : (
                tasks.map((task: Task) => (
                  <div
                    key={task.id}
                    className={`p-3 border-b border-gray-100 border-l-4 ${getPriorityColor(task.priority)}`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(task.status)}
                          <h4 className="font-medium text-gray-900 text-sm">{task.title}</h4>
                        </div>
                        <p className="text-xs text-gray-600 mt-1 line-clamp-2">{task.description}</p>
                        <div className="flex items-center justify-between mt-2">
                          <span className="text-xs text-gray-500">
                            {formatTime(new Date(task.dueDate))}
                          </span>
                          <span className={`text-xs px-2 py-1 rounded-full ${{
                            'high': 'bg-red-100 text-red-800',
                            'medium': 'bg-yellow-100 text-yellow-800',
                            'low': 'bg-green-100 text-green-800'
                          }[task.priority]}`}>
                            {task.priority}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </>
          )}
        </div>
      </div>

      {/* Center Area - Chat */}
      <div className="flex-1 flex flex-col">
        {selectedContact && selectedContactData ? (
          <>
            {/* Chat Header */}
            <div className="h-14 bg-gray-100 px-4 flex items-center justify-between border-b border-gray-300">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  {selectedContactData.profilePicUrl ? (
                    <img 
                      src={selectedContactData.profilePicUrl} 
                      alt={selectedContactData.name}
                      className="w-8 h-8 rounded-full object-cover"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.nextElementSibling?.classList.remove('hidden');
                      }}
                    />
                  ) : null}
                  <div className={`w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 text-sm ${selectedContactData.profilePicUrl ? 'hidden' : ''}`}>
                    {selectedContactData.isGroup ? <Users className="w-4 h-4" /> : getInitials(selectedContactData.name)}
                  </div>
                </div>
                
                <div>
                  <h3 className="font-medium text-gray-900">{selectedContactData.name}</h3>
                  <p className="text-xs text-gray-500">
                    {selectedContactData.isOnline ? 'online' : 'última vez hoje às 15:30'}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-1">
                <button className="p-2 text-gray-600 hover:bg-gray-200 rounded-full">
                  <Phone className="w-5 h-5" />
                </button>
                <button className="p-2 text-gray-600 hover:bg-gray-200 rounded-full">
                  <VideoIcon className="w-5 h-5" />
                </button>
                <button 
                  onClick={() => setShowContactInfo(!showContactInfo)}
                  className="p-2 text-gray-600 hover:bg-gray-200 rounded-full"
                >
                  <MoreVertical className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Messages Area */}
            <div className="flex-1">
              {children}
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <MessageSquare className="w-24 h-24 text-gray-300 mx-auto mb-6" />
              <h2 className="text-2xl font-light text-gray-600 mb-2">WhatsApp Web</h2>
              <p className="text-gray-500 max-w-md">
                Envie e receba mensagens sem manter seu telefone conectado à internet.
              </p>
              <p className="text-sm text-gray-400 mt-4">
                Selecione uma conversa para começar a enviar mensagens.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Right Sidebar - Contact Info & CRM */}
      {showContactInfo && selectedContactData && (
        <div className="w-80 bg-white border-l border-gray-300 p-0">
          {/* Contact Info Header */}
          <div className="h-14 bg-gray-100 px-4 flex items-center border-b border-gray-300">
            <h3 className="font-medium text-gray-900">Informações do contato</h3>
          </div>

          {/* Contact Details */}
          <div className="p-6 border-b border-gray-200">
            <div className="text-center mb-6">
              <div className="relative mx-auto w-20 h-20 mb-4">
                {selectedContactData.profilePicUrl ? (
                  <img 
                    src={selectedContactData.profilePicUrl} 
                    alt={selectedContactData.name}
                    className="w-20 h-20 rounded-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                ) : null}
                <div className={`w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 font-medium ${selectedContactData.profilePicUrl ? 'hidden' : ''}`}>
                  {selectedContactData.isGroup ? <Users className="w-10 h-10" /> : getInitials(selectedContactData.name)}
                </div>
              </div>
              <h3 className="text-xl font-medium text-gray-900 mb-1">{selectedContactData.name}</h3>
              <p className="text-sm text-gray-500">{selectedContactData.phone}</p>
            </div>

            {/* Action Buttons */}
            <div className="space-y-2">
              <button className="w-full p-3 text-left hover:bg-gray-50 rounded-lg flex items-center space-x-3">
                <Star className="w-5 h-5 text-gray-600" />
                <span>Mensagens com estrela</span>
              </button>
              <button className="w-full p-3 text-left hover:bg-gray-50 rounded-lg flex items-center space-x-3">
                <Archive className="w-5 h-5 text-gray-600" />
                <span>Arquivar conversa</span>
              </button>
              <button className="w-full p-3 text-left hover:bg-gray-50 rounded-lg flex items-center space-x-3">
                <VolumeX className="w-5 h-5 text-gray-600" />
                <span>Silenciar notificações</span>
              </button>
              <button className="w-full p-3 text-left hover:bg-gray-50 rounded-lg flex items-center space-x-3">
                <Trash2 className="w-5 h-5 text-red-600" />
                <span className="text-red-600">Excluir conversa</span>
              </button>
            </div>
          </div>

          {/* CRM Section */}
          <div className="p-4">
            <h4 className="font-medium text-gray-900 mb-3">Informações CRM</h4>
            <div className="space-y-3">
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">Status do cliente</p>
                <p className="font-medium">Ativo</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">Última compra</p>
                <p className="font-medium">Há 2 semanas</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">Valor total</p>
                <p className="font-medium text-green-600">R$ 2.450,00</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}