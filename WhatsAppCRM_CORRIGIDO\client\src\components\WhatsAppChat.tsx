import { useState, useRef, useEffect, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { 
  Send, Mic, Smile, Paperclip, Check, CheckCheck, Forward, 
  Download, Star, Reply, MoreVertical, Play, Pause, Image,
  FileText, Video, Music, MapPin, Phone, MessageCircle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { formatTime } from "@/lib/utils";

interface Message {
  id: string;
  body: string;
  timestamp: number;
  fromMe: boolean;
  from: string;
  to: string;
  ack?: number;
  type?: string;
  hasMedia?: boolean;
  mediaType?: string;
  mediaUrl?: string;
  mediaName?: string;
  mediaMimeType?: string;
  isStarred?: boolean;
  isForwarded?: boolean;
  quotedMsgId?: string;
  authorName?: string;
}

interface WhatsAppChatProps {
  contactId: string;
  contactName: string;
  contactPhone: string;
  contactAvatar?: string;
  isOnline?: boolean;
  lastSeen?: string;
}

const EMOJI_LIST = [
  '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
  '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
  '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
  '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
  '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬',
  '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉',
  '👆', '👇', '☝️', '👋', '🤚', '🖐️', '✋', '👏', '🙌', '🤝', '🙏'
];

export default function WhatsAppChat({ 
  contactId, 
  contactName, 
  contactPhone, 
  contactAvatar,
  isOnline = false,
  lastSeen = "hoje às 14:30"
}: WhatsAppChatProps) {
  const [messageInput, setMessageInput] = useState("");
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showAttachMenu, setShowAttachMenu] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<string | null>(null);
  const [replyingTo, setReplyingTo] = useState<Message | null>(null);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const currentUserId = 'user_1749665821961';

  // Buscar mensagens reais do WhatsApp with paginated structure
  const { data: messagesData, isLoading } = useQuery({
    queryKey: ['/api/whatsapp/messages', currentUserId, contactId],
    queryFn: async () => {
      if (!currentUserId || !contactId) return { data: [], total: 0, offset: 0 };
      const response = await fetch(`/api/whatsapp/messages?userId=${encodeURIComponent(currentUserId)}&chatId=${encodeURIComponent(contactId)}&limit=100`);
      if (!response.ok) throw new Error('Failed to fetch messages');
      const result = await response.json();
      
      // Handle both array and paginated responses
      if (Array.isArray(result)) {
        return { data: result, total: result.length, offset: 0 };
      }
      return result.data ? result : { data: result, total: Array.isArray(result) ? result.length : 0, offset: 0 };
    },
    enabled: !!currentUserId && !!contactId,
    refetchInterval: 3000,
  });

  // Extract messages array from paginated structure with proper fallback
  const messages = Array.isArray(messagesData?.data) ? messagesData.data : 
                   Array.isArray(messagesData) ? messagesData : [];

  // Mutation para enviar mensagem
  const sendMessageMutation = useMutation({
    mutationFn: async (messageData: { to: string; message: string; userId: string }) => {
      const response = await fetch('/api/whatsapp/send', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(messageData),
      });
      if (!response.ok) throw new Error('Failed to send message');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/whatsapp/messages', currentUserId, contactId] });
      setMessageInput("");
      setReplyingTo(null);
      toast({
        title: "Mensagem enviada",
        description: "Sua mensagem foi enviada com sucesso",
      });
    },
    onError: () => {
      toast({
        title: "Erro ao enviar",
        description: "Não foi possível enviar a mensagem",
        variant: "destructive",
      });
    },
  });

  // Auto scroll para a última mensagem
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleSendMessage = useCallback(() => {
    if (!messageInput.trim()) return;
    
    const messageData = {
      to: contactPhone,
      message: replyingTo 
        ? `@${replyingTo.from} ${replyingTo.body}\n\n${messageInput}`
        : messageInput,
      userId: currentUserId,
    };

    sendMessageMutation.mutate(messageData);
  }, [messageInput, replyingTo, contactPhone, currentUserId, sendMessageMutation]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);

  const addEmoji = useCallback((emoji: string) => {
    const cursorPosition = inputRef.current?.selectionStart || messageInput.length;
    const newMessage = messageInput.slice(0, cursorPosition) + emoji + messageInput.slice(cursorPosition);
    setMessageInput(newMessage);
    
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        inputRef.current.setSelectionRange(cursorPosition + emoji.length, cursorPosition + emoji.length);
      }
    }, 0);
  }, [messageInput]);

  const toggleEmojiPicker = useCallback(() => {
    setShowEmojiPicker(!showEmojiPicker);
  }, [showEmojiPicker]);

  const getMessageStatus = (message: Message) => {
    if (!message.fromMe) return null;
    
    switch (message.ack) {
      case 0: return <Check className="w-4 h-4 text-gray-400" />;
      case 1: return <Check className="w-4 h-4 text-gray-400" />;
      case 2: return <CheckCheck className="w-4 h-4 text-gray-400" />;
      case 3: return <CheckCheck className="w-4 h-4 text-blue-400" />;
      default: return <Check className="w-4 h-4 text-gray-400" />;
    }
  };

  const renderMessage = (message: Message) => {
    const isFromMe = message.fromMe;
    const time = formatTime(new Date(message.timestamp));

    return (
      <div
        key={message.id}
        className={`flex mb-3 ${isFromMe ? 'justify-end' : 'justify-start'}`}
      >
        <div
          className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg relative group ${
            isFromMe
              ? 'bg-green-500 text-white rounded-br-none'
              : 'bg-white text-gray-900 rounded-bl-none shadow-sm border'
          }`}
          onClick={() => setSelectedMessage(selectedMessage === message.id ? null : message.id)}
        >
          {message.isForwarded && (
            <div className="flex items-center text-xs text-gray-500 mb-1">
              <Forward className="w-3 h-3 mr-1" />
              Encaminhada
            </div>
          )}

          {message.quotedMsgId && (
            <div className="bg-black bg-opacity-10 rounded p-2 mb-2 text-xs">
              <div className="font-medium">{message.authorName}</div>
              <div className="opacity-75">Mensagem citada</div>
            </div>
          )}

          {/* Media Content */}
          {message.hasMedia && message.mediaUrl && (
            <div className="mb-2">
              {message.mediaType === 'image' && (
                <img 
                  src={message.mediaUrl} 
                  alt="Imagem" 
                  className="max-w-full rounded-lg cursor-pointer hover:opacity-90"
                  loading="lazy"
                />
              )}
              {message.mediaType === 'video' && (
                <video 
                  src={message.mediaUrl} 
                  controls 
                  className="max-w-full rounded-lg"
                  preload="metadata"
                />
              )}
              {message.mediaType === 'audio' && (
                <audio 
                  src={message.mediaUrl} 
                  controls 
                  className="w-full"
                />
              )}
              {message.mediaType === 'document' && (
                <div className="flex items-center p-2 bg-gray-100 rounded-lg">
                  <FileText className="w-6 h-6 text-blue-500 mr-2" />
                  <div className="flex-1">
                    <div className="text-sm font-medium">{message.mediaName || 'Documento'}</div>
                    <div className="text-xs text-gray-500">Clique para baixar</div>
                  </div>
                  <Download className="w-4 h-4 text-gray-500" />
                </div>
              )}
            </div>
          )}

          {/* Text Content */}
          {message.body && (
            <div className="break-words">
              {message.body}
            </div>
          )}

          {/* Media placeholder for types not recognized */}
          {message.hasMedia && !message.mediaUrl && (
            <div className="flex items-center p-2 bg-gray-100 rounded-lg mb-2">
              {message.type === 'image' && <Image className="w-6 h-6 text-pink-500 mr-2" />}
              {message.type === 'video' && <Video className="w-6 h-6 text-purple-500 mr-2" />}
              {message.type === 'audio' && <Music className="w-6 h-6 text-orange-500 mr-2" />}
              {message.type === 'document' && <FileText className="w-6 h-6 text-blue-500 mr-2" />}
              <span className="text-sm">{message.type === 'image' ? '📷 Imagem' : 
                                        message.type === 'video' ? '🎥 Vídeo' : 
                                        message.type === 'audio' ? '🎵 Áudio' : 
                                        '📄 Documento'}</span>
            </div>
          )}

          <div className={`flex items-center justify-end mt-1 space-x-1 text-xs ${
            isFromMe ? 'text-green-100' : 'text-gray-500'
          }`}>
            {message.isStarred && <Star className="w-3 h-3 fill-current" />}
            <span>{time}</span>
            {getMessageStatus(message)}
          </div>

          {selectedMessage === message.id && (
            <div className="absolute top-full left-0 mt-1 bg-white border rounded-lg shadow-lg py-1 z-10">
              <button className="px-3 py-1 text-sm hover:bg-gray-100 w-full text-left flex items-center">
                <Reply className="w-4 h-4 mr-2" />
                Responder
              </button>
              <button className="px-3 py-1 text-sm hover:bg-gray-100 w-full text-left flex items-center">
                <Forward className="w-4 h-4 mr-2" />
                Encaminhar
              </button>
              <button className="px-3 py-1 text-sm hover:bg-gray-100 w-full text-left flex items-center">
                <Star className="w-4 h-4 mr-2" />
                Favoritar
              </button>
            </div>
          )}
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Carregando mensagens...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-gray-50">
      {/* Header do Chat */}
      <div className="bg-gray-100 px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              {contactAvatar ? (
                <img 
                  src={contactAvatar} 
                  alt={contactName}
                  className="w-10 h-10 rounded-full object-cover"
                />
              ) : (
                <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 font-medium text-sm">
                    {contactName.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </span>
                </div>
              )}
              {isOnline && (
                <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              )}
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{contactName}</h3>
              <p className="text-xs text-gray-500">
                {isOnline ? 'online' : `visto por último ${lastSeen}`}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button className="p-2 hover:bg-gray-200 rounded-full transition-colors">
              <Phone className="w-5 h-5 text-gray-600" />
            </button>
            <button className="p-2 hover:bg-gray-200 rounded-full transition-colors">
              <MoreVertical className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </div>
      </div>

      {/* Mensagens */}
      <div className="flex-1 overflow-y-auto px-4 py-4 space-y-2">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500">
              <MessageCircle className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <p>Nenhuma mensagem ainda</p>
              <p className="text-sm">Envie uma mensagem para começar a conversa</p>
            </div>
          </div>
        ) : (
          messages.map(renderMessage)
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input de Mensagem */}
      <div className="bg-gray-100 px-4 py-3 border-t border-gray-200">
        {replyingTo && (
          <div className="bg-white rounded-lg p-3 mb-3 border-l-4 border-green-500">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <p className="text-sm font-medium text-green-600">Respondendo para {replyingTo.authorName}</p>
                <p className="text-sm text-gray-600 truncate">{replyingTo.body}</p>
              </div>
              <button 
                onClick={() => setReplyingTo(null)}
                className="ml-2 text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
          </div>
        )}

        <div className="flex items-end space-x-2">
          {/* Botão de anexos */}
          <div className="relative">
            <button
              onClick={() => setShowAttachMenu(!showAttachMenu)}
              className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <Paperclip className="w-5 h-5" />
            </button>
            
            {showAttachMenu && (
              <div className="absolute bottom-full left-0 mb-2 bg-white border rounded-lg shadow-lg py-2 w-48">
                <button className="w-full px-4 py-2 text-left hover:bg-gray-100 flex items-center">
                  <Image className="w-4 h-4 mr-3 text-pink-500" />
                  Fotos e vídeos
                </button>
                <button className="w-full px-4 py-2 text-left hover:bg-gray-100 flex items-center">
                  <FileText className="w-4 h-4 mr-3 text-blue-500" />
                  Documento
                </button>
                <button className="w-full px-4 py-2 text-left hover:bg-gray-100 flex items-center">
                  <MapPin className="w-4 h-4 mr-3 text-green-500" />
                  Localização
                </button>
              </div>
            )}
          </div>

          {/* Campo de texto */}
          <div className="flex-1 relative">
            <input
              ref={inputRef}
              type="text"
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Digite uma mensagem"
              className="w-full px-4 py-3 rounded-full border border-gray-300 focus:outline-none focus:border-green-500"
              disabled={sendMessageMutation.isPending}
            />
            
            {/* Botão de emoji */}
            <button
              onClick={toggleEmojiPicker}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-600 hover:text-gray-800"
            >
              <Smile className="w-5 h-5" />
            </button>

            {/* Picker de emoji */}
            {showEmojiPicker && (
              <div className="absolute bottom-full right-0 mb-2 bg-white border rounded-lg shadow-lg p-3 w-64">
                <div className="grid grid-cols-8 gap-2 max-h-48 overflow-y-auto">
                  {EMOJI_LIST.map((emoji, index) => (
                    <button
                      key={index}
                      onClick={() => addEmoji(emoji)}
                      className="text-lg hover:bg-gray-100 rounded p-1 transition-colors"
                    >
                      {emoji}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Botão de enviar/gravar */}
          {messageInput.trim() ? (
            <button
              onClick={handleSendMessage}
              disabled={sendMessageMutation.isPending}
              className="p-3 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors disabled:opacity-50"
            >
              {sendMessageMutation.isPending ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              ) : (
                <Send className="w-5 h-5" />
              )}
            </button>
          ) : (
            <button
              onClick={() => setIsRecording(!isRecording)}
              className={`p-3 rounded-full transition-colors ${
                isRecording ? 'bg-red-500 text-white' : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
              }`}
            >
              <Mic className="w-5 h-5" />
            </button>
          )}
        </div>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        multiple
        accept="image/*,video/*,audio/*,.pdf,.doc,.docx"
      />
    </div>
  );
}