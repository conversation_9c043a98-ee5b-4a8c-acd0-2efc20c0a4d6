#!/bin/bash

# Casa das Camisetas - Configuração para Acesso LAN Multi-usuário
# Script complementar para otimizar acesso via rede local
# Para uso após instalação principal

set -e

echo "🌐 Casa das Camisetas - Configuração LAN Multi-usuário"
echo "======================================================"

# Cores para output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

PROJECT_DIR="/opt/whatsappcrm"

# Verificar se o sistema principal está instalado
if [ ! -d "$PROJECT_DIR" ]; then
    echo "❌ Sistema principal não encontrado. Execute install.sh primeiro."
    exit 1
fi

cd "$PROJECT_DIR"

# 1. Configurar CORS para acesso de qualquer IP da rede local
log "🌐 Configurando CORS para rede local..."
cat >> .env << 'EOF'

# LAN Access Configuration
CORS_ORIGIN=http://192.168.*.*,http://10.*.*.*,http://172.16.*.*
ALLOWED_IPS=***********/16,10.0.0.0/8,**********/12,127.0.0.1
TRUST_PROXY=true
EOF

# 2. Atualizar configuração do Nginx para múltiplos usuários
log "⚙️ Otimizando Nginx para múltiplos usuários..."
sudo tee /etc/nginx/sites-available/whatsappcrm << 'EOF'
# Casa das Camisetas CRM - Configuração LAN Multi-usuário
upstream backend {
    least_conn;
    server 127.0.0.1:5000;
    keepalive 32;
}

server {
    listen 80;
    server_name _;
    client_max_body_size 100M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # Rate limiting para prevenir sobrecarga
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=uploads:10m rate=5r/s;
    
    # Allow access from LAN networks
    allow ***********/16;
    allow 10.0.0.0/8;
    allow **********/12;
    allow 127.0.0.1;
    deny all;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Main application
    location / {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        proxy_send_timeout 300s;
        
        # Buffer settings for better performance
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }
    
    # API routes with rate limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 120s;
        proxy_connect_timeout 30s;
    }
    
    # WebSocket para tempo real (crítico para multi-usuário)
    location /ws {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
        proxy_connect_timeout 60s;
    }
    
    # Upload de arquivos
    location /uploads {
        limit_req zone=uploads burst=10 nodelay;
        alias /opt/whatsappcrm/uploads;
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # Security for uploads
        location ~* \.(php|php5|phtml|pl|py|jsp|asp|sh|cgi)$ {
            deny all;
        }
    }
    
    # Static assets com cache otimizado
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
        
        # Fallback para aplicação se não encontrar arquivo
        try_files $uri @fallback;
    }
    
    # Fallback para SPA routing
    location @fallback {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://backend;
        access_log off;
    }
    
    # Admin area (protegido)
    location /admin {
        # Limitar apenas para IPs administrativos se necessário
        # allow ************;  # IP do administrador
        # deny all;
        
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Configuração adicional para WebSocket
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}
EOF

# 3. Otimizar PM2 para múltiplos usuários simultâneos
log "🚀 Otimizando PM2 para carga multi-usuário..."
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'casa-camisetas-crm',
    script: './dist/index.js',
    instances: 'max',  // Usar todos os cores disponíveis
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 5000,
      HOST: '0.0.0.0'
    },
    max_memory_restart: '2G',
    node_args: '--max-old-space-size=4096',
    error_file: '/opt/whatsappcrm/logs/err.log',
    out_file: '/opt/whatsappcrm/logs/out.log',
    log_file: '/opt/whatsappcrm/logs/combined.log',
    time: true,
    restart_delay: 2000,
    max_restarts: 15,
    min_uptime: '10s',
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'sessions', 'uploads'],
    
    // Configurações para alta performance
    kill_timeout: 5000,
    listen_timeout: 3000,
    shutdown_with_message: true,
    
    // Variáveis específicas para LAN
    env_production: {
      NODE_ENV: 'production',
      MAX_CONNECTIONS: 300,
      KEEP_ALIVE_TIMEOUT: 60000,
      HEADERS_TIMEOUT: 65000
    }
  }]
};
EOF

# 4. Configurar PostgreSQL para múltiplas conexões
log "🐘 Otimizando PostgreSQL para múltiplos usuários..."
sudo -u postgres psql << 'EOF'
-- Configurações para múltiplos usuários
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '512MB';
ALTER SYSTEM SET effective_cache_size = '2GB';
ALTER SYSTEM SET work_mem = '4MB';
ALTER SYSTEM SET maintenance_work_mem = '128MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;
SELECT pg_reload_conf();
EOF

# 5. Configurar monitoramento específico para LAN
log "📊 Configurando monitoramento para acesso LAN..."
cat > monitor-lan.sh << 'EOF'
#!/bin/bash
# Monitor específico para acesso LAN multi-usuário

LOG_FILE="/opt/whatsappcrm/logs/lan-monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# Verificar conexões ativas
ACTIVE_CONNECTIONS=$(netstat -an | grep :80 | grep ESTABLISHED | wc -l)
WEBSOCKET_CONNECTIONS=$(netstat -an | grep :5000 | grep ESTABLISHED | wc -l)

# Log de conexões
echo "$DATE - Conexões HTTP: $ACTIVE_CONNECTIONS, WebSocket: $WEBSOCKET_CONNECTIONS" >> $LOG_FILE

# Verificar se há muitas conexões (alerta para possível sobrecarga)
if [ $ACTIVE_CONNECTIONS -gt 50 ]; then
    echo "$DATE - ALERTA: Muitas conexões HTTP ativas: $ACTIVE_CONNECTIONS" >> $LOG_FILE
    # Restart nginx se necessário
    if [ $ACTIVE_CONNECTIONS -gt 100 ]; then
        echo "$DATE - CRÍTICO: Reiniciando Nginx devido a sobrecarga" >> $LOG_FILE
        sudo systemctl reload nginx
    fi
fi

# Verificar sessões WhatsApp ativas
WHATSAPP_SESSIONS=$(find /opt/whatsappcrm/sessions -name "*.json" | wc -l)
echo "$DATE - Sessões WhatsApp ativas: $WHATSAPP_SESSIONS" >> $LOG_FILE

# Verificar performance da aplicação
if ! curl -s http://localhost:5000/health >/dev/null; then
    echo "$DATE - ALERTA: Health check falhou, reiniciando aplicação" >> $LOG_FILE
    pm2 restart casa-camisetas-crm
fi

# Limpar logs antigos (manter apenas 3 dias)
find /opt/whatsappcrm/logs -name "*.log" -mtime +3 -delete
EOF

chmod +x monitor-lan.sh

# 6. Configurar script de teste de conectividade LAN
log "🔍 Criando script de teste de conectividade..."
cat > test-lan-access.sh << 'EOF'
#!/bin/bash
# Teste de conectividade LAN para Casa das Camisetas CRM

echo "🏠 Casa das Camisetas - Teste de Conectividade LAN"
echo "================================================"

# Obter IP do servidor
SERVER_IP=$(hostname -I | awk '{print $1}' | head -n 1)
echo "🖥️ IP do Servidor: $SERVER_IP"

# Testar se os serviços estão rodando
echo ""
echo "📋 Status dos Serviços:"

# Nginx
if systemctl is-active --quiet nginx; then
    echo "✅ Nginx: Rodando"
else
    echo "❌ Nginx: Parado"
fi

# PostgreSQL
if systemctl is-active --quiet postgresql; then
    echo "✅ PostgreSQL: Rodando"
else
    echo "❌ PostgreSQL: Parado"
fi

# PM2
if pm2 list | grep -q "casa-camisetas-crm.*online"; then
    echo "✅ Aplicação: Online"
else
    echo "❌ Aplicação: Offline"
fi

# Testar conectividade HTTP
echo ""
echo "🌐 Teste de Conectividade:"

if curl -s -o /dev/null -w "%{http_code}" http://localhost:80 | grep -q "200\|302"; then
    echo "✅ HTTP (porta 80): OK"
else
    echo "❌ HTTP (porta 80): Falha"
fi

if curl -s -o /dev/null -w "%{http_code}" http://localhost:5000 | grep -q "200\|302"; then
    echo "✅ Aplicação (porta 5000): OK"
else
    echo "❌ Aplicação (porta 5000): Falha"
fi

# Mostrar conexões ativas
echo ""
echo "👥 Conexões Ativas:"
ACTIVE_HTTP=$(netstat -an | grep :80 | grep ESTABLISHED | wc -l)
ACTIVE_APP=$(netstat -an | grep :5000 | grep ESTABLISHED | wc -l)
echo "   HTTP: $ACTIVE_HTTP conexões"
echo "   App: $ACTIVE_APP conexões"

# Mostrar uso de recursos
echo ""
echo "💾 Recursos do Sistema:"
echo "   RAM: $(free -h | grep Mem | awk '{printf "%.1f%% usado\n", $3/$2*100}')"
echo "   Disco: $(df -h / | tail -1 | awk '{print $5}') usado"
echo "   CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)% usado"

echo ""
echo "🎯 URLs de Acesso:"
echo "   Principal: http://$SERVER_IP"
echo "   API: http://$SERVER_IP/api"
echo "   Health: http://$SERVER_IP/health"

echo ""
echo "📱 Sessões WhatsApp:"
SESSIONS=$(find /opt/whatsappcrm/sessions -name "*.json" 2>/dev/null | wc -l)
echo "   Ativas: $SESSIONS"

echo ""
echo "Para testar de outro computador da rede:"
echo "Abra o navegador e acesse: http://$SERVER_IP"
EOF

chmod +x test-lan-access.sh

# 7. Criar script de otimização de rede
log "⚡ Configurando otimizações de rede..."
cat > optimize-network.sh << 'EOF'
#!/bin/bash
# Otimizações de rede para alta performance LAN

echo "⚡ Aplicando otimizações de rede..."

# Otimizações do kernel para networking
sudo tee -a /etc/sysctl.conf << 'SYSCTL'

# Casa das Camisetas CRM - Network Optimizations
net.core.somaxconn = 1024
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 8192
net.ipv4.tcp_slow_start_after_idle = 0
net.ipv4.tcp_tw_reuse = 1
net.ipv4.ip_local_port_range = 1024 65535
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.ipv4.tcp_congestion_control = bbr
SYSCTL

# Aplicar configurações
sudo sysctl -p

echo "✅ Otimizações aplicadas"
EOF

chmod +x optimize-network.sh

# 8. Reiniciar serviços com novas configurações
log "🔄 Aplicando configurações..."
sudo nginx -t && sudo systemctl reload nginx
pm2 reload casa-camisetas-crm

# 9. Adicionar monitoramento ao cron
(crontab -l 2>/dev/null | grep -v monitor-lan; echo "*/2 * * * * cd $PROJECT_DIR && ./monitor-lan.sh") | crontab -

# 10. Executar teste inicial
sleep 5
./test-lan-access.sh

# Obter IP do servidor
SERVER_IP=$(hostname -I | awk '{print $1}' | head -n 1)

echo ""
echo "🎉 CONFIGURAÇÃO LAN MULTI-USUÁRIO CONCLUÍDA!"
echo "============================================="
echo ""
echo "🌐 ACESSO PARA USUÁRIOS DA REDE:"
echo "   URL Principal: http://$SERVER_IP"
echo ""
echo "👥 CAPACIDADE:"
echo "   • Até 30 vendedores simultâneos"
echo "   • WhatsApp Multi-Device por usuário"
echo "   • Rate limiting configurado"
echo "   • Monitoramento automático"
echo ""
echo "🔧 SCRIPTS DISPONÍVEIS:"
echo "   ./test-lan-access.sh      # Testar conectividade"
echo "   ./monitor-lan.sh          # Monitor manual"
echo "   ./optimize-network.sh     # Otimizações extras"
echo ""
echo "📊 MONITORAMENTO:"
echo "   tail -f logs/lan-monitor.log"
echo "   pm2 monit"
echo ""
success "Sistema otimizado para acesso LAN multi-usuário!"