import { useState, useRef } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  Send, 
  Image, 
  Video, 
  FileText, 
  Mic, 
  WifiOff,
  Wifi,
  CheckCircle, 
  XCircle, 
  Clock,
  Upload,
  Play,
  Pause,
  Square
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Progress } from '@/components/ui/progress';

interface MessageTest {
  id: string;
  type: 'text' | 'image' | 'video' | 'document' | 'audio';
  content: string;
  file?: File;
  status: 'pending' | 'sending' | 'sent' | 'failed' | 'retrying';
  timestamp: Date;
  retryCount: number;
  error?: string;
  progress?: number;
}

export function MessageTestPanel() {
  const [messageTests, setMessageTests] = useState<MessageTest[]>([]);
  const [textMessage, setTextMessage] = useState('Teste de mensagem de texto');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isNetworkSimulated, setIsNetworkSimulated] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const addMessageTest = (test: Omit<MessageTest, 'id' | 'timestamp'>) => {
    const newTest: MessageTest = {
      ...test,
      id: Date.now().toString(),
      timestamp: new Date()
    };
    setMessageTests(prev => [newTest, ...prev]);
    return newTest.id;
  };

  const updateMessageTest = (id: string, updates: Partial<MessageTest>) => {
    setMessageTests(prev => prev.map(test => 
      test.id === id ? { ...test, ...updates } : test
    ));
  };

  const simulateNetworkFailure = async <T,>(operation: () => Promise<T>): Promise<T> => {
    if (isNetworkSimulated && Math.random() < 0.7) {
      throw new Error('Simulated network failure');
    }
    return operation();
  };

  const sendMessageWithRetry = async (testId: string, sendFunction: () => Promise<void>) => {
    const test = messageTests.find(t => t.id === testId);
    if (!test) return;

    let attempts = 0;
    const maxRetries = 3;

    while (attempts <= maxRetries) {
      try {
        updateMessageTest(testId, { 
          status: attempts === 0 ? 'sending' : 'retrying',
          retryCount: attempts 
        });

        await simulateNetworkFailure(sendFunction);
        
        updateMessageTest(testId, { 
          status: 'sent',
          error: undefined 
        });
        
        toast({
          title: "Sucesso",
          description: `Mensagem ${test.type} enviada com sucesso`,
          variant: "default"
        });
        return;

      } catch (error) {
        attempts++;
        console.error(`[Message Test] Tentativa ${attempts} falhou:`, error);
        
        if (attempts > maxRetries) {
          updateMessageTest(testId, { 
            status: 'failed',
            error: (error as Error).message,
            retryCount: attempts - 1
          });
          
          toast({
            title: "Falha no Envio",
            description: `Mensagem ${test.type} falhou após ${maxRetries} tentativas`,
            variant: "destructive"
          });
          return;
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * attempts));
      }
    }
  };

  const sendTextMessage = async () => {
    if (!textMessage.trim()) {
      toast({
        title: "Erro",
        description: "Mensagem de texto não pode estar vazia",
        variant: "destructive"
      });
      return;
    }

    const testId = addMessageTest({
      type: 'text',
      content: textMessage,
      status: 'pending',
      retryCount: 0
    });

    await sendMessageWithRetry(testId, async () => {
      const response = await fetch('/api/whatsapp/send-message', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          phoneNumber: '+5511999999999',
          message: textMessage,
          type: 'text'
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    });
  };

  const sendFileMessage = async (file: File, type: 'image' | 'video' | 'document') => {
    const testId = addMessageTest({
      type,
      content: file.name,
      file,
      status: 'pending',
      retryCount: 0,
      progress: 0
    });

    await sendMessageWithRetry(testId, async () => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('phoneNumber', '+5511999999999');
      formData.append('type', type);

      const response = await fetch('/api/whatsapp/send-media', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    });
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setSelectedFile(file);
    
    // Determine file type
    let messageType: 'image' | 'video' | 'document' = 'document';
    
    if (file.type.startsWith('image/')) {
      messageType = 'image';
    } else if (file.type.startsWith('video/')) {
      messageType = 'video';
    }

    toast({
      title: "Arquivo Selecionado",
      description: `${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`,
      variant: "default"
    });
  };

  const sendSelectedFile = () => {
    if (!selectedFile) {
      toast({
        title: "Erro",
        description: "Nenhum arquivo selecionado",
        variant: "destructive"
      });
      return;
    }

    let messageType: 'image' | 'video' | 'document' = 'document';
    
    if (selectedFile.type.startsWith('image/')) {
      messageType = 'image';
    } else if (selectedFile.type.startsWith('video/')) {
      messageType = 'video';
    }

    sendFileMessage(selectedFile, messageType);
    setSelectedFile(null);
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      const chunks: Blob[] = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/wav' });
        setAudioBlob(blob);
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();
      setIsRecording(true);
      setRecordingTime(0);

      recordingIntervalRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);

      toast({
        title: "Gravação Iniciada",
        description: "Gravando áudio...",
        variant: "default"
      });

    } catch (error) {
      console.error('[Audio Recording] Erro:', error);
      toast({
        title: "Erro",
        description: "Não foi possível acessar o microfone",
        variant: "destructive"
      });
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current);
      }

      toast({
        title: "Gravação Finalizada",
        description: `Áudio gravado (${recordingTime}s)`,
        variant: "default"
      });
    }
  };

  const sendAudioMessage = async () => {
    if (!audioBlob) {
      toast({
        title: "Erro",
        description: "Nenhum áudio gravado",
        variant: "destructive"
      });
      return;
    }

    const audioFile = new File([audioBlob], 'audio-recording.wav', { type: 'audio/wav' });
    
    const testId = addMessageTest({
      type: 'audio',
      content: `Áudio gravado (${recordingTime}s)`,
      file: audioFile,
      status: 'pending',
      retryCount: 0
    });

    await sendMessageWithRetry(testId, async () => {
      const formData = new FormData();
      formData.append('file', audioFile);
      formData.append('phoneNumber', '+5511999999999');
      formData.append('type', 'audio');

      const response = await fetch('/api/whatsapp/send-media', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    });

    setAudioBlob(null);
    setRecordingTime(0);
  };

  const runComprehensiveTest = async () => {
    toast({
      title: "Teste Abrangente",
      description: "Iniciando teste completo de envio de mensagens",
      variant: "default"
    });

    // Test 1: Text message
    setTextMessage('Teste automático de mensagem de texto');
    await new Promise(resolve => setTimeout(resolve, 500));
    await sendTextMessage();

    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test 2: Image (create a small test image)
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = '#00a884';
      ctx.fillRect(0, 0, 100, 100);
      ctx.fillStyle = 'white';
      ctx.font = '16px Arial';
      ctx.fillText('TEST', 25, 55);
    }
    
    canvas.toBlob(async (blob) => {
      if (blob) {
        const testImage = new File([blob], 'test-image.png', { type: 'image/png' });
        await sendFileMessage(testImage, 'image');
      }
    });

    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test 3: Document (create a test text file)
    const testDoc = new File(['Documento de teste para validação'], 'test-document.txt', { type: 'text/plain' });
    await sendFileMessage(testDoc, 'document');

    toast({
      title: "Teste Completo",
      description: "Todos os tipos de mensagem foram testados",
      variant: "default"
    });
  };

  const clearTests = () => {
    setMessageTests([]);
  };

  const getStatusIcon = (status: MessageTest['status']) => {
    switch (status) {
      case 'sent':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'sending':
      case 'retrying':
        return <Clock className="h-4 w-4 text-yellow-500 animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: MessageTest['status']) => {
    switch (status) {
      case 'sent':
        return 'bg-green-500';
      case 'failed':
        return 'bg-red-500';
      case 'sending':
      case 'retrying':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Validação de Envio de Mensagens</h2>
        <div className="flex items-center gap-2">
          <Button
            onClick={() => setIsNetworkSimulated(!isNetworkSimulated)}
            variant={isNetworkSimulated ? "destructive" : "outline"}
            size="sm"
          >
            {isNetworkSimulated ? <WifiOff className="h-4 w-4 mr-2" /> : <Wifi className="h-4 w-4 mr-2" />}
            {isNetworkSimulated ? "Falha Simulada" : "Rede Normal"}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Text Message Test */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Send className="h-5 w-5" />
              Mensagem de Texto
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Textarea
              placeholder="Digite sua mensagem de teste"
              value={textMessage}
              onChange={(e) => setTextMessage(e.target.value)}
              rows={3}
            />
            <Button onClick={sendTextMessage} className="w-full">
              <Send className="h-4 w-4 mr-2" />
              Enviar Texto
            </Button>
          </CardContent>
        </Card>

        {/* File Upload Test */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Envio de Arquivos
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="file-upload">Selecionar arquivo</Label>
              <Input
                id="file-upload"
                type="file"
                ref={fileInputRef}
                onChange={handleFileSelect}
                accept="image/*,video/*,.pdf,.doc,.docx,.txt"
              />
            </div>
            {selectedFile && (
              <div className="p-3 border rounded-lg bg-muted">
                <div className="flex items-center gap-2">
                  {selectedFile.type.startsWith('image/') && <Image className="h-4 w-4" />}
                  {selectedFile.type.startsWith('video/') && <Video className="h-4 w-4" />}
                  {!selectedFile.type.startsWith('image/') && !selectedFile.type.startsWith('video/') && <FileText className="h-4 w-4" />}
                  <span className="text-sm font-medium">{selectedFile.name}</span>
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </div>
              </div>
            )}
            <Button onClick={sendSelectedFile} disabled={!selectedFile} className="w-full">
              <Upload className="h-4 w-4 mr-2" />
              Enviar Arquivo
            </Button>
          </CardContent>
        </Card>

        {/* Audio Recording Test */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Mic className="h-5 w-5" />
              Gravação de Áudio
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {isRecording ? (
              <div className="text-center">
                <div className="text-2xl font-mono text-red-500 mb-2">
                  {formatTime(recordingTime)}
                </div>
                <div className="w-full bg-red-100 h-2 rounded-full mb-4">
                  <div className="bg-red-500 h-2 rounded-full animate-pulse" style={{ width: '100%' }}></div>
                </div>
                <Button onClick={stopRecording} variant="destructive">
                  <Square className="h-4 w-4 mr-2" />
                  Parar Gravação
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {audioBlob && (
                  <div className="p-3 border rounded-lg bg-muted">
                    <div className="flex items-center gap-2">
                      <Mic className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        Áudio gravado ({formatTime(recordingTime)})
                      </span>
                    </div>
                  </div>
                )}
                <div className="flex gap-2">
                  <Button onClick={startRecording} variant="outline" className="flex-1">
                    <Mic className="h-4 w-4 mr-2" />
                    Gravar
                  </Button>
                  <Button 
                    onClick={sendAudioMessage} 
                    disabled={!audioBlob}
                    className="flex-1"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    Enviar Áudio
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Controles de Teste</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={runComprehensiveTest} className="w-full">
              <Send className="h-4 w-4 mr-2" />
              Teste Abrangente
            </Button>
            <Button onClick={clearTests} variant="outline" className="w-full">
              Limpar Resultados
            </Button>
            {isNetworkSimulated && (
              <div className="p-3 border rounded-lg bg-yellow-50 border-yellow-200">
                <div className="text-sm text-yellow-800">
                  ⚠️ Modo de falha de rede ativo - 70% das mensagens falharão inicialmente
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Test Results */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-lg">Resultados dos Testes</CardTitle>
          <Badge variant="outline">
            {messageTests.length} testes
          </Badge>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {messageTests.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                Nenhum teste de mensagem executado
              </div>
            ) : (
              messageTests.map((test) => (
                <div key={test.id} className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="flex-shrink-0">
                    {getStatusIcon(test.status)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="outline" className="text-xs">
                        {test.type.toUpperCase()}
                      </Badge>
                      <Badge className={`text-xs ${getStatusColor(test.status)}`}>
                        {test.status}
                      </Badge>
                      {test.retryCount > 0 && (
                        <Badge variant="secondary" className="text-xs">
                          {test.retryCount} tentativas
                        </Badge>
                      )}
                    </div>
                    
                    <div className="text-sm font-medium truncate">
                      {test.content}
                    </div>
                    
                    <div className="text-xs text-muted-foreground">
                      {test.timestamp.toLocaleTimeString()}
                      {test.error && ` • ${test.error}`}
                    </div>
                    
                    {test.progress !== undefined && test.progress < 100 && (
                      <Progress value={test.progress} className="mt-2" />
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}