import { useState, useRef, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send } from 'lucide-react';
import { formatRelativeTime } from '@/lib/utils';
import { useWhatsApp } from '@/hooks/useWhatsApp';
import { useWebSocket } from '@/hooks/useWebSocket';
import { apiRequest } from '@/lib/queryClient';
import { Message } from '@shared/schema';

export function ChatPreview() {
  const [selectedChatId, setSelectedChatId] = useState<number | null>(null);
  const [messageInput, setMessageInput] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();
  const { sendMessage: sendWhatsAppMessage } = useWhatsApp(1); // Using user ID 1 for now

  const { data: chats = [] } = useQuery({
    queryKey: ['/api/chats'],
    refetchInterval: 10000,
  });

  const { data: messages = [] } = useQuery({
    queryKey: ['/api/chats', selectedChatId, 'messages'],
    enabled: !!selectedChatId,
    refetchInterval: 5000,
  });

  // WebSocket for real-time messages
  useWebSocket(`ws://${window.location.host}/ws?userId=1`, {
    onMessage: (message) => {
      if (message.type === 'new_message' || message.type === 'message_sent') {
        // Invalidate queries to refresh messages
        queryClient.invalidateQueries({ queryKey: ['/api/chats'] });
        if (selectedChatId) {
          queryClient.invalidateQueries({ queryKey: ['/api/chats', selectedChatId, 'messages'] });
        }
      }
    },
  });

  const sendMessageMutation = useMutation({
    mutationFn: async ({ content }: { content: string }) => {
      // This would need the actual WhatsApp chat ID, not the database chat ID
      // For now, we'll use a placeholder
      const result = await sendWhatsAppMessage('placeholder_chat_id', content);
      return result;
    },
    onSuccess: () => {
      setMessageInput('');
      queryClient.invalidateQueries({ queryKey: ['/api/chats', selectedChatId, 'messages'] });
    },
  });

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!messageInput.trim() || !selectedChatId) return;
    
    sendMessageMutation.mutate({ content: messageInput.trim() });
  };

  useEffect(() => {
    // Auto-select first chat if none selected
    if (chats.length > 0 && !selectedChatId) {
      setSelectedChatId(chats[0].id);
    }
  }, [chats, selectedChatId]);

  useEffect(() => {
    // Scroll to bottom when new messages arrive
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Chat Rápido</CardTitle>
          <div className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
            <span className="text-sm text-gray-600 dark:text-gray-400">Online</span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col p-0">
        {/* Chat Selection */}
        {chats.length > 1 && (
          <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
            <select 
              value={selectedChatId || ''}
              onChange={(e) => setSelectedChatId(Number(e.target.value))}
              className="w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm"
            >
              {chats.map((chat: any) => (
                <option key={chat.id} value={chat.id}>
                  Chat {chat.id}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Messages */}
        <div className="flex-1 p-4 overflow-y-auto space-y-3 max-h-80">
          {messages.length === 0 ? (
            <div className="text-center text-gray-500 dark:text-gray-400 py-8">
              <p className="text-sm">Nenhuma mensagem ainda</p>
            </div>
          ) : (
            messages.map((message: Message) => (
              <div key={message.id} className={`flex ${message.fromMe ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-xs px-3 py-2 rounded-lg ${
                  message.fromMe 
                    ? 'message-from-me' 
                    : 'message-from-other'
                }`}>
                  <p className="text-sm">{message.content}</p>
                  <p className={`text-xs mt-1 ${
                    message.fromMe 
                      ? 'text-green-100' 
                      : 'text-gray-500 dark:text-gray-400'
                  }`}>
                    {formatRelativeTime(message.timestamp)}
                  </p>
                </div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Message Input */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <form onSubmit={handleSendMessage} className="flex items-center space-x-3">
            <Input
              type="text"
              placeholder="Digite sua mensagem..."
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              disabled={!selectedChatId || sendMessageMutation.isPending}
              className="flex-1"
            />
            <Button 
              type="submit"
              disabled={!messageInput.trim() || !selectedChatId || sendMessageMutation.isPending}
              className="whatsapp-green text-white"
            >
              <Send className="w-5 h-5" />
            </Button>
          </form>
        </div>
      </CardContent>
    </Card>
  );
}
