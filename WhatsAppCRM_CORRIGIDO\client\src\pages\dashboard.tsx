import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { 
  BarChart3, MessageSquare, Users, Clock, TrendingUp, 
  Activity, Star, Calendar, Send, Eye, ArrowLeft 
} from "lucide-react";
import { useLocation } from "wouter";

export default function Dashboard() {
  const [, setLocation] = useLocation();
  const [timeRange, setTimeRange] = useState("7d");

  // Dashboard analytics data
  const { data: analytics } = useQuery({
    queryKey: ['/api/analytics/dashboard', timeRange],
    queryFn: async () => {
      const response = await fetch(`/api/analytics/dashboard?range=${timeRange}`);
      return response.json();
    },
  });

  // Message statistics
  const { data: messageStats } = useQuery({
    queryKey: ['/api/analytics/messages', timeRange],
    queryFn: async () => {
      const response = await fetch(`/api/analytics/messages?range=${timeRange}`);
      return response.json();
    },
  });

  const stats = analytics || {};
  const messageData = messageStats || {};

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setLocation('/whatsapp')}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Dashboard Empresarial</h1>
              <p className="text-sm text-gray-600">Análise abrangente de comunicação WhatsApp</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
            >
              <option value="1d">Últimas 24h</option>
              <option value="7d">Últimos 7 dias</option>
              <option value="30d">Últimos 30 dias</option>
              <option value="90d">Últimos 90 dias</option>
            </select>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Key Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Mensagens</p>
                <p className="text-3xl font-bold text-gray-900">{stats.totalMessages?.toLocaleString() || 0}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <MessageSquare className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Contatos Ativos</p>
                <p className="text-3xl font-bold text-gray-900">{stats.totalContacts?.toLocaleString() || 0}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tempo de Resposta</p>
                <p className="text-3xl font-bold text-gray-900">{stats.responseTime || 0}min</p>
              </div>
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Taxa de Engajamento</p>
                <p className="text-3xl font-bold text-gray-900">{stats.engagement || 0}%</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Message Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Análise de Mensagens</h3>
              <BarChart3 className="w-5 h-5 text-gray-500" />
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">Mensagens Enviadas</span>
                </div>
                <span className="font-semibold">{messageData.sent?.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">Mensagens Recebidas</span>
                </div>
                <span className="font-semibold">{messageData.received?.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">Mensagens Favoritas</span>
                </div>
                <span className="font-semibold">{messageData.starred?.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">Arquivos de Mídia</span>
                </div>
                <span className="font-semibold">{messageData.media?.toLocaleString()}</span>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Ferramentas Empresariais</h3>
              <Activity className="w-5 h-5 text-gray-500" />
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Calendar className="w-4 h-4 text-blue-600" />
                  <span className="text-sm text-gray-600">Mensagens Agendadas</span>
                </div>
                <span className="font-semibold">{stats.scheduledMessages}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Send className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-gray-600">Templates Utilizados</span>
                </div>
                <span className="font-semibold">{stats.templatesUsed}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Star className="w-4 h-4 text-yellow-600" />
                  <span className="text-sm text-gray-600">Mensagens Importantes</span>
                </div>
                <span className="font-semibold">{messageData.starred}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Eye className="w-4 h-4 text-purple-600" />
                  <span className="text-sm text-gray-600">Logs de Auditoria</span>
                </div>
                <button
                  onClick={() => setLocation('/audit')}
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  Ver Todos
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Ações Rápidas</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <button
              onClick={() => setLocation('/whatsapp')}
              className="p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <MessageSquare className="w-6 h-6 text-blue-600 mb-2" />
              <div className="font-medium text-gray-900">WhatsApp CRM</div>
              <div className="text-sm text-gray-600">Gerenciar conversas</div>
            </button>
            
            <button
              onClick={() => setLocation('/audit')}
              className="p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Activity className="w-6 h-6 text-green-600 mb-2" />
              <div className="font-medium text-gray-900">Auditoria</div>
              <div className="text-sm text-gray-600">Logs empresariais</div>
            </button>
            
            <button className="p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Calendar className="w-6 h-6 text-purple-600 mb-2" />
              <div className="font-medium text-gray-900">Agendamentos</div>
              <div className="text-sm text-gray-600">Mensagens programadas</div>
            </button>
            
            <button className="p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <BarChart3 className="w-6 h-6 text-yellow-600 mb-2" />
              <div className="font-medium text-gray-900">Relatórios</div>
              <div className="text-sm text-gray-600">Análises detalhadas</div>
            </button>
          </div>
        </div>

        {/* System Status */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Status do Sistema</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-600">WhatsApp Web</span>
              <span className="text-sm font-medium text-green-600">Conectado</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-600">Banco de Dados</span>
              <span className="text-sm font-medium text-green-600">Operacional</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-600">WebSocket</span>
              <span className="text-sm font-medium text-green-600">Ativo</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}