import { useState, useEffect, useRef, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Wifi, 
  WifiOff, 
  RotateCcw, 
  AlertTriangle,
  CheckCircle2,
  Clock,
  MessageSquare,
  Activity
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useWebSocketManager } from '@/hooks/useWebSocketManager';

interface TestLog {
  id: string;
  timestamp: Date;
  type: 'info' | 'warning' | 'error' | 'success';
  message: string;
  details?: any;
}

interface ConnectionMetrics {
  totalConnections: number;
  totalReconnections: number;
  totalFailures: number;
  averageReconnectTime: number;
  messagesSent: number;
  messagesReceived: number;
  messagesLost: number;
}

export function WebSocketReconnectionTest() {
  const [testLogs, setTestLogs] = useState<TestLog[]>([]);
  const [isTestRunning, setIsTestRunning] = useState(false);
  const [testPhase, setTestPhase] = useState<string>('idle');
  const [metrics, setMetrics] = useState<ConnectionMetrics>({
    totalConnections: 0,
    totalReconnections: 0,
    totalFailures: 0,
    averageReconnectTime: 0,
    messagesSent: 0,
    messagesReceived: 0,
    messagesLost: 0
  });
  
  const { toast } = useToast();
  const testWebSocket = useRef<WebSocket | null>(null);
  const messageQueue = useRef<any[]>([]);
  const reconnectStartTime = useRef<number>(0);
  const testStartTime = useRef<number>(0);
  const heartbeatInterval = useRef<NodeJS.Timeout | null>(null);
  const messageInterval = useRef<NodeJS.Timeout | null>(null);

  const addLog = useCallback((type: TestLog['type'], message: string, details?: any) => {
    const log: TestLog = {
      id: Date.now().toString(),
      timestamp: new Date(),
      type,
      message,
      details
    };
    
    setTestLogs(prev => [log, ...prev.slice(0, 99)]); // Keep last 100 logs
    
    console.log(`[WebSocket Test] ${type.toUpperCase()}: ${message}`, details || '');
  }, []);

  const updateMetrics = useCallback((updates: Partial<ConnectionMetrics>) => {
    setMetrics(prev => ({ ...prev, ...updates }));
  }, []);

  // Simulate message sending during test
  const sendTestMessage = useCallback(() => {
    if (testWebSocket.current?.readyState === WebSocket.OPEN) {
      const message = {
        type: 'test_message',
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        data: `Test message ${metrics.messagesSent + 1}`
      };
      
      testWebSocket.current.send(JSON.stringify(message));
      messageQueue.current.push(message);
      updateMetrics({ messagesSent: metrics.messagesSent + 1 });
      
      addLog('info', `Mensagem enviada: ${message.id}`);
      return true;
    } else {
      addLog('warning', 'Falha ao enviar mensagem: WebSocket não conectado');
      return false;
    }
  }, [addLog, metrics.messagesSent, updateMetrics]);

  // Connect to WebSocket with test monitoring
  const connectTestSocket = useCallback(() => {
    if (testWebSocket.current?.readyState === WebSocket.OPEN) {
      return;
    }

    const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws`;
    addLog('info', `Conectando WebSocket: ${wsUrl}`);
    
    try {
      const ws = new WebSocket(wsUrl);
      testWebSocket.current = ws;
      const connectStartTime = Date.now();

      ws.onopen = () => {
        const connectTime = Date.now() - connectStartTime;
        updateMetrics({ totalConnections: metrics.totalConnections + 1 });
        
        if (reconnectStartTime.current > 0) {
          const reconnectTime = Date.now() - reconnectStartTime.current;
          const avgReconnectTime = (metrics.averageReconnectTime * metrics.totalReconnections + reconnectTime) / (metrics.totalReconnections + 1);
          updateMetrics({ 
            totalReconnections: metrics.totalReconnections + 1,
            averageReconnectTime: avgReconnectTime
          });
          addLog('success', `Reconexão bem-sucedida em ${reconnectTime}ms`);
          reconnectStartTime.current = 0;
        } else {
          addLog('success', `Conexão estabelecida em ${connectTime}ms`);
        }
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          updateMetrics({ messagesReceived: metrics.messagesReceived + 1 });
          
          if (data.type === 'test_message_ack') {
            // Remove message from queue when acknowledged
            messageQueue.current = messageQueue.current.filter(msg => msg.id !== data.messageId);
            addLog('success', `Mensagem confirmada: ${data.messageId}`);
          } else {
            addLog('info', `Mensagem recebida: ${data.type}`);
          }
        } catch (error) {
          addLog('error', 'Erro ao processar mensagem recebida', error);
        }
      };

      ws.onerror = (error) => {
        updateMetrics({ totalFailures: metrics.totalFailures + 1 });
        addLog('error', 'Erro WebSocket', error);
      };

      ws.onclose = (event) => {
        addLog('warning', `Conexão fechada - Code: ${event.code}, Reason: ${event.reason}`);
        
        // Check for lost messages
        if (messageQueue.current.length > 0) {
          updateMetrics({ messagesLost: metrics.messagesLost + messageQueue.current.length });
          addLog('error', `${messageQueue.current.length} mensagens perdidas durante desconexão`);
        }

        // Auto-reconnect during test
        if (isTestRunning && event.code !== 1000) {
          reconnectStartTime.current = Date.now();
          addLog('info', 'Iniciando reconexão automática...');
          setTimeout(connectTestSocket, 2000);
        }
      };

    } catch (error) {
      updateMetrics({ totalFailures: metrics.totalFailures + 1 });
      addLog('error', 'Falha ao criar conexão WebSocket', error);
    }
  }, [addLog, isTestRunning, metrics, updateMetrics]);

  // Disconnect WebSocket
  const disconnectTestSocket = useCallback(() => {
    if (testWebSocket.current) {
      testWebSocket.current.close(1000, 'Test disconnect');
      testWebSocket.current = null;
    }
  }, []);

  // Force connection interruption
  const forceDisconnection = useCallback(() => {
    if (testWebSocket.current?.readyState === WebSocket.OPEN) {
      addLog('warning', 'Forçando desconexão para teste de reconexão...');
      // Simulate network interruption by closing socket abruptly
      testWebSocket.current.close(1006, 'Simulated network failure');
    }
  }, [addLog]);

  // Start comprehensive reconnection test
  const startReconnectionTest = useCallback(async () => {
    setIsTestRunning(true);
    testStartTime.current = Date.now();
    messageQueue.current = [];
    
    // Reset metrics
    setMetrics({
      totalConnections: 0,
      totalReconnections: 0,
      totalFailures: 0,
      averageReconnectTime: 0,
      messagesSent: 0,
      messagesReceived: 0,
      messagesLost: 0
    });

    addLog('info', '🚀 Iniciando teste de reconexão WebSocket...');
    
    // Phase 1: Initial Connection
    setTestPhase('Conectando inicialmente...');
    connectTestSocket();
    
    // Wait for initial connection
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Phase 2: Send messages continuously
    setTestPhase('Enviando mensagens de teste...');
    messageInterval.current = setInterval(sendTestMessage, 1000);
    
    // Wait a bit then force disconnection
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Phase 3: Force disconnection
    setTestPhase('Simulando perda de conexão...');
    forceDisconnection();
    
    // Wait for reconnection
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // Phase 4: Test message recovery
    setTestPhase('Testando recuperação de mensagens...');
    
    // Send more messages after reconnection
    for (let i = 0; i < 5; i++) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      sendTestMessage();
    }
    
    // Wait for final synchronization
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Phase 5: Complete test
    setTestPhase('Finalizando teste...');
    
    if (messageInterval.current) {
      clearInterval(messageInterval.current);
      messageInterval.current = null;
    }
    
    const testDuration = Date.now() - testStartTime.current;
    addLog('success', `✅ Teste concluído em ${Math.round(testDuration / 1000)}s`);
    
    setIsTestRunning(false);
    setTestPhase('idle');
    
    toast({
      title: "Teste de reconexão concluído",
      description: `Teste executado com sucesso em ${Math.round(testDuration / 1000)} segundos`,
    });
    
  }, [addLog, connectTestSocket, forceDisconnection, sendTestMessage, toast]);

  // Stop test
  const stopTest = useCallback(() => {
    setIsTestRunning(false);
    setTestPhase('idle');
    
    if (messageInterval.current) {
      clearInterval(messageInterval.current);
      messageInterval.current = null;
    }
    
    if (heartbeatInterval.current) {
      clearInterval(heartbeatInterval.current);
      heartbeatInterval.current = null;
    }
    
    disconnectTestSocket();
    addLog('info', 'Teste interrompido pelo usuário');
  }, [addLog, disconnectTestSocket]);

  // Clear logs
  const clearLogs = useCallback(() => {
    setTestLogs([]);
    addLog('info', 'Logs limpos');
  }, [addLog]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (messageInterval.current) clearInterval(messageInterval.current);
      if (heartbeatInterval.current) clearInterval(heartbeatInterval.current);
      disconnectTestSocket();
    };
  }, [disconnectTestSocket]);

  const getConnectionStatus = () => {
    const ws = testWebSocket.current;
    if (!ws) return { status: 'disconnected', color: 'destructive' };
    
    switch (ws.readyState) {
      case WebSocket.CONNECTING:
        return { status: 'connecting', color: 'secondary' };
      case WebSocket.OPEN:
        return { status: 'connected', color: 'default' };
      case WebSocket.CLOSING:
        return { status: 'closing', color: 'secondary' };
      case WebSocket.CLOSED:
        return { status: 'disconnected', color: 'destructive' };
      default:
        return { status: 'unknown', color: 'destructive' };
    }
  };

  const connectionStatus = getConnectionStatus();

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Teste de Reconexão WebSocket</h2>
        <div className="flex items-center gap-2">
          <Badge variant={connectionStatus.color as any}>
            {connectionStatus.status === 'connected' ? <Wifi className="h-3 w-3 mr-1" /> : <WifiOff className="h-3 w-3 mr-1" />}
            {connectionStatus.status.charAt(0).toUpperCase() + connectionStatus.status.slice(1)}
          </Badge>
          {isTestRunning && (
            <Badge variant="outline">
              <Activity className="h-3 w-3 mr-1 animate-pulse" />
              Teste em execução
            </Badge>
          )}
        </div>
      </div>

      {testPhase !== 'idle' && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4" />
              <span className="font-medium">Fase atual: {testPhase}</span>
            </div>
            <Progress value={isTestRunning ? 50 : 100} className="w-full" />
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{metrics.totalConnections}</div>
            <div className="text-sm text-muted-foreground">Conexões</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{metrics.totalReconnections}</div>
            <div className="text-sm text-muted-foreground">Reconexões</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">{metrics.messagesLost}</div>
            <div className="text-sm text-muted-foreground">Mensagens Perdidas</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-purple-600">{Math.round(metrics.averageReconnectTime)}ms</div>
            <div className="text-sm text-muted-foreground">Tempo Médio Reconexão</div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Control Panel */}
        <Card>
          <CardHeader>
            <CardTitle>Controles de Teste</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Button 
                onClick={startReconnectionTest}
                disabled={isTestRunning}
                className="flex-1"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Iniciar Teste
              </Button>
              <Button 
                onClick={stopTest}
                disabled={!isTestRunning}
                variant="outline"
                className="flex-1"
              >
                Parar
              </Button>
            </div>
            
            <div className="flex gap-2">
              <Button 
                onClick={connectTestSocket}
                disabled={isTestRunning || connectionStatus.status === 'connected'}
                variant="outline"
                className="flex-1"
              >
                <Wifi className="h-4 w-4 mr-2" />
                Conectar
              </Button>
              <Button 
                onClick={forceDisconnection}
                disabled={!isTestRunning || connectionStatus.status !== 'connected'}
                variant="outline"
                className="flex-1"
              >
                <WifiOff className="h-4 w-4 mr-2" />
                Desconectar
              </Button>
            </div>
            
            <Button 
              onClick={sendTestMessage}
              disabled={connectionStatus.status !== 'connected'}
              variant="outline"
              className="w-full"
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              Enviar Mensagem Teste
            </Button>
          </CardContent>
        </Card>

        {/* Logs Panel */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle>Logs de Teste</CardTitle>
            <Button onClick={clearLogs} variant="outline" size="sm">
              Limpar
            </Button>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-80">
              <div className="space-y-2">
                {testLogs.map((log) => (
                  <div key={log.id} className="flex items-start gap-2 text-sm">
                    <div className="mt-0.5">
                      {log.type === 'success' && <CheckCircle2 className="h-3 w-3 text-green-500" />}
                      {log.type === 'error' && <AlertTriangle className="h-3 w-3 text-red-500" />}
                      {log.type === 'warning' && <AlertTriangle className="h-3 w-3 text-yellow-500" />}
                      {log.type === 'info' && <Activity className="h-3 w-3 text-blue-500" />}
                    </div>
                    <div className="flex-1">
                      <div className="text-xs text-muted-foreground">
                        {log.timestamp.toLocaleTimeString()}
                      </div>
                      <div>{log.message}</div>
                      {log.details && (
                        <div className="text-xs text-muted-foreground font-mono mt-1">
                          {JSON.stringify(log.details, null, 2)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}