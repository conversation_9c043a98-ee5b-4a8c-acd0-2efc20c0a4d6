import { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Wifi, 
  WifiOff, 
  RotateCcw, 
  Play,
  Square,
  AlertTriangle,
  CheckCircle2,
  Clock,
  MessageSquare,
  Activity,
  Database,
  Users
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useWebSocketManagerEnhanced } from '@/hooks/useWebSocketManagerEnhanced';
import { WebSocketReconnectionTest } from '@/components/websocket-reconnection-test';

interface WhatsAppSession {
  id: string;
  phoneNumber: string;
  status: 'connected' | 'disconnected' | 'connecting';
  lastActivity: Date;
  messageCount: number;
}

interface SystemIntegrityTest {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  result?: string;
  duration?: number;
}

export default function WebSocketReconnectionTestPage() {
  const [whatsappSessions, setWhatsappSessions] = useState<WhatsAppSession[]>([]);
  const [systemTests, setSystemTests] = useState<SystemIntegrityTest[]>([
    { id: '1', name: 'Persistência de sessões WhatsApp', status: 'pending' },
    { id: '2', name: 'Sincronização de mensagens pendentes', status: 'pending' },
    { id: '3', name: 'Integridade do estado global', status: 'pending' },
    { id: '4', name: 'Recuperação automática de conexão', status: 'pending' },
    { id: '5', name: 'Prevenção de perda de mensagens', status: 'pending' }
  ]);
  
  const [testPhase, setTestPhase] = useState<string>('idle');
  const [testProgress, setTestProgress] = useState(0);
  const [isTestRunning, setIsTestRunning] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);
  
  const { toast } = useToast();
  const testStartTime = useRef<number>(0);

  // Enhanced WebSocket manager with comprehensive logging
  const wsManager = useWebSocketManagerEnhanced({
    maxReconnectAttempts: 8,
    reconnectInterval: 2000,
    heartbeatInterval: 15000,
    messageRetryAttempts: 3,
    enableMessageQueue: true,
    enableConnectionHistory: true,
    onConnect: () => {
      addTestResult('✅ WebSocket conectado com sucesso');
      updateSystemTest('4', 'passed', 'Conexão estabelecida');
    },
    onDisconnect: () => {
      addTestResult('⚠️ WebSocket desconectado');
    },
    onError: (error) => {
      addTestResult(`❌ Erro WebSocket: ${error}`);
    },
    onReconnectSuccess: (attempts, duration) => {
      addTestResult(`🔄 Reconexão bem-sucedida após ${attempts} tentativas em ${Math.round(duration/1000)}s`);
      updateSystemTest('4', 'passed', `Reconectado em ${Math.round(duration/1000)}s`);
    },
    onMessageQueued: (message) => {
      addTestResult(`📤 Mensagem enfileirada: ${message.id}`);
    },
    onMessageDequeued: (message) => {
      addTestResult(`📥 Mensagem processada: ${message.id}`);
    }
  });

  const addTestResult = (result: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [`[${timestamp}] ${result}`, ...prev.slice(0, 49)]);
  };

  const updateSystemTest = (id: string, status: SystemIntegrityTest['status'], result?: string, duration?: number) => {
    setSystemTests(prev => prev.map(test => 
      test.id === id ? { ...test, status, result, duration } : test
    ));
  };

  // Simulate WhatsApp sessions for testing
  useEffect(() => {
    const mockSessions: WhatsAppSession[] = [
      {
        id: 'session_1',
        phoneNumber: '+55 61 8108-4062',
        status: 'connected',
        lastActivity: new Date(),
        messageCount: 15
      },
      {
        id: 'session_2', 
        phoneNumber: '+55 11 9999-8888',
        status: 'disconnected',
        lastActivity: new Date(Date.now() - 300000),
        messageCount: 8
      }
    ];
    
    setWhatsappSessions(mockSessions);
  }, []);

  // Listen for WebSocket events to update session status
  useEffect(() => {
    const handleWhatsAppConnected = (event: CustomEvent) => {
      setWhatsappSessions(prev => prev.map(session => 
        session.phoneNumber === event.detail.phoneNumber 
          ? { ...session, status: 'connected', lastActivity: new Date() }
          : session
      ));
      updateSystemTest('1', 'passed', 'Sessão WhatsApp restaurada');
    };

    const handleWhatsAppDisconnected = (event: CustomEvent) => {
      setWhatsappSessions(prev => prev.map(session => 
        session.phoneNumber === event.detail.phoneNumber 
          ? { ...session, status: 'disconnected' }
          : session
      ));
    };

    const handleMessageReceived = (event: CustomEvent) => {
      setWhatsappSessions(prev => prev.map(session => 
        session.id === event.detail.sessionId
          ? { ...session, messageCount: session.messageCount + 1, lastActivity: new Date() }
          : session
      ));
      updateSystemTest('2', 'passed', 'Mensagem sincronizada');
    };

    const handleSessionRestored = (event: CustomEvent) => {
      addTestResult(`🔄 Sessão WhatsApp restaurada: ${event.detail.phoneNumber}`);
      updateSystemTest('1', 'passed', 'Sessões restauradas com sucesso');
    };

    window.addEventListener('websocket:whatsapp:connected', handleWhatsAppConnected as EventListener);
    window.addEventListener('websocket:whatsapp:disconnected', handleWhatsAppDisconnected as EventListener);
    window.addEventListener('websocket:message:received', handleMessageReceived as EventListener);
    window.addEventListener('websocket:session:restored', handleSessionRestored as EventListener);

    return () => {
      window.removeEventListener('websocket:whatsapp:connected', handleWhatsAppConnected as EventListener);
      window.removeEventListener('websocket:whatsapp:disconnected', handleWhatsAppDisconnected as EventListener);
      window.removeEventListener('websocket:message:received', handleMessageReceived as EventListener);
      window.removeEventListener('websocket:session:restored', handleSessionRestored as EventListener);
    };
  }, []);

  // Comprehensive reconnection test scenario
  const runComprehensiveTest = async () => {
    setIsTestRunning(true);
    testStartTime.current = Date.now();
    setTestProgress(0);
    setTestResults([]);
    
    // Reset all tests
    setSystemTests(prev => prev.map(test => ({ ...test, status: 'pending', result: undefined })));
    
    addTestResult('🚀 Iniciando teste abrangente de reconexão...');
    
    try {
      // Phase 1: Verify initial connection (10%)
      setTestPhase('Verificando conexão inicial...');
      setTestProgress(10);
      updateSystemTest('4', 'running');
      
      if (!wsManager.isConnected) {
        addTestResult('⚡ Estabelecendo conexão inicial...');
        wsManager.connect();
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
      
      if (wsManager.isConnected) {
        addTestResult('✅ Conexão inicial estabelecida');
        updateSystemTest('4', 'passed', 'Conexão ativa');
      } else {
        throw new Error('Falha na conexão inicial');
      }

      // Phase 2: Test message queuing (25%)
      setTestPhase('Testando sistema de filas de mensagens...');
      setTestProgress(25);
      updateSystemTest('5', 'running');
      
      // Send test messages
      for (let i = 1; i <= 3; i++) {
        const testMessage = {
          type: 'test_message',
          content: `Mensagem de teste ${i}`,
          timestamp: new Date().toISOString()
        };
        
        const sent = wsManager.sendMessage(testMessage);
        addTestResult(`📤 Mensagem ${i} ${sent ? 'enviada' : 'enfileirada'}`);
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      updateSystemTest('5', 'passed', '3 mensagens processadas');

      // Phase 3: Simulate connection interruption (40%)
      setTestPhase('Simulando interrupção de conexão...');
      setTestProgress(40);
      updateSystemTest('4', 'running');
      
      addTestResult('🔌 Forçando desconexão para teste...');
      wsManager.disconnect();
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Phase 4: Send messages during disconnection (55%)
      setTestPhase('Enviando mensagens durante desconexão...');
      setTestProgress(55);
      updateSystemTest('5', 'running');
      
      for (let i = 4; i <= 6; i++) {
        const queuedMessage = {
          type: 'queued_message',
          content: `Mensagem enfileirada ${i}`,
          timestamp: new Date().toISOString()
        };
        
        wsManager.sendMessage(queuedMessage);
        addTestResult(`📋 Mensagem ${i} adicionada à fila`);
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // Phase 5: Wait for automatic reconnection (70%)
      setTestPhase('Aguardando reconexão automática...');
      setTestProgress(70);
      
      addTestResult('⏳ Aguardando reconexão automática...');
      
      // Wait up to 30 seconds for reconnection
      let reconnectWaitTime = 0;
      const maxWaitTime = 30000;
      
      while (!wsManager.isConnected && reconnectWaitTime < maxWaitTime) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        reconnectWaitTime += 1000;
        
        if (reconnectWaitTime % 5000 === 0) {
          addTestResult(`⏱️ Aguardando reconexão... ${reconnectWaitTime/1000}s`);
        }
      }

      if (wsManager.isConnected) {
        addTestResult('🔄 Reconexão automática bem-sucedida!');
        updateSystemTest('4', 'passed', `Reconectado em ${reconnectWaitTime/1000}s`);
      } else {
        throw new Error('Falha na reconexão automática');
      }

      // Phase 6: Verify message queue processing (85%)
      setTestPhase('Verificando processamento da fila...');
      setTestProgress(85);
      
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const stats = wsManager.getConnectionStats();
      addTestResult(`📊 Mensagens na fila: ${stats.queuedMessages}`);
      
      if (stats.queuedMessages === 0) {
        updateSystemTest('5', 'passed', 'Todas as mensagens processadas');
      } else {
        updateSystemTest('5', 'failed', `${stats.queuedMessages} mensagens não processadas`);
      }

      // Phase 7: Test session integrity (95%)
      setTestPhase('Verificando integridade das sessões...');
      setTestProgress(95);
      updateSystemTest('1', 'running');
      
      // Simulate session restoration check
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const activeSessions = whatsappSessions.filter(s => s.status === 'connected');
      if (activeSessions.length > 0) {
        updateSystemTest('1', 'passed', `${activeSessions.length} sessões ativas`);
        updateSystemTest('3', 'passed', 'Estado global íntegro');
      } else {
        updateSystemTest('1', 'failed', 'Nenhuma sessão ativa encontrada');
        updateSystemTest('3', 'failed', 'Estado global comprometido');
      }

      // Phase 8: Complete test (100%)
      setTestPhase('Finalizando teste...');
      setTestProgress(100);
      
      const testDuration = Date.now() - testStartTime.current;
      const passedTests = systemTests.filter(t => t.status === 'passed').length;
      const totalTests = systemTests.length;
      
      addTestResult(`🎯 Teste concluído: ${passedTests}/${totalTests} testes passaram`);
      addTestResult(`⏱️ Duração total: ${Math.round(testDuration / 1000)}s`);
      
      if (passedTests === totalTests) {
        toast({
          title: "Teste de reconexão concluído com sucesso",
          description: `Todos os ${totalTests} testes passaram em ${Math.round(testDuration / 1000)}s`,
        });
      } else {
        toast({
          title: "Teste concluído com problemas",
          description: `${passedTests}/${totalTests} testes passaram`,
          variant: "destructive"
        });
      }

    } catch (error) {
      addTestResult(`❌ Erro durante teste: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      
      toast({
        title: "Falha no teste de reconexão",
        description: error instanceof Error ? error.message : "Erro desconhecido",
        variant: "destructive"
      });
    } finally {
      setIsTestRunning(false);
      setTestPhase('idle');
    }
  };

  const stopTest = () => {
    setIsTestRunning(false);
    setTestPhase('idle');
    addTestResult('⏹️ Teste interrompido pelo usuário');
  };

  const clearResults = () => {
    setTestResults([]);
    setSystemTests(prev => prev.map(test => ({ ...test, status: 'pending', result: undefined })));
    addTestResult('🧹 Resultados limpos');
  };

  const getStatusIcon = (status: SystemIntegrityTest['status']) => {
    switch (status) {
      case 'passed': return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'failed': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'running': return <Activity className="h-4 w-4 text-blue-500 animate-pulse" />;
      default: return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getSessionStatusBadge = (status: WhatsAppSession['status']) => {
    switch (status) {
      case 'connected':
        return <Badge variant="default" className="bg-green-500"><Wifi className="h-3 w-3 mr-1" />Conectado</Badge>;
      case 'connecting':
        return <Badge variant="secondary"><Activity className="h-3 w-3 mr-1 animate-pulse" />Conectando</Badge>;
      default:
        return <Badge variant="destructive"><WifiOff className="h-3 w-3 mr-1" />Desconectado</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Teste de Reconexão WebSocket</h1>
            <p className="text-muted-foreground">Simulação e validação do comportamento de reconexão do sistema</p>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant={wsManager.isConnected ? "default" : "destructive"}>
              {wsManager.isConnected ? <Wifi className="h-3 w-3 mr-1" /> : <WifiOff className="h-3 w-3 mr-1" />}
              {wsManager.connectionState}
            </Badge>
            {wsManager.reconnectAttempts > 0 && (
              <Badge variant="outline">
                <RotateCcw className="h-3 w-3 mr-1" />
                Tentativas: {wsManager.reconnectAttempts}
              </Badge>
            )}
          </div>
        </div>

        {testPhase !== 'idle' && (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <Activity className="h-4 w-4 animate-pulse" />
                <span className="font-medium">Fase: {testPhase}</span>
              </div>
              <Progress value={testProgress} className="w-full" />
            </CardContent>
          </Card>
        )}

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Visão Geral</TabsTrigger>
            <TabsTrigger value="sessions">Sessões WhatsApp</TabsTrigger>
            <TabsTrigger value="tests">Testes Detalhados</TabsTrigger>
            <TabsTrigger value="advanced">Teste Avançado</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-blue-600">{wsManager.isConnected ? 1 : 0}</div>
                  <div className="text-sm text-muted-foreground">Conexões Ativas</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-green-600">{wsManager.messageQueue.length}</div>
                  <div className="text-sm text-muted-foreground">Mensagens na Fila</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-yellow-600">{wsManager.reconnectAttempts}</div>
                  <div className="text-sm text-muted-foreground">Tentativas de Reconexão</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-purple-600">{whatsappSessions.filter(s => s.status === 'connected').length}</div>
                  <div className="text-sm text-muted-foreground">Sessões WhatsApp</div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Controles de Teste</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <Button 
                      onClick={runComprehensiveTest}
                      disabled={isTestRunning}
                      className="flex-1"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Executar Teste Completo
                    </Button>
                    <Button 
                      onClick={stopTest}
                      disabled={!isTestRunning}
                      variant="outline"
                    >
                      <Square className="h-4 w-4 mr-2" />
                      Parar
                    </Button>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button 
                      onClick={wsManager.reconnect}
                      disabled={isTestRunning}
                      variant="outline"
                      className="flex-1"
                    >
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Forçar Reconexão
                    </Button>
                    <Button 
                      onClick={clearResults}
                      variant="outline"
                      className="flex-1"
                    >
                      Limpar Resultados
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Testes de Integridade do Sistema</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {systemTests.map((test) => (
                      <div key={test.id} className="flex items-center gap-3">
                        {getStatusIcon(test.status)}
                        <div className="flex-1">
                          <div className="font-medium">{test.name}</div>
                          {test.result && (
                            <div className="text-sm text-muted-foreground">{test.result}</div>
                          )}
                        </div>
                        {test.duration && (
                          <div className="text-xs text-muted-foreground">{test.duration}ms</div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Log de Resultados em Tempo Real</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-80">
                  <div className="space-y-1">
                    {testResults.map((result, index) => (
                      <div key={index} className="text-sm font-mono">
                        {result}
                      </div>
                    ))}
                    {testResults.length === 0 && (
                      <div className="text-muted-foreground text-center py-8">
                        Nenhum resultado ainda. Execute um teste para ver os logs.
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="sessions" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Sessões WhatsApp Ativas
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {whatsappSessions.map((session) => (
                    <div key={session.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <MessageSquare className="h-5 w-5 text-green-600" />
                        <div>
                          <div className="font-medium">{session.phoneNumber}</div>
                          <div className="text-sm text-muted-foreground">
                            Última atividade: {session.lastActivity.toLocaleString()}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="text-right">
                          <div className="text-sm font-medium">{session.messageCount} mensagens</div>
                          <div className="text-xs text-muted-foreground">ID: {session.id}</div>
                        </div>
                        {getSessionStatusBadge(session.status)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tests" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Detalhes dos Testes de Integridade</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {systemTests.map((test) => (
                    <div key={test.id} className="border rounded-lg p-4">
                      <div className="flex items-center gap-3 mb-3">
                        {getStatusIcon(test.status)}
                        <h3 className="font-semibold">{test.name}</h3>
                      </div>
                      
                      <div className="space-y-2 text-sm">
                        <div>
                          <strong>Objetivo:</strong> 
                          {test.id === '1' && ' Verificar se as sessões do WhatsApp são restauradas corretamente após reconexão'}
                          {test.id === '2' && ' Garantir que mensagens pendentes são sincronizadas sem perda'}
                          {test.id === '3' && ' Validar que o estado global permanece íntegro após reconexão'}
                          {test.id === '4' && ' Testar a capacidade de reconexão automática do sistema'}
                          {test.id === '5' && ' Assegurar que não há perda de mensagens durante reconexão'}
                        </div>
                        
                        {test.result && (
                          <div>
                            <strong>Resultado:</strong> {test.result}
                          </div>
                        )}
                        
                        {test.duration && (
                          <div>
                            <strong>Duração:</strong> {test.duration}ms
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-6">
            <WebSocketReconnectionTest />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}