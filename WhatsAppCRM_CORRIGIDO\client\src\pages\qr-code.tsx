import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useLocation } from 'wouter';
import { ArrowLeft, Smartphone, CheckCircle } from 'lucide-react';

export default function QRCode() {
  const [, setLocation] = useLocation();
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'failed'>('connecting');

  // Fetch QR code and connection status
  const { data: qrData, isLoading } = useQuery({
    queryKey: ["/api/whatsapp/qr"],
    refetchInterval: 2000,
  });

  const { data: status } = useQuery({
    queryKey: ["/api/whatsapp/status"],
    refetchInterval: 1000,
  });

  // Check if connection is established
  useEffect(() => {
    if ((status as any)?.connected) {
      setConnectionStatus('connected');
      setTimeout(() => {
        setLocation('/messages');
      }, 2000);
    }
  }, [status, setLocation]);

  // Redirect immediately if already connected
  useEffect(() => {
    if ((qrData as any)?.connected || (status as any)?.connected) {
      setLocation('/messages');
    }
  }, [qrData, status, setLocation]);

  const handleBack = () => {
    setLocation('/messages-whatsapp');
  };

  if (connectionStatus === 'connected') {
    return (
      <div className="min-h-screen bg-[#f0f2f5] flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4 text-center">
          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">
            Conectado com sucesso!
          </h2>
          <p className="text-gray-600 mb-4">
            Redirecionando para as mensagens...
          </p>
          <div className="w-8 h-8 border-2 border-green-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#f0f2f5]">
      {/* Header */}
      <div className="bg-[#00a884] text-white p-4">
        <div className="flex items-center gap-4">
          <button
            onClick={handleBack}
            className="p-2 hover:bg-white/10 rounded-full transition-colors"
          >
            <ArrowLeft className="w-6 h-6" />
          </button>
          <h1 className="text-xl font-medium">Conectar WhatsApp</h1>
        </div>
      </div>

      {/* Content */}
      <div className="flex flex-col lg:flex-row min-h-[calc(100vh-80px)]">
        {/* QR Code Section */}
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
            <div className="mb-6">
              <div className="w-16 h-16 bg-[#00a884] rounded-full flex items-center justify-center mx-auto mb-4">
                <Smartphone className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-2xl font-semibold text-gray-800 mb-2">
                Escaneie o código QR
              </h2>
              <p className="text-gray-600">
                Use seu telefone para escanear o código QR e conectar
              </p>
            </div>

            {/* QR Code Display */}
            <div className="bg-white p-4 rounded-lg border-2 border-gray-200 mb-6">
              {isLoading ? (
                <div className="w-64 h-64 flex items-center justify-center">
                  <div className="w-8 h-8 border-2 border-[#00a884] border-t-transparent rounded-full animate-spin"></div>
                </div>
              ) : (qrData as any)?.qrCode ? (
                <img 
                  src={(qrData as any).qrCode} 
                  alt="QR Code" 
                  className="w-64 h-64 mx-auto"
                />
              ) : (
                <div className="w-64 h-64 flex items-center justify-center text-gray-500">
                  <p>Gerando código QR...</p>
                </div>
              )}
            </div>

            <div className="text-sm text-gray-600 space-y-2">
              <p>• Abra o WhatsApp no seu telefone</p>
              <p>• Toque em Menu ou Configurações</p>
              <p>• Toque em Aparelhos conectados</p>
              <p>• Toque em Conectar um aparelho</p>
              <p>• Aponte seu telefone para esta tela</p>
            </div>
          </div>
        </div>

        {/* Info Section */}
        <div className="lg:w-96 bg-white p-8 border-l border-gray-200">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">
                WhatsApp Web
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Envie e receba mensagens diretamente do seu computador. 
                Mantenha-se conectado mesmo quando seu telefone está em outro lugar.
              </p>
            </div>

            <div>
              <h4 className="font-medium text-gray-800 mb-2">Recursos:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Sincronização em tempo real</li>
                <li>• Envio de arquivos e mídia</li>
                <li>• Notificações no desktop</li>
                <li>• Histórico completo de conversas</li>
              </ul>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-1">Importante:</h4>
              <p className="text-sm text-yellow-700">
                Mantenha seu telefone conectado à internet para que o WhatsApp Web funcione.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}